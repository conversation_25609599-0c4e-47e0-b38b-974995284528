const express = require('express');
const puppeteer = require('puppeteer');
const cors = require('cors');

const app = express();
const PORT = 3001;

let browser = null;
let page = null;

// Enable CORS for all routes
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['*']
}));

// Initialize Puppeteer browser
async function initBrowser() {
  try {
    console.log('Launching Puppeteer browser...');
    browser = await puppeteer.launch({
      headless: 'new',
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu'
      ]
    });
    
    page = await browser.newPage();
    
    // Set viewport to a standard desktop size
    await page.setViewport({ width: 1920, height: 1080 });
    
    // Set user agent to avoid detection
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
    
    console.log('Puppeteer browser initialized successfully');
  } catch (error) {
    console.error('Failed to initialize browser:', error);
  }
}

// Route to serve pump.fun content
app.get('/pump-fun', async (req, res) => {
  try {
    console.log('Fetching pump.fun content with Puppeteer...');
    
    if (!page) {
      await initBrowser();
    }
    
    // Navigate to pump.fun
    const url = 'https://pump.fun/coin/9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump';
    console.log(`Navigating to: ${url}`);

    await page.goto(url, {
      waitUntil: 'networkidle2',
      timeout: 30000
    });

    // Wait for initial content to load
    await page.waitForTimeout(2000);

    // Try to close any modal dialogs that might be blocking the content
    try {
      // Look for "I'm ready to pump" button and click it to close the modal
      const readyButton = await page.$('[data-test-id="how-it-works-button"]');
      if (readyButton) {
        console.log('Closing "how it works" modal...');
        await readyButton.click();
        await page.waitForTimeout(1000);
      }
    } catch (error) {
      console.log('No modal to close or error closing modal:', error.message);
    }

    // Wait for main content to be visible
    await page.waitForTimeout(2000);
    
    // Get the full HTML content
    const content = await page.content();
    
    // Modify the HTML to work in our iframe
    const modifiedContent = content
      // Remove any X-Frame-Options meta tags
      .replace(/<meta[^>]*http-equiv=["']X-Frame-Options["'][^>]*>/gi, '')
      // Remove CSP meta tags
      .replace(/<meta[^>]*http-equiv=["']Content-Security-Policy["'][^>]*>/gi, '')
      // Add our own meta tags to allow iframe embedding
      .replace('<head>', `<head>
        <meta http-equiv="X-Frame-Options" content="ALLOWALL">
        <style>
          body { margin: 0; padding: 0; overflow-x: hidden; }
        </style>`);
    
    // Set headers to allow iframe embedding
    res.setHeader('X-Frame-Options', 'ALLOWALL');
    res.setHeader('Content-Security-Policy', '');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', '*');
    res.setHeader('Access-Control-Allow-Headers', '*');
    res.setHeader('Content-Type', 'text/html; charset=utf-8');
    
    console.log('Successfully fetched and modified pump.fun content');
    res.send(modifiedContent);
    
  } catch (error) {
    console.error('Error fetching pump.fun content:', error);
    res.status(500).json({ 
      error: 'Failed to fetch pump.fun content', 
      message: error.message 
    });
  }
});

// Route to get a screenshot of pump.fun
app.get('/pump-fun-screenshot', async (req, res) => {
  try {
    console.log('Taking screenshot of pump.fun...');
    
    if (!page) {
      await initBrowser();
    }
    
    const url = 'https://pump.fun/coin/9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump';
    await page.goto(url, { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });
    
    await page.waitForTimeout(3000);
    
    const screenshot = await page.screenshot({ 
      type: 'png',
      fullPage: false
    });
    
    res.setHeader('Content-Type', 'image/png');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.send(screenshot);
    
  } catch (error) {
    console.error('Error taking screenshot:', error);
    res.status(500).json({ 
      error: 'Failed to take screenshot', 
      message: error.message 
    });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'Puppeteer proxy server is running',
    browser: browser ? 'connected' : 'disconnected'
  });
});

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('Shutting down gracefully...');
  if (browser) {
    await browser.close();
  }
  process.exit(0);
});

// Start the server
app.listen(PORT, async () => {
  console.log(`Puppeteer proxy server running on http://localhost:${PORT}`);
  console.log(`Pump.fun content available at: http://localhost:${PORT}/pump-fun`);
  console.log(`Pump.fun screenshot available at: http://localhost:${PORT}/pump-fun-screenshot`);
  
  // Initialize browser on startup
  await initBrowser();
});
