{"version": 3, "file": "install.js", "sourceRoot": "", "sources": ["../../../../src/node/install.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAEH,kDAM6B;AAE7B,uEAAyE;AAEzE,gEAAwD;AAExD;;GAEG;AACH,MAAM,iBAAiB,GAAG;IACxB,MAAM,EAAE,QAAQ;IAChB,OAAO,EAAE,iBAAiB;CAClB,CAAC;AAEX;;GAEG;AACI,KAAK,UAAU,eAAe;IACnC,aAAa,EAAE,CAAC;IAEhB,MAAM,aAAa,GAAG,IAAA,sCAAgB,GAAE,CAAC;IACzC,IAAI,aAAa,CAAC,YAAY,EAAE,CAAC;QAC/B,WAAW,CAAC,mDAAmD,CAAC,CAAC;QACjE,OAAO;IACT,CAAC;IAED,MAAM,eAAe,GAAG,aAAa,CAAC,eAAe,CAAC;IAEtD,MAAM,QAAQ,GAAG,IAAA,gCAAqB,GAAE,CAAC;IACzC,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;IAC5D,CAAC;IAED,MAAM,OAAO,GAAG,aAAa,CAAC,cAAe,CAAC;IAC9C,MAAM,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAE1C,MAAM,iBAAiB,GACrB,aAAa,CAAC,eAAe,IAAI,kCAAmB,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC;IAC5E,MAAM,sBAAsB,GAC1B,aAAa,CAAC,eAAe;QAC7B,kCAAmB,CAAC,uBAAuB,CAAC;QAC5C,QAAQ,CAAC;IAEX,4DAA4D;IAC5D,MAAM,QAAQ,GAAG,aAAa,CAAC,YAAY,IAAI,aAAa,CAAC,cAAe,CAAC;IAE7E,IAAI,CAAC;QACH,MAAM,gBAAgB,GAAG,EAAE,CAAC;QAE5B,IAAI,aAAa,CAAC,kBAAkB,EAAE,CAAC;YACrC,WAAW,CAAC,kDAAkD,CAAC,CAAC;QAClE,CAAC;aAAM,CAAC;YACN,MAAM,OAAO,GAAG,MAAM,IAAA,yBAAc,EAClC,OAAO,EACP,QAAQ,EACR,iBAAiB,CAClB,CAAC;YACF,gBAAgB,CAAC,IAAI,CACnB,IAAA,kBAAO,EAAC;gBACN,OAAO;gBACP,QAAQ;gBACR,QAAQ;gBACR,OAAO;gBACP,wBAAwB,EAAE,IAAA,+BAAoB,EAAC,OAAO,EAAE,OAAO,CAAC;gBAChE,OAAO,EAAE,eAAe;aACzB,CAAC;iBACC,IAAI,CAAC,MAAM,CAAC,EAAE;gBACb,WAAW,CACT,GAAG,iBAAiB,CAAC,OAAO,CAAC,KAAK,MAAM,CAAC,OAAO,mBAAmB,MAAM,CAAC,IAAI,EAAE,CACjF,CAAC;YACJ,CAAC,CAAC;iBACD,KAAK,CAAC,KAAK,CAAC,EAAE;gBACb,MAAM,IAAI,KAAK,CACb,2BAA2B,iBAAiB,CAAC,OAAO,CAAC,KAAK,OAAO,gEAAgE,EACjI;oBACE,KAAK,EAAE,KAAK;iBACb,CACF,CAAC;YACJ,CAAC,CAAC,CACL,CAAC;QACJ,CAAC;QAED,IAAI,OAAO,KAAK,kBAAO,CAAC,MAAM,EAAE,CAAC;YAC/B,IAAI,aAAa,CAAC,+BAA+B,EAAE,CAAC;gBAClD,WAAW,CAAC,kDAAkD,CAAC,CAAC;YAClE,CAAC;iBAAM,CAAC;gBACN,MAAM,YAAY,GAAG,MAAM,IAAA,yBAAc,EACvC,OAAO,EACP,QAAQ,EACR,sBAAsB,CACvB,CAAC;gBAEF,gBAAgB,CAAC,IAAI,CACnB,IAAA,kBAAO,EAAC;oBACN,OAAO,EAAE,kBAAO,CAAC,mBAAmB;oBACpC,QAAQ;oBACR,QAAQ;oBACR,OAAO,EAAE,YAAY;oBACrB,wBAAwB,EAAE,IAAA,+BAAoB,EAC5C,OAAO,EACP,YAAY,CACb;oBACD,OAAO,EAAE,eAAe;iBACzB,CAAC;qBACC,IAAI,CAAC,MAAM,CAAC,EAAE;oBACb,WAAW,CACT,GAAG,kBAAO,CAAC,mBAAmB,KAAK,MAAM,CAAC,OAAO,mBAAmB,MAAM,CAAC,IAAI,EAAE,CAClF,CAAC;gBACJ,CAAC,CAAC;qBACD,KAAK,CAAC,KAAK,CAAC,EAAE;oBACb,MAAM,IAAI,KAAK,CACb,2BAA2B,kBAAO,CAAC,mBAAmB,KAAK,YAAY,gEAAgE,EACvI;wBACE,KAAK,EAAE,KAAK;qBACb,CACF,CAAC;gBACJ,CAAC,CAAC,CACL,CAAC;YACJ,CAAC;QACH,CAAC;QAED,MAAM,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IACtC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACrB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC;AA7GD,0CA6GC;AAED,SAAS,gBAAgB,CAAC,OAAiB;IACzC,QAAQ,OAAO,EAAE,CAAC;QAChB,KAAK,QAAQ;YACX,OAAO,kBAAO,CAAC,MAAM,CAAC;QACxB,KAAK,SAAS;YACZ,OAAO,kBAAO,CAAC,OAAO,CAAC;IAC3B,CAAC;IACD,OAAO,kBAAO,CAAC,MAAM,CAAC;AACxB,CAAC;AAED;;GAEG;AACH,SAAS,WAAW,CAAC,UAAmB;IACtC,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,IAAI,EAAE,CAAC;IAC1D,MAAM,eAAe,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IAE3E,sCAAsC;IACtC,IAAI,CAAC,eAAe,EAAE,CAAC;QACrB,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC1B,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,aAAa;IACpB,8EAA8E;IAC9E,MAAM,eAAe,GACnB,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IAC3E,MAAM,cAAc,GAClB,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IAC1E,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;IAExD,IAAI,eAAe,EAAE,CAAC;QACpB,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,eAAe,CAAC;IAC/C,CAAC;IACD,IAAI,cAAc,EAAE,CAAC;QACnB,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,cAAc,CAAC;IAC7C,CAAC;IACD,IAAI,YAAY,EAAE,CAAC;QACjB,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,YAAY,CAAC;IACzC,CAAC;AACH,CAAC"}