{"version": 3, "file": "Frame.js", "sourceRoot": "", "sources": ["../../../../src/bidi/Frame.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,iFAAmE;AAEnE,4DASwC;AAGxC,8CAKyB;AAEzB,mDAAyD;AAGzD,+CAK2B;AAC3B,qDAA6C;AAC7C,yDAAoD;AAGpD,6DAAwD;AAExD,iDAIwB;AAExB,6CAKsB;AAEtB;;;GAGG;IACU,SAAS;sBAAS,gBAAK;;;;;iBAAvB,SAAU,SAAQ,WAAK;;;YAkElC,+JAAe,IAAI,6DAsClB;YAGD,iLAAe,UAAU,6DAmCxB;YAOD,sMAAe,iBAAiB,6DAmD/B;;;QAvMD,KAAK,iEAAW;QAChB,QAAQ,CAAkB;QAC1B,gBAAgB,CAAkB;QAClC,cAAc,GAAG,sBAAQ,CAAC,MAAM,EAAS,CAAC;QAC1C,SAAS,GAAG,KAAK,CAAC;QAClB,SAAS,CAAe;QACf,GAAG,CAAS;QAErB,YACE,IAAc,EACd,OAAwB,EACxB,eAAgC,EAChC,QAAwB;YAExB,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;YAClB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;YACxB,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;YACxC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,IAAI,CAAC,SAAS,GAAG,QAAQ,IAAI,SAAS,CAAC;YAEvC,IAAI,CAAC,SAAS,GAAG;gBACf,CAAC,yBAAY,CAAC,EAAE,IAAI,oBAAO,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,eAAe,CAAC;gBACtE,CAAC,8BAAiB,CAAC,EAAE,IAAI,oBAAO,CAC9B,4BAAkB,EAClB,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAAE,EAC/B,eAAe,CAChB;aACF,CAAC;QACJ,CAAC;QAED,IAAa,MAAM;YACjB,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC;QACnC,CAAC;QAEQ,SAAS;YAChB,OAAO,IAAI,CAAC,SAAS,CAAC,yBAAY,CAAC,CAAC;QACtC,CAAC;QAEQ,aAAa;YACpB,OAAO,IAAI,CAAC,SAAS,CAAC,8BAAiB,CAAC,CAAC;QAC3C,CAAC;QAEQ,IAAI;YACX,OAAO,IAAI,CAAC,KAAK,CAAC;QACpB,CAAC;QAEQ,UAAU;YACjB,MAAM,IAAI,gCAAoB,EAAE,CAAC;QACnC,CAAC;QAEQ,GAAG;YACV,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;QAC3B,CAAC;QAEQ,WAAW;YAClB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC;QAChD,CAAC;QAEQ,WAAW;YAClB,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAClD,CAAC;QAGQ,KAAK,CAAC,IAAI,CACjB,GAAW,EACX,UAAuB,EAAE;YAEzB,MAAM,EACJ,SAAS,GAAG,MAAM,EAClB,OAAO,EAAE,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,GACxD,GAAG,OAAO,CAAC;YAEZ,MAAM,CAAC,SAAS,EAAE,WAAW,CAAC,GAAG,IAAA,oCAAqB,EAAC,SAAS,CAAC,CAAC;YAElE,MAAM,OAAO,GAAG,IAAA,aAAG,EACjB,IAAA,cAAI,EACF,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,0BAA0B,EAAE;gBACxD,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE;gBACzB,GAAG;gBACH,IAAI,EAAE,SAAS;aAChB,CAAC,CACH,EACD,GAAG,CAAC,WAAW,KAAK,IAAI;gBACtB,CAAC,CAAC;oBACE,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC;wBAC7B,OAAO,EAAE,EAAE;wBACX,WAAW,EAAE,WAAW,KAAK,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnD,QAAQ,EAAE,2BAAiB;qBAC5B,CAAC;iBACH;gBACH,CAAC,CAAC,EAAE,CAAC,CACR,CAAC,IAAI,CACJ,IAAA,aAAG,EAAC,CAAC,CAAC,EAAC,MAAM,EAAC,CAAC,EAAE,EAAE;gBACjB,OAAO,MAAM,CAAC;YAChB,CAAC,CAAC,EACF,IAAA,kBAAQ,EAAC,IAAA,iBAAO,EAAC,EAAE,CAAC,EAAE,IAAA,cAAI,EAAC,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC,CAAC,EAC/D,IAAA,qCAAsB,EAAC,GAAG,EAAE,EAAE,CAAC,CAChC,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,IAAA,wBAAc,EAAC,OAAO,CAAC,CAAC;YAC7C,OAAO,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAC7D,CAAC;QAGQ,KAAK,CAAC,UAAU,CACvB,IAAY,EACZ,UAA0B,EAAE;YAE5B,MAAM,EACJ,SAAS,GAAG,MAAM,EAClB,OAAO,EAAE,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,GACxD,GAAG,OAAO,CAAC;YAEZ,MAAM,CAAC,SAAS,EAAE,WAAW,CAAC,GAAG,IAAA,oCAAqB,EAAC,SAAS,CAAC,CAAC;YAElE,MAAM,OAAO,GAAG,IAAA,aAAG,EACjB,IAAA,kBAAQ,EAAC;gBACP,IAAA,0BAAgB,EAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,IAAA,eAAK,GAAE,CAAC;gBACxD,IAAA,cAAI,EAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;aACjC,CAAC,CAAC,IAAI,CACL,IAAA,aAAG,EAAC,GAAG,EAAE;gBACP,OAAO,IAAI,CAAC;YACd,CAAC,CAAC,CACH,EACD,GAAG,CAAC,WAAW,KAAK,IAAI;gBACtB,CAAC,CAAC;oBACE,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC;wBAC7B,OAAO,EAAE,EAAE;wBACX,WAAW,EAAE,WAAW,KAAK,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnD,QAAQ,EAAE,2BAAiB;qBAC5B,CAAC;iBACH;gBACH,CAAC,CAAC,EAAE,CAAC,CACR,CAAC,IAAI,CACJ,IAAA,kBAAQ,EAAC,IAAA,iBAAO,EAAC,EAAE,CAAC,EAAE,IAAA,cAAI,EAAC,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC,CAAC,EAC/D,IAAA,qCAAsB,EAAC,YAAY,EAAE,EAAE,CAAC,CACzC,CAAC;YAEF,MAAM,IAAA,wBAAc,EAAC,OAAO,CAAC,CAAC;QAChC,CAAC;QAED,OAAO;YACL,OAAO,IAAI,CAAC,QAAQ,CAAC;QACvB,CAAC;QAGQ,KAAK,CAAC,iBAAiB,CAC9B,UAA0B,EAAE;YAE5B,MAAM,EACJ,SAAS,GAAG,MAAM,EAClB,OAAO,EAAE,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,GACxD,GAAG,OAAO,CAAC;YAEZ,MAAM,CAAC,cAAc,EAAE,WAAW,CAAC,GAAG,IAAA,oCAAqB,EAAC,SAAS,CAAC,CAAC;YAEvE,MAAM,WAAW,GAAG,IAAA,eAAK,EACvB,IAAA,kBAAQ,EAAC;gBACP,IAAA,0BAAgB,EACd,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,UAAU,CAAC,iBAAiB,CAC/D,CAAC,IAAI,CAAC,IAAA,eAAK,GAAE,CAAC;gBACf,IAAA,0BAAgB,EAAC,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC,IAAI,CAAC,IAAA,eAAK,GAAE,CAAC;aAC9D,CAAC,EACF,IAAA,0BAAgB,EACd,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,UAAU,CAAC,iBAAiB,CAC/D,CACF,CAAC,IAAI,CACJ,IAAA,aAAG,EAAC,MAAM,CAAC,EAAE;gBACX,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC1B,OAAO,EAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,EAAC,CAAC;gBAC7B,CAAC;gBACD,OAAO,EAAC,MAAM,EAAC,CAAC;YAClB,CAAC,CAAC,CACH,CAAC;YAEF,MAAM,OAAO,GAAG,IAAA,aAAG,EACjB,WAAW,EACX,GAAG,CAAC,WAAW,KAAK,IAAI;gBACtB,CAAC,CAAC;oBACE,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC;wBAC7B,OAAO,EAAE,EAAE;wBACX,WAAW,EAAE,WAAW,KAAK,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnD,QAAQ,EAAE,2BAAiB;qBAC5B,CAAC;iBACH;gBACH,CAAC,CAAC,EAAE,CAAC,CACR,CAAC,IAAI,CACJ,IAAA,aAAG,EAAC,CAAC,CAAC,EAAC,MAAM,EAAC,CAAC,EAAE,EAAE;gBACjB,OAAO,MAAM,CAAC;YAChB,CAAC,CAAC,EACF,IAAA,kBAAQ,EAAC,IAAA,iBAAO,EAAC,EAAE,CAAC,EAAE,IAAA,cAAI,EAAC,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC,CAAC,CAChE,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,IAAA,wBAAc,EAAC,OAAO,CAAC,CAAC;YAC7C,OAAO,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAC7D,CAAC;QAEQ,mBAAmB;YAC1B,MAAM,IAAI,gCAAoB,EAAE,CAAC;QACnC,CAAC;QAED,IAAa,QAAQ;YACnB,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB,CAAC;QAED,sBAjJC,0BAAe,6BAyCf,0BAAe,oCA0Cf,0BAAe,GA8Df,6BAAa,EAAC;YACb,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,OAAO;YACT,CAAC;YACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;YACxD,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;YACxB,IAAI,CAAC,SAAS,CAAC,yBAAY,CAAC,CAAC,6BAAa,CAAC,EAAE,CAAC;YAC9C,IAAI,CAAC,SAAS,CAAC,8BAAiB,CAAC,CAAC,6BAAa,CAAC,EAAE,CAAC;QACrD,CAAC;QAED,iBAAiB,GAAG,IAAI,GAAG,EAAgD,CAAC;QAC5E,KAAK,CAAC,cAAc,CAClB,IAAY,EACZ,KAAwC;YAExC,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBACrC,MAAM,IAAI,KAAK,CACb,wCAAwC,IAAI,iBAAiB,IAAI,oBAAoB,CACtF,CAAC;YACJ,CAAC;YACD,MAAM,UAAU,GAAG,IAAI,uCAAkB,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAC7D,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;YAC7C,IAAI,CAAC;gBACH,MAAM,UAAU,CAAC,MAAM,EAAE,CAAC;YAC5B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACpC,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;QAEQ,eAAe,CACtB,QAAkB,EAClB,OAAgC;YAEhC,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;gBAChC,MAAM,IAAI,gCAAoB,CAC5B,0CAA0C,CAC3C,CAAC;YACJ,CAAC;YAED,OAAO,KAAK,CAAC,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAClD,CAAC;;;AA5PU,8BAAS"}