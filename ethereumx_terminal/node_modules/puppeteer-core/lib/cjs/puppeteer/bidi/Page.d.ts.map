{"version": 3, "file": "Page.d.ts", "sourceRoot": "", "sources": ["../../../../src/bidi/Page.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;;AAEH,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,QAAQ,CAAC;AAGrC,OAAO,KAAK,QAAQ,MAAM,mBAAmB,CAAC;AAS9C,OAAO,KAAK,EAAC,UAAU,EAAC,MAAM,sBAAsB,CAAC;AAErD,OAAO,KAAK,EAAC,cAAc,EAAC,MAAM,iBAAiB,CAAC;AACpD,OAAO,KAAK,EAAC,YAAY,EAAC,MAAM,wBAAwB,CAAC;AACzD,OAAO,EACL,IAAI,EAEJ,KAAK,kBAAkB,EACvB,KAAK,YAAY,EACjB,KAAK,2BAA2B,EAChC,KAAK,iBAAiB,EACvB,MAAM,gBAAgB,CAAC;AACxB,OAAO,EAAC,aAAa,EAAC,MAAM,yBAAyB,CAAC;AACtD,OAAO,EAAC,QAAQ,EAAC,MAAM,oBAAoB,CAAC;AAG5C,OAAO,EAAC,OAAO,EAAC,MAAM,mBAAmB,CAAC;AAQ1C,OAAO,KAAK,EAAC,UAAU,EAAC,MAAM,yBAAyB,CAAC;AACxD,OAAO,KAAK,EAAC,SAAS,EAAC,MAAM,oBAAoB,CAAC;AASlD,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,uBAAuB,CAAC;AAMpD,OAAO,KAAK,EAAC,WAAW,EAAC,MAAM,cAAc,CAAC;AAC9C,OAAO,KAAK,EAAC,kBAAkB,EAAC,MAAM,qBAAqB,CAAC;AAC5D,OAAO,EAGL,KAAK,eAAe,EACrB,MAAM,sBAAsB,CAAC;AAC9B,OAAO,KAAK,EAAC,cAAc,EAAC,MAAM,iBAAiB,CAAC;AAKpD,OAAO,EAAC,SAAS,EAAC,MAAM,YAAY,CAAC;AAErC,OAAO,KAAK,EAAC,gBAAgB,EAAC,MAAM,mBAAmB,CAAC;AACxD,OAAO,EAAC,YAAY,EAAE,SAAS,EAAE,eAAe,EAAC,MAAM,YAAY,CAAC;AACpE,OAAO,KAAK,EAAC,YAAY,EAAC,MAAM,eAAe,CAAC;AAIhD,OAAO,KAAK,EAAC,cAAc,EAAC,MAAM,aAAa,CAAC;AAEhD;;GAEG;AACH,qBAAa,QAAS,SAAQ,IAAI;;IAoEhC,OAAO,IAAI,UAAU;gBAKnB,eAAe,EAAE,eAAe,EAChC,cAAc,EAAE,kBAAkB,EAClC,MAAM,EAAE,cAAc;IA+CxB;;OAEG;IACH,IAAI,UAAU,IAAI,cAAc,CAE/B;IAEc,YAAY,CACzB,SAAS,EAAE,MAAM,EACjB,iBAAiB,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,iBAAiB,GAAG,SAAS,GACnE,OAAO,CAAC,IAAI,CAAC;IAQD,YAAY,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAK7C,YAAY,CAAC,SAAS,EACnC,eAAe,EAAE,YAAY,CAAC,SAAS,CAAC,GACvC,OAAO,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,CAAC;IAkBrC,kBAAkB,CAAC,cAAc,EAAE,kBAAkB,GAAG,IAAI;IAI5D,IAAa,aAAa,IAAI,aAAa,CAE1C;IAED,IAAa,OAAO,IAAI,OAAO,CAE9B;IAED,IAAa,QAAQ,IAAI,QAAQ,CAEhC;IAED,IAAa,KAAK,IAAI,SAAS,CAE9B;IAED,IAAa,WAAW,IAAI,eAAe,CAE1C;IAED,IAAa,QAAQ,IAAI,YAAY,CAEpC;IAEQ,OAAO,IAAI,WAAW;IAItB,cAAc,IAAI,kBAAkB;IAIpC,SAAS,IAAI,SAAS;IAM/B;;OAEG;IACG,YAAY,IAAI,OAAO,CAAC,SAAS,CAAC;IAkB/B,MAAM,IAAI,SAAS,EAAE;IAI9B,KAAK,CAAC,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI;IAIzC,WAAW,CAAC,OAAO,EAAE,MAAM,GAAG,SAAS,EAAE;IA8IzC,qBAAqB,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,gBAAgB,GAAG,IAAI;IAIzD,QAAQ,IAAI,OAAO;IAIb,KAAK,CAAC,OAAO,CAAC,EAAE;QAAC,eAAe,CAAC,EAAE,OAAO,CAAA;KAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAiB3D,MAAM,CACnB,OAAO,GAAE,cAAmB,GAC3B,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAoC1B,2BAA2B,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI;IAIlD,iBAAiB,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI;IAIxC,iBAAiB,IAAI,MAAM;IAI3B,mBAAmB,IAAI,OAAO;IAIxB,cAAc,CAAC,OAAO,EAAE,kBAAkB,GAAG,OAAO,CAAC,IAAI,CAAC;IAI1D,oBAAoB,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAIrD,gBAAgB,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAI9C,oBAAoB,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IAI1D,oBAAoB,CACjC,QAAQ,CAAC,EAAE,YAAY,EAAE,GACxB,OAAO,CAAC,IAAI,CAAC;IAID,eAAe,CAAC,UAAU,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAInD,gBAAgB,CAAC,SAAS,CAAC,EAAE;QAC1C,YAAY,EAAE,OAAO,CAAC;QACtB,gBAAgB,EAAE,OAAO,CAAC;KAC3B,GAAG,OAAO,CAAC,IAAI,CAAC;IAIF,uBAAuB,CACpC,IAAI,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,kCAAkC,CAAC,MAAM,CAAC,GACnE,OAAO,CAAC,IAAI,CAAC;IAID,WAAW,CAAC,QAAQ,EAAE,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IAcpD,QAAQ,IAAI,QAAQ,GAAG,IAAI;IAIrB,GAAG,CAAC,OAAO,GAAE,UAAe,GAAG,OAAO,CAAC,MAAM,CAAC;IAuC9C,eAAe,CAC5B,OAAO,CAAC,EAAE,UAAU,GAAG,SAAS,GAC/B,OAAO,CAAC,QAAQ,CAAC;IAeL,WAAW,CACxB,OAAO,EAAE,QAAQ,CAAC,iBAAiB,CAAC,GACnC,OAAO,CAAC,MAAM,CAAC;IA0DH,gBAAgB,IAAI,OAAO,CAAC,UAAU,CAAC;IAUvC,YAAY,IAAI,OAAO,CAAC,IAAI,CAAC;IAM7B,qBAAqB,CAClC,MAAM,SAAS,OAAO,EAAE,EACxB,IAAI,SAAS,CAAC,GAAG,IAAI,EAAE,MAAM,KAAK,OAAO,GAAG,CAAC,GAAG,IAAI,EAAE,MAAM,KAAK,OAAO,EAExE,YAAY,EAAE,IAAI,GAAG,MAAM,EAC3B,GAAG,IAAI,EAAE,MAAM,GACd,OAAO,CAAC,2BAA2B,CAAC;IAUxB,mCAAmC,CAChD,EAAE,EAAE,MAAM,GACT,OAAO,CAAC,IAAI,CAAC;IAMD,cAAc,CAAC,IAAI,SAAS,OAAO,EAAE,EAAE,GAAG,EACvD,IAAI,EAAE,MAAM,EACZ,YAAY,EACR,CAAC,CAAC,GAAG,IAAI,EAAE,IAAI,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,GACnC;QAAC,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE,IAAI,KAAK,SAAS,CAAC,GAAG,CAAC,CAAA;KAAC,GAC/C,OAAO,CAAC,IAAI,CAAC;IAOP,yBAAyB,IAAI,OAAO;IAI9B,eAAe,CAAC,OAAO,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAOvD,uBAAuB,IAAI,KAAK;IAIhC,MAAM,IAAI,cAAc;IAIxB,kBAAkB,IAAI,KAAK;IAI3B,OAAO,IAAI,KAAK;IAIhB,sBAAsB,IAAI,KAAK;IAI/B,mBAAmB,IAAI,KAAK;IAI5B,sBAAsB,IAAI,KAAK;IAI/B,cAAc,IAAI,KAAK;IAIvB,wBAAwB,IAAI,KAAK;IAIjC,OAAO,IAAI,KAAK;IAIhB,SAAS,IAAI,KAAK;IAIlB,YAAY,IAAI,KAAK;IAIrB,qBAAqB,IAAI,KAAK;IAK9B,YAAY,IAAI,KAAK;IAIrB,mBAAmB,IAAI,KAAK;IAI5B,OAAO,IAAI,KAAK;IAIV,MAAM,CACnB,OAAO,GAAE,cAAmB,GAC3B,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;IAIhB,SAAS,CACtB,OAAO,GAAE,cAAmB,GAC3B,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;IA4BtB,mBAAmB,IAAI,KAAK;CAGtC"}