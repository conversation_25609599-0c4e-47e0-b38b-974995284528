{"version": 3, "file": "Session.js", "sourceRoot": "", "sources": ["../../../../../src/bidi/core/Session.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIH,kEAA0D;AAC1D,kDAAgD;AAChD,4DAA0E;AAC1E,4DAAwE;AAExE,6CAAqC;AAGrC,4EAA4E;AAC5E,yBAAyB;AAEzB;;GAEG;IACU,OAAO;sBACV,8BAAY;;;;;;iBADT,OACX,SAAQ,WAAoD;;;YAmG5D,wKAAQ,OAAO,6DAGd;YAiBD,+JAAM,IAAI,6DAKT;YAMD,8KAAM,SAAS,6DAId;YAMD,4JAAM,GAAG,6DAMR;;;QA/ID,MAAM,CAAC,KAAK,CAAC,IAAI,CACf,UAAsB,EACtB,YAA8C;YAE9C,mCAAmC;YACnC,EAAE;YACF,2EAA2E;YAC3E,sDAAsD;YAEtD,4CAA4C;YAC5C,0CAA0C;YAC1C,mEAAmE;YACnE,wBAAwB;YACxB,aAAa;YACb,MAAM;YACN,uCAAuC;YACvC,mCAAmC;YACnC,kDAAkD;YAClD,QAAQ;YACR,IAAI;YACJ,uBAAuB;YACvB,qCAAqC;YACrC,IAAI;YAEJ,IAAI,MAAM,CAAC;YACX,IAAI,CAAC;gBACH,MAAM,GAAG,CACP,MAAM,UAAU,CAAC,IAAI,CAAC,aAAa,EAAE;oBACnC,YAAY;iBACb,CAAC,CACH,CAAC,MAAM,CAAC;YACX,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,uCAAuC;gBACvC,IAAA,oBAAU,EAAC,GAAG,CAAC,CAAC;gBAChB,MAAM,GAAG;oBACP,SAAS,EAAE,EAAE;oBACb,YAAY,EAAE;wBACZ,mBAAmB,EAAE,KAAK;wBAC1B,WAAW,EAAE,EAAE;wBACf,cAAc,EAAE,EAAE;wBAClB,YAAY,EAAE,EAAE;wBAChB,aAAa,EAAE,KAAK;wBACpB,YAAY,EAAE,EAAE;qBACjB;iBACF,CAAC;YACJ,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YAChD,MAAM,OAAO,CAAC,WAAW,EAAE,CAAC;YAC5B,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,oBAAoB;QACpB,OAAO,iEAAqB;QACnB,YAAY,GAAG,IAAI,+BAAe,EAAE,CAAC;QACrC,KAAK,CAAyB;QAC9B,OAAO,CAAW;QAClB,UAAU,CAAa;QAChC,kBAAkB;QAElB,YAAoB,UAAsB,EAAE,IAA4B;YACtE,KAAK,EAAE,CAAC;YACR,oBAAoB;YACpB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;YAClB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;YAC7B,kBAAkB;QACpB,CAAC;QAED,KAAK,CAAC,WAAW;YACf,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAE7B,qEAAqE;YACpE,IAAY,CAAC,OAAO,GAAG,MAAM,oBAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEjD,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3D,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAC,MAAM,EAAC,EAAE,EAAE;gBACzC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC;QACL,CAAC;QAED,8BAA8B;QAC9B,IAAI,YAAY;YACd,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;QACjC,CAAC;QACD,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,KAAK,CAAC;QACpB,CAAC;QACD,IAAI,KAAK;YACP,OAAO,IAAI,CAAC,OAAO,KAAK,SAAS,CAAC;QACpC,CAAC;QACD,IAAI,EAAE;YACJ,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;QAC9B,CAAC;QACD,kBAAkB;QAGV,OAAO,CAAC,MAAe;YAC7B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;YACtB,IAAI,CAAC,6BAAa,CAAC,EAAE,CAAC;QACxB,CAAC;QAED,MAAM,CAA4B,OAA6B;YAC7D,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAClC,CAAC;QAED;;;;;;WAMG;QAKH,KAAK,CAAC,IAAI,CACR,MAAS,EACT,MAA6B;YAE7B,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACpD,CAAC;QAMD,KAAK,CAAC,SAAS,CAAC,MAAgB;YAC9B,MAAM,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBACnC,MAAM;aACP,CAAC,CAAC;QACL,CAAC;QAMD,KAAK,CAAC,GAAG;YACP,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;YACrC,CAAC;oBAAS,CAAC;gBACT,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;YACzC,CAAC;QACH,CAAC;QAED,yBAlDC,+BAAe,uBAiBf,IAAA,+BAAe,EAAU,OAAO,CAAC,EAAE;gBAClC,6DAA6D;gBAC7D,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,4BAQD,IAAA,+BAAe,EAAU,OAAO,CAAC,EAAE;gBAClC,6DAA6D;gBAC7D,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,sBAOD,IAAA,+BAAe,EAAU,OAAO,CAAC,EAAE;gBAClC,6DAA6D;gBAC7D,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,GASD,6BAAa,EAAC;YACb,IAAI,CAAC,OAAO;gBACV,mEAAmE,CAAC;YACtE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAC,CAAC,CAAC;YAE3C,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAC5B,KAAK,CAAC,6BAAa,CAAC,EAAE,CAAC;QACzB,CAAC;;;AA5JU,0BAAO"}