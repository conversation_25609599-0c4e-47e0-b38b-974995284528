# Copyright 2017 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.
#
# Contributing to Chrome DevTools Protocol: https://goo.gle/devtools-contribution-guide-cdp

version
  major 1
  minor 3

experimental domain Accessibility
  depends on DOM

  # Unique accessibility node identifier.
  type AXNodeId extends string

  # Enum of possible property types.
  type AXValueType extends string
    enum
      boolean
      tristate
      booleanOrUndefined
      idref
      idrefList
      integer
      node
      nodeList
      number
      string
      computedString
      token
      tokenList
      domRelation
      role
      internalRole
      valueUndefined

  # Enum of possible property sources.
  type AXValueSourceType extends string
    enum
      attribute
      implicit
      style
      contents
      placeholder
      relatedElement

  # Enum of possible native property sources (as a subtype of a particular AXValueSourceType).
  type AXValueNativeSourceType extends string
    enum
      description
      figcaption
      label
      labelfor
      labelwrapped
      legend
      rubyannotation
      tablecaption
      title
      other

  # A single source for a computed AX property.
  type AXValueSource extends object
    properties
      # What type of source this is.
      AXValueSourceType type
      # The value of this property source.
      optional AXValue value
      # The name of the relevant attribute, if any.
      optional string attribute
      # The value of the relevant attribute, if any.
      optional AXValue attributeValue
      # Whether this source is superseded by a higher priority source.
      optional boolean superseded
      # The native markup source for this value, e.g. a `<label>` element.
      optional AXValueNativeSourceType nativeSource
      # The value, such as a node or node list, of the native source.
      optional AXValue nativeSourceValue
      # Whether the value for this property is invalid.
      optional boolean invalid
      # Reason for the value being invalid, if it is.
      optional string invalidReason

  type AXRelatedNode extends object
    properties
      # The BackendNodeId of the related DOM node.
      DOM.BackendNodeId backendDOMNodeId
      # The IDRef value provided, if any.
      optional string idref
      # The text alternative of this node in the current context.
      optional string text

  type AXProperty extends object
    properties
      # The name of this property.
      AXPropertyName name
      # The value of this property.
      AXValue value

  # A single computed AX property.
  type AXValue extends object
    properties
      # The type of this value.
      AXValueType type
      # The computed value of this property.
      optional any value
      # One or more related nodes, if applicable.
      optional array of AXRelatedNode relatedNodes
      # The sources which contributed to the computation of this property.
      optional array of AXValueSource sources

  # Values of AXProperty name:
  # - from 'busy' to 'roledescription': states which apply to every AX node
  # - from 'live' to 'root': attributes which apply to nodes in live regions
  # - from 'autocomplete' to 'valuetext': attributes which apply to widgets
  # - from 'checked' to 'selected': states which apply to widgets
  # - from 'activedescendant' to 'owns' - relationships between elements other than parent/child/sibling.
  type AXPropertyName extends string
    enum
      busy
      disabled
      editable
      focusable
      focused
      hidden
      hiddenRoot
      invalid
      keyshortcuts
      settable
      roledescription
      live
      atomic
      relevant
      root
      autocomplete
      hasPopup
      level
      multiselectable
      orientation
      multiline
      readonly
      required
      valuemin
      valuemax
      valuetext
      checked
      expanded
      modal
      pressed
      selected
      activedescendant
      controls
      describedby
      details
      errormessage
      flowto
      labelledby
      owns

  # A node in the accessibility tree.
  type AXNode extends object
    properties
      # Unique identifier for this node.
      AXNodeId nodeId
      # Whether this node is ignored for accessibility
      boolean ignored
      # Collection of reasons why this node is hidden.
      optional array of AXProperty ignoredReasons
      # This `Node`'s role, whether explicit or implicit.
      optional AXValue role
      # This `Node`'s Chrome raw role.
      optional AXValue chromeRole
      # The accessible name for this `Node`.
      optional AXValue name
      # The accessible description for this `Node`.
      optional AXValue description
      # The value for this `Node`.
      optional AXValue value
      # All other properties
      optional array of AXProperty properties
      # ID for this node's parent.
      optional AXNodeId parentId
      # IDs for each of this node's child nodes.
      optional array of AXNodeId childIds
      # The backend ID for the associated DOM node, if any.
      optional DOM.BackendNodeId backendDOMNodeId
      # The frame ID for the frame associated with this nodes document.
      optional Page.FrameId frameId

  # Disables the accessibility domain.
  command disable

  # Enables the accessibility domain which causes `AXNodeId`s to remain consistent between method calls.
  # This turns on accessibility for the page, which can impact performance until accessibility is disabled.
  command enable

  # Fetches the accessibility node and partial accessibility tree for this DOM node, if it exists.
  experimental command getPartialAXTree
    parameters
      # Identifier of the node to get the partial accessibility tree for.
      optional DOM.NodeId nodeId
      # Identifier of the backend node to get the partial accessibility tree for.
      optional DOM.BackendNodeId backendNodeId
      # JavaScript object id of the node wrapper to get the partial accessibility tree for.
      optional Runtime.RemoteObjectId objectId
      # Whether to fetch this node's ancestors, siblings and children. Defaults to true.
      optional boolean fetchRelatives
    returns
      # The `Accessibility.AXNode` for this DOM node, if it exists, plus its ancestors, siblings and
      # children, if requested.
      array of AXNode nodes

  # Fetches the entire accessibility tree for the root Document
  experimental command getFullAXTree
    parameters
      # The maximum depth at which descendants of the root node should be retrieved.
      # If omitted, the full tree is returned.
      optional integer depth
      # The frame for whose document the AX tree should be retrieved.
      # If omited, the root frame is used.
      optional Page.FrameId frameId
    returns
      array of AXNode nodes

  # Fetches the root node.
  # Requires `enable()` to have been called previously.
  experimental command getRootAXNode
    parameters
      # The frame in whose document the node resides.
      # If omitted, the root frame is used.
      optional Page.FrameId frameId
    returns
      AXNode node

  # Fetches a node and all ancestors up to and including the root.
  # Requires `enable()` to have been called previously.
  experimental command getAXNodeAndAncestors
    parameters
      # Identifier of the node to get.
      optional DOM.NodeId nodeId
      # Identifier of the backend node to get.
      optional DOM.BackendNodeId backendNodeId
      # JavaScript object id of the node wrapper to get.
      optional Runtime.RemoteObjectId objectId
    returns
      array of AXNode nodes

  # Fetches a particular accessibility node by AXNodeId.
  # Requires `enable()` to have been called previously.
  experimental command getChildAXNodes
    parameters
      AXNodeId id
      # The frame in whose document the node resides.
      # If omitted, the root frame is used.
      optional Page.FrameId frameId
    returns
      array of AXNode nodes

  # Query a DOM node's accessibility subtree for accessible name and role.
  # This command computes the name and role for all nodes in the subtree, including those that are
  # ignored for accessibility, and returns those that mactch the specified name and role. If no DOM
  # node is specified, or the DOM node does not exist, the command returns an error. If neither
  # `accessibleName` or `role` is specified, it returns all the accessibility nodes in the subtree.
  experimental command queryAXTree
    parameters
      # Identifier of the node for the root to query.
      optional DOM.NodeId nodeId
      # Identifier of the backend node for the root to query.
      optional DOM.BackendNodeId backendNodeId
      # JavaScript object id of the node wrapper for the root to query.
      optional Runtime.RemoteObjectId objectId
      # Find nodes with this computed name.
      optional string accessibleName
      # Find nodes with this computed role.
      optional string role
    returns
      # A list of `Accessibility.AXNode` matching the specified attributes,
      # including nodes that are ignored for accessibility.
      array of AXNode nodes

  # The loadComplete event mirrors the load complete event sent by the browser to assistive
  # technology when the web page has finished loading.
  experimental event loadComplete
    parameters
      # New document root node.
      AXNode root

  # The nodesUpdated event is sent every time a previously requested node has changed the in tree.
  experimental event nodesUpdated
    parameters
      # Updated node data.
      array of AXNode nodes

experimental domain Animation
  depends on Runtime
  depends on DOM

  # Animation instance.
  type Animation extends object
    properties
      # `Animation`'s id.
      string id
      # `Animation`'s name.
      string name
      # `Animation`'s internal paused state.
      boolean pausedState
      # `Animation`'s play state.
      string playState
      # `Animation`'s playback rate.
      number playbackRate
      # `Animation`'s start time.
      number startTime
      # `Animation`'s current time.
      number currentTime
      # Animation type of `Animation`.
      enum type
        CSSTransition
        CSSAnimation
        WebAnimation
      # `Animation`'s source animation node.
      optional AnimationEffect source
      # A unique ID for `Animation` representing the sources that triggered this CSS
      # animation/transition.
      optional string cssId

  # AnimationEffect instance
  type AnimationEffect extends object
    properties
      # `AnimationEffect`'s delay.
      number delay
      # `AnimationEffect`'s end delay.
      number endDelay
      # `AnimationEffect`'s iteration start.
      number iterationStart
      # `AnimationEffect`'s iterations.
      number iterations
      # `AnimationEffect`'s iteration duration.
      number duration
      # `AnimationEffect`'s playback direction.
      string direction
      # `AnimationEffect`'s fill mode.
      string fill
      # `AnimationEffect`'s target node.
      optional DOM.BackendNodeId backendNodeId
      # `AnimationEffect`'s keyframes.
      optional KeyframesRule keyframesRule
      # `AnimationEffect`'s timing function.
      string easing

  # Keyframes Rule
  type KeyframesRule extends object
    properties
      # CSS keyframed animation's name.
      optional string name
      # List of animation keyframes.
      array of KeyframeStyle keyframes

  # Keyframe Style
  type KeyframeStyle extends object
    properties
      # Keyframe's time offset.
      string offset
      # `AnimationEffect`'s timing function.
      string easing

  # Disables animation domain notifications.
  command disable

  # Enables animation domain notifications.
  command enable

  # Returns the current time of the an animation.
  command getCurrentTime
    parameters
      # Id of animation.
      string id
    returns
      # Current time of the page.
      number currentTime

  # Gets the playback rate of the document timeline.
  command getPlaybackRate
    returns
      # Playback rate for animations on page.
      number playbackRate

  # Releases a set of animations to no longer be manipulated.
  command releaseAnimations
    parameters
      # List of animation ids to seek.
      array of string animations

  # Gets the remote object of the Animation.
  command resolveAnimation
    parameters
      # Animation id.
      string animationId
    returns
      # Corresponding remote object.
      Runtime.RemoteObject remoteObject

  # Seek a set of animations to a particular time within each animation.
  command seekAnimations
    parameters
      # List of animation ids to seek.
      array of string animations
      # Set the current time of each animation.
      number currentTime

  # Sets the paused state of a set of animations.
  command setPaused
    parameters
      # Animations to set the pause state of.
      array of string animations
      # Paused state to set to.
      boolean paused

  # Sets the playback rate of the document timeline.
  command setPlaybackRate
    parameters
      # Playback rate for animations on page
      number playbackRate

  # Sets the timing of an animation node.
  command setTiming
    parameters
      # Animation id.
      string animationId
      # Duration of the animation.
      number duration
      # Delay of the animation.
      number delay

  # Event for when an animation has been cancelled.
  event animationCanceled
    parameters
      # Id of the animation that was cancelled.
      string id

  # Event for each animation that has been created.
  event animationCreated
    parameters
      # Id of the animation that was created.
      string id

  # Event for animation that has been started.
  event animationStarted
    parameters
      # Animation that was started.
      Animation animation

# Audits domain allows investigation of page violations and possible improvements.
experimental domain Audits
  depends on Network

  # Information about a cookie that is affected by an inspector issue.
  type AffectedCookie extends object
    properties
      # The following three properties uniquely identify a cookie
      string name
      string path
      string domain

  # Information about a request that is affected by an inspector issue.
  type AffectedRequest extends object
    properties
      # The unique request id.
      Network.RequestId requestId
      optional string url

  # Information about the frame affected by an inspector issue.
  type AffectedFrame extends object
    properties
      Page.FrameId frameId

  type CookieExclusionReason extends string
    enum
      ExcludeSameSiteUnspecifiedTreatedAsLax
      ExcludeSameSiteNoneInsecure
      ExcludeSameSiteLax
      ExcludeSameSiteStrict
      ExcludeInvalidSameParty
      ExcludeSamePartyCrossPartyContext
      ExcludeDomainNonASCII
      ExcludeThirdPartyCookieBlockedInFirstPartySet
      ExcludeThirdPartyPhaseout

  type CookieWarningReason extends string
    enum
      WarnSameSiteUnspecifiedCrossSiteContext
      WarnSameSiteNoneInsecure
      WarnSameSiteUnspecifiedLaxAllowUnsafe
      WarnSameSiteStrictLaxDowngradeStrict
      WarnSameSiteStrictCrossDowngradeStrict
      WarnSameSiteStrictCrossDowngradeLax
      WarnSameSiteLaxCrossDowngradeStrict
      WarnSameSiteLaxCrossDowngradeLax
      WarnAttributeValueExceedsMaxSize
      WarnDomainNonASCII
      WarnThirdPartyPhaseout
      WarnCrossSiteRedirectDowngradeChangesInclusion

  type CookieOperation extends string
    enum
      SetCookie
      ReadCookie

  # This information is currently necessary, as the front-end has a difficult
  # time finding a specific cookie. With this, we can convey specific error
  # information without the cookie.
  type CookieIssueDetails extends object
    properties
      # If AffectedCookie is not set then rawCookieLine contains the raw
      # Set-Cookie header string. This hints at a problem where the
      # cookie line is syntactically or semantically malformed in a way
      # that no valid cookie could be created.
      optional AffectedCookie cookie
      optional string rawCookieLine
      array of CookieWarningReason cookieWarningReasons
      array of CookieExclusionReason cookieExclusionReasons
      # Optionally identifies the site-for-cookies and the cookie url, which
      # may be used by the front-end as additional context.
      CookieOperation operation
      optional string siteForCookies
      optional string cookieUrl
      optional AffectedRequest request

  type MixedContentResolutionStatus extends string
    enum
      MixedContentBlocked
      MixedContentAutomaticallyUpgraded
      MixedContentWarning

  type MixedContentResourceType extends string
    enum
      AttributionSrc
      Audio
      Beacon
      CSPReport
      Download
      EventSource
      Favicon
      Font
      Form
      Frame
      Image
      Import
      Manifest
      Ping
      PluginData
      PluginResource
      Prefetch
      Resource
      Script
      ServiceWorker
      SharedWorker
      Stylesheet
      Track
      Video
      Worker
      XMLHttpRequest
      XSLT

  type MixedContentIssueDetails extends object
    properties
      # The type of resource causing the mixed content issue (css, js, iframe,
      # form,...). Marked as optional because it is mapped to from
      # blink::mojom::RequestContextType, which will be replaced
      # by network::mojom::RequestDestination
      optional MixedContentResourceType resourceType
      # The way the mixed content issue is being resolved.
      MixedContentResolutionStatus resolutionStatus
      # The unsafe http url causing the mixed content issue.
      string insecureURL
      # The url responsible for the call to an unsafe url.
      string mainResourceURL
      # The mixed content request.
      # Does not always exist (e.g. for unsafe form submission urls).
      optional AffectedRequest request
      # Optional because not every mixed content issue is necessarily linked to a frame.
      optional AffectedFrame frame

  # Enum indicating the reason a response has been blocked. These reasons are
  # refinements of the net error BLOCKED_BY_RESPONSE.
  type BlockedByResponseReason extends string
    enum
      CoepFrameResourceNeedsCoepHeader
      CoopSandboxedIFrameCannotNavigateToCoopPage
      CorpNotSameOrigin
      CorpNotSameOriginAfterDefaultedToSameOriginByCoep
      CorpNotSameSite

  # Details for a request that has been blocked with the BLOCKED_BY_RESPONSE
  # code. Currently only used for COEP/COOP, but may be extended to include
  # some CSP errors in the future.
  type BlockedByResponseIssueDetails extends object
    properties
      AffectedRequest request
      optional AffectedFrame parentFrame
      optional AffectedFrame blockedFrame
      BlockedByResponseReason reason

  type HeavyAdResolutionStatus extends string
    enum
      HeavyAdBlocked
      HeavyAdWarning

  type HeavyAdReason extends string
    enum
      NetworkTotalLimit
      CpuTotalLimit
      CpuPeakLimit

  type HeavyAdIssueDetails extends object
    properties
      # The resolution status, either blocking the content or warning.
      HeavyAdResolutionStatus resolution
      # The reason the ad was blocked, total network or cpu or peak cpu.
      HeavyAdReason reason
      # The frame that was blocked.
      AffectedFrame frame

  type ContentSecurityPolicyViolationType extends string
    enum
      kInlineViolation
      kEvalViolation
      kURLViolation
      kTrustedTypesSinkViolation
      kTrustedTypesPolicyViolation
      kWasmEvalViolation

  type SourceCodeLocation extends object
    properties
      optional Runtime.ScriptId scriptId
      string url
      integer lineNumber
      integer columnNumber

  type ContentSecurityPolicyIssueDetails extends object
    properties
      # The url not included in allowed sources.
      optional string blockedURL
      # Specific directive that is violated, causing the CSP issue.
      string violatedDirective
      boolean isReportOnly
      ContentSecurityPolicyViolationType contentSecurityPolicyViolationType
      optional AffectedFrame frameAncestor
      optional SourceCodeLocation sourceCodeLocation
      optional DOM.BackendNodeId violatingNodeId

  type SharedArrayBufferIssueType extends string
    enum
      TransferIssue
      CreationIssue

  # Details for a issue arising from an SAB being instantiated in, or
  # transferred to a context that is not cross-origin isolated.
  type SharedArrayBufferIssueDetails extends object
    properties
      SourceCodeLocation sourceCodeLocation
      boolean isWarning
      SharedArrayBufferIssueType type

  type LowTextContrastIssueDetails extends object
    properties
      DOM.BackendNodeId violatingNodeId
      string violatingNodeSelector
      number contrastRatio
      number thresholdAA
      number thresholdAAA
      string fontSize
      string fontWeight

  # Details for a CORS related issue, e.g. a warning or error related to
  # CORS RFC1918 enforcement.
  type CorsIssueDetails extends object
    properties
      Network.CorsErrorStatus corsErrorStatus
      boolean isWarning
      AffectedRequest request
      optional SourceCodeLocation location
      optional string initiatorOrigin
      optional Network.IPAddressSpace resourceIPAddressSpace
      optional Network.ClientSecurityState clientSecurityState

  type AttributionReportingIssueType extends string
    enum
      PermissionPolicyDisabled
      UntrustworthyReportingOrigin
      InsecureContext
      # TODO(apaseltiner): Rename this to InvalidRegisterSourceHeader
      InvalidHeader
      InvalidRegisterTriggerHeader
      SourceAndTriggerHeaders
      SourceIgnored
      TriggerIgnored
      OsSourceIgnored
      OsTriggerIgnored
      InvalidRegisterOsSourceHeader
      InvalidRegisterOsTriggerHeader
      WebAndOsHeaders
      NoWebOrOsSupport
      NavigationRegistrationWithoutTransientUserActivation

  # Details for issues around "Attribution Reporting API" usage.
  # Explainer: https://github.com/WICG/attribution-reporting-api
  type AttributionReportingIssueDetails extends object
    properties
      AttributionReportingIssueType violationType
      optional AffectedRequest request
      optional DOM.BackendNodeId violatingNodeId
      optional string invalidParameter

# Details for issues about documents in Quirks Mode
# or Limited Quirks Mode that affects page layouting.
  type QuirksModeIssueDetails extends object
    properties
      # If false, it means the document's mode is "quirks"
      # instead of "limited-quirks".
      boolean isLimitedQuirksMode
      DOM.BackendNodeId documentNodeId
      string url
      Page.FrameId frameId
      Network.LoaderId loaderId

  deprecated type NavigatorUserAgentIssueDetails extends object
    properties
      string url
      optional SourceCodeLocation location

  type GenericIssueErrorType extends string
    enum
      CrossOriginPortalPostMessageError
      FormLabelForNameError
      FormDuplicateIdForInputError
      FormInputWithNoLabelError
      FormAutocompleteAttributeEmptyError
      FormEmptyIdAndNameAttributesForInputError
      FormAriaLabelledByToNonExistingId
      FormInputAssignedAutocompleteValueToIdOrNameAttributeError
      FormLabelHasNeitherForNorNestedInput
      FormLabelForMatchesNonExistingIdError
      FormInputHasWrongButWellIntendedAutocompleteValueError
      ResponseWasBlockedByORB

  # Depending on the concrete errorType, different properties are set.
  type GenericIssueDetails extends object
    properties
      # Issues with the same errorType are aggregated in the frontend.
      GenericIssueErrorType errorType
      optional Page.FrameId frameId
      optional DOM.BackendNodeId violatingNodeId
      optional string violatingNodeAttribute
      optional AffectedRequest request

  # This issue tracks information needed to print a deprecation message.
  # https://source.chromium.org/chromium/chromium/src/+/main:third_party/blink/renderer/core/frame/third_party/blink/renderer/core/frame/deprecation/README.md
  type DeprecationIssueDetails extends object
    properties
      optional AffectedFrame affectedFrame
      SourceCodeLocation sourceCodeLocation
      # One of the deprecation names from third_party/blink/renderer/core/frame/deprecation/deprecation.json5
      string type

  # This issue warns about sites in the redirect chain of a finished navigation
  # that may be flagged as trackers and have their state cleared if they don't
  # receive a user interaction. Note that in this context 'site' means eTLD+1.
  # For example, if the URL `https://example.test:80/bounce` was in the
  # redirect chain, the site reported would be `example.test`.
  type BounceTrackingIssueDetails extends object
    properties
      array of string trackingSites

  # This issue warns about third-party sites that are accessing cookies on the
  # current page, and have been permitted due to having a global metadata grant.
  # Note that in this context 'site' means eTLD+1. For example, if the URL
  # `https://example.test:80/web_page` was accessing cookies, the site reported
  # would be `example.test`.
  type CookieDeprecationMetadataIssueDetails extends object
    properties
      array of string allowedSites

  type ClientHintIssueReason extends string
    enum
      # Items in the accept-ch meta tag allow list must be valid origins.
      # No special values (e.g. self, none, and *) are permitted.
      MetaTagAllowListInvalidOrigin
      # Only accept-ch meta tags in the original HTML sent from the server
      # are respected. Any injected via javascript (or other means) are ignored.
      MetaTagModifiedHTML

  type FederatedAuthRequestIssueDetails extends object
    properties
      FederatedAuthRequestIssueReason federatedAuthRequestIssueReason

  # Represents the failure reason when a federated authentication reason fails.
  # Should be updated alongside RequestIdTokenStatus in
  # third_party/blink/public/mojom/devtools/inspector_issue.mojom to include
  # all cases except for success.
  type FederatedAuthRequestIssueReason extends string
    enum
      ShouldEmbargo
      TooManyRequests
      WellKnownHttpNotFound
      WellKnownNoResponse
      WellKnownInvalidResponse
      WellKnownListEmpty
      WellKnownInvalidContentType
      ConfigNotInWellKnown
      WellKnownTooBig
      ConfigHttpNotFound
      ConfigNoResponse
      ConfigInvalidResponse
      ConfigInvalidContentType
      ClientMetadataHttpNotFound
      ClientMetadataNoResponse
      ClientMetadataInvalidResponse
      ClientMetadataInvalidContentType
      DisabledInSettings
      ErrorFetchingSignin
      InvalidSigninResponse
      AccountsHttpNotFound
      AccountsNoResponse
      AccountsInvalidResponse
      AccountsListEmpty
      AccountsInvalidContentType
      IdTokenHttpNotFound
      IdTokenNoResponse
      IdTokenInvalidResponse
      IdTokenIdpErrorResponse
      IdTokenCrossSiteIdpErrorResponse
      IdTokenInvalidRequest
      IdTokenInvalidContentType
      ErrorIdToken
      Canceled
      RpPageNotVisible
      SilentMediationFailure
      ThirdPartyCookiesBlocked
      NotSignedInWithIdp

  type FederatedAuthUserInfoRequestIssueDetails extends object
    properties
      FederatedAuthUserInfoRequestIssueReason federatedAuthUserInfoRequestIssueReason

  # Represents the failure reason when a getUserInfo() call fails.
  # Should be updated alongside FederatedAuthUserInfoRequestResult in
  # third_party/blink/public/mojom/devtools/inspector_issue.mojom.
  type FederatedAuthUserInfoRequestIssueReason extends string
    enum
      NotSameOrigin
      NotIframe
      NotPotentiallyTrustworthy
      NoApiPermission
      NotSignedInWithIdp
      NoAccountSharingPermission
      InvalidConfigOrWellKnown
      InvalidAccountsResponse
      NoReturningUserFromFetchedAccounts

  # This issue tracks client hints related issues. It's used to deprecate old
  # features, encourage the use of new ones, and provide general guidance.
  type ClientHintIssueDetails extends object
    properties
      SourceCodeLocation sourceCodeLocation
      ClientHintIssueReason clientHintIssueReason

  type FailedRequestInfo extends object
    properties
      # The URL that failed to load.
      string url
      # The failure message for the failed request.
      string failureMessage
      optional Network.RequestId requestId

  type StyleSheetLoadingIssueReason extends string
    enum
      LateImportRule
      RequestFailed

  # This issue warns when a referenced stylesheet couldn't be loaded.
  type StylesheetLoadingIssueDetails extends object
    properties
      # Source code position that referenced the failing stylesheet.
      SourceCodeLocation sourceCodeLocation
      # Reason why the stylesheet couldn't be loaded.
      StyleSheetLoadingIssueReason styleSheetLoadingIssueReason
      # Contains additional info when the failure was due to a request.
      optional FailedRequestInfo failedRequestInfo

  type PropertyRuleIssueReason extends string
    enum
      InvalidSyntax
      InvalidInitialValue
      InvalidInherits
      InvalidName

  # This issue warns about errors in property rules that lead to property
  # registrations being ignored.
  type PropertyRuleIssueDetails extends object
    properties
      # Source code position of the property rule.
      SourceCodeLocation sourceCodeLocation
      # Reason why the property rule was discarded.
      PropertyRuleIssueReason propertyRuleIssueReason
      # The value of the property rule property that failed to parse
      optional string propertyValue

  # A unique identifier for the type of issue. Each type may use one of the
  # optional fields in InspectorIssueDetails to convey more specific
  # information about the kind of issue.
  type InspectorIssueCode extends string
    enum
      CookieIssue
      MixedContentIssue
      BlockedByResponseIssue
      HeavyAdIssue
      ContentSecurityPolicyIssue
      SharedArrayBufferIssue
      LowTextContrastIssue
      CorsIssue
      AttributionReportingIssue
      QuirksModeIssue
      # Deprecated
      NavigatorUserAgentIssue
      GenericIssue
      DeprecationIssue
      ClientHintIssue
      FederatedAuthRequestIssue
      BounceTrackingIssue
      CookieDeprecationMetadataIssue
      StylesheetLoadingIssue
      FederatedAuthUserInfoRequestIssue
      PropertyRuleIssue

  # This struct holds a list of optional fields with additional information
  # specific to the kind of issue. When adding a new issue code, please also
  # add a new optional field to this type.
  type InspectorIssueDetails extends object
    properties
      optional CookieIssueDetails cookieIssueDetails
      optional MixedContentIssueDetails mixedContentIssueDetails
      optional BlockedByResponseIssueDetails blockedByResponseIssueDetails
      optional HeavyAdIssueDetails heavyAdIssueDetails
      optional ContentSecurityPolicyIssueDetails contentSecurityPolicyIssueDetails
      optional SharedArrayBufferIssueDetails sharedArrayBufferIssueDetails
      optional LowTextContrastIssueDetails lowTextContrastIssueDetails
      optional CorsIssueDetails corsIssueDetails
      optional AttributionReportingIssueDetails attributionReportingIssueDetails
      optional QuirksModeIssueDetails quirksModeIssueDetails
      deprecated optional NavigatorUserAgentIssueDetails navigatorUserAgentIssueDetails
      optional GenericIssueDetails genericIssueDetails
      optional DeprecationIssueDetails deprecationIssueDetails
      optional ClientHintIssueDetails clientHintIssueDetails
      optional FederatedAuthRequestIssueDetails federatedAuthRequestIssueDetails
      optional BounceTrackingIssueDetails bounceTrackingIssueDetails
      optional CookieDeprecationMetadataIssueDetails cookieDeprecationMetadataIssueDetails
      optional StylesheetLoadingIssueDetails stylesheetLoadingIssueDetails
      optional PropertyRuleIssueDetails propertyRuleIssueDetails
      optional FederatedAuthUserInfoRequestIssueDetails federatedAuthUserInfoRequestIssueDetails

  # A unique id for a DevTools inspector issue. Allows other entities (e.g.
  # exceptions, CDP message, console messages, etc.) to reference an issue.
  type IssueId extends string

  # An inspector issue reported from the back-end.
  type InspectorIssue extends object
    properties
      InspectorIssueCode code
      InspectorIssueDetails details
      # A unique id for this issue. May be omitted if no other entity (e.g.
      # exception, CDP message, etc.) is referencing this issue.
      optional IssueId issueId

  # Returns the response body and size if it were re-encoded with the specified settings. Only
  # applies to images.
  command getEncodedResponse
    parameters
      # Identifier of the network request to get content for.
      Network.RequestId requestId
      # The encoding to use.
      enum encoding
        webp
        jpeg
        png
      # The quality of the encoding (0-1). (defaults to 1)
      optional number quality
      # Whether to only return the size information (defaults to false).
      optional boolean sizeOnly
    returns
      # The encoded body as a base64 string. Omitted if sizeOnly is true.
      optional binary body
      # Size before re-encoding.
      integer originalSize
      # Size after re-encoding.
      integer encodedSize

  # Disables issues domain, prevents further issues from being reported to the client.
  command disable

  # Enables issues domain, sends the issues collected so far to the client by means of the
  # `issueAdded` event.
  command enable

  # Runs the contrast check for the target page. Found issues are reported
  # using Audits.issueAdded event.
  command checkContrast
    parameters
      # Whether to report WCAG AAA level issues. Default is false.
      optional boolean reportAAA

  # Runs the form issues check for the target page. Found issues are reported
  # using Audits.issueAdded event.
  command checkFormsIssues
    returns
      array of GenericIssueDetails formIssues

  event issueAdded
    parameters
      InspectorIssue issue

# Defines commands and events for Autofill.
experimental domain Autofill
  type CreditCard extends object
    properties
      # 16-digit credit card number.
      string number
      # Name of the credit card owner.
      string name
      # 2-digit expiry month.
      string expiryMonth
      # 4-digit expiry year.
      string expiryYear
      # 3-digit card verification code.
      string cvc

  type AddressField extends object
    properties
      # address field name, for example GIVEN_NAME.
      string name
      # address field value, for example Jon Doe.
      string value

  # A list of address fields.
  type AddressFields extends object
    properties
      array of AddressField fields

  type Address extends object
    properties
      # fields and values defining an address.
      array of AddressField fields

  # Defines how an address can be displayed like in chrome://settings/addresses.
  # Address UI is a two dimensional array, each inner array is an "address information line", and when rendered in a UI surface should be displayed as such.
  # The following address UI for instance:
  # [[{name: "GIVE_NAME", value: "Jon"}, {name: "FAMILY_NAME", value: "Doe"}], [{name: "CITY", value: "Munich"}, {name: "ZIP", value: "81456"}]]
  # should allow the receiver to render:
  # Jon Doe
  # Munich 81456
  type AddressUI extends object
    properties
      # A two dimension array containing the repesentation of values from an address profile.
      array of AddressFields addressFields

  # Specified whether a filled field was done so by using the html autocomplete attribute or autofill heuristics.
  type FillingStrategy extends string
    enum
      autocompleteAttribute
      autofillInferred

  type FilledField extends object
    properties
      # The type of the field, e.g text, password etc.
      string htmlType
      # the html id
      string id
      # the html name
      string name
      # the field value
      string value
      # The actual field type, e.g FAMILY_NAME
      string autofillType
      # The filling strategy
      FillingStrategy fillingStrategy

  # Emitted when an address form is filled.
  event addressFormFilled
    parameters
      # Information about the fields that were filled
      array of FilledField filledFields
      # An UI representation of the address used to fill the form.
      # Consists of a 2D array where each child represents an address/profile line.
      AddressUI addressUi

  # Trigger autofill on a form identified by the fieldId.
  # If the field and related form cannot be autofilled, returns an error.
  command trigger
    parameters
      # Identifies a field that serves as an anchor for autofill.
      DOM.BackendNodeId fieldId
      # Identifies the frame that field belongs to.
      optional Page.FrameId frameId
      # Credit card information to fill out the form. Credit card data is not saved.
      CreditCard card

  # Set addresses so that developers can verify their forms implementation.
  command setAddresses
    # Test addresses for the available countries.
    parameters
      array of Address addresses

  # Disables autofill domain notifications.
  command disable

  # Enables autofill domain notifications.
  command enable

# Defines events for background web platform features.
experimental domain BackgroundService
  # The Background Service that will be associated with the commands/events.
  # Every Background Service operates independently, but they share the same
  # API.
  type ServiceName extends string
    enum
      backgroundFetch
      backgroundSync
      pushMessaging
      notifications
      paymentHandler
      periodicBackgroundSync

  # Enables event updates for the service.
  command startObserving
    parameters
      ServiceName service

  # Disables event updates for the service.
  command stopObserving
    parameters
      ServiceName service

  # Set the recording state for the service.
  command setRecording
    parameters
      boolean shouldRecord
      ServiceName service

  # Clears all stored data for the service.
  command clearEvents
    parameters
      ServiceName service

  # Called when the recording state for the service has been updated.
  event recordingStateChanged
    parameters
      boolean isRecording
      ServiceName service

  # A key-value pair for additional event information to pass along.
  type EventMetadata extends object
    properties
      string key
      string value

  type BackgroundServiceEvent extends object
    properties
      # Timestamp of the event (in seconds).
      Network.TimeSinceEpoch timestamp
      # The origin this event belongs to.
      string origin
      # The Service Worker ID that initiated the event.
      ServiceWorker.RegistrationID serviceWorkerRegistrationId
      # The Background Service this event belongs to.
      ServiceName service
      # A description of the event.
      string eventName
      # An identifier that groups related events together.
      string instanceId
      # A list of event-specific information.
      array of EventMetadata eventMetadata
      # Storage key this event belongs to.
      string storageKey

  # Called with all existing backgroundServiceEvents when enabled, and all new
  # events afterwards if enabled and recording.
  event backgroundServiceEventReceived
    parameters
      BackgroundServiceEvent backgroundServiceEvent

# The Browser domain defines methods and events for browser managing.
domain Browser
  experimental type BrowserContextID extends string
  experimental type WindowID extends integer

  # The state of the browser window.
  experimental type WindowState extends string
    enum
      normal
      minimized
      maximized
      fullscreen

  # Browser window bounds information
  experimental type Bounds extends object
    properties
      # The offset from the left edge of the screen to the window in pixels.
      optional integer left
      # The offset from the top edge of the screen to the window in pixels.
      optional integer top
      # The window width in pixels.
      optional integer width
      # The window height in pixels.
      optional integer height
      # The window state. Default to normal.
      optional WindowState windowState

  experimental type PermissionType extends string
    enum
      accessibilityEvents
      audioCapture
      backgroundSync
      backgroundFetch
      clipboardReadWrite
      clipboardSanitizedWrite
      displayCapture
      durableStorage
      flash
      geolocation
      idleDetection
      localFonts
      midi
      midiSysex
      nfc
      notifications
      paymentHandler
      periodicBackgroundSync
      protectedMediaIdentifier
      sensors
      storageAccess
      topLevelStorageAccess
      videoCapture
      videoCapturePanTiltZoom
      wakeLockScreen
      wakeLockSystem
      windowManagement

  experimental type PermissionSetting extends string
    enum
      granted
      denied
      prompt

  # Definition of PermissionDescriptor defined in the Permissions API:
  # https://w3c.github.io/permissions/#dom-permissiondescriptor.
  experimental type PermissionDescriptor extends object
    properties
      # Name of permission.
      # See https://cs.chromium.org/chromium/src/third_party/blink/renderer/modules/permissions/permission_descriptor.idl for valid permission names.
      string name
      # For "midi" permission, may also specify sysex control.
      optional boolean sysex
      # For "push" permission, may specify userVisibleOnly.
      # Note that userVisibleOnly = true is the only currently supported type.
      optional boolean userVisibleOnly
      # For "clipboard" permission, may specify allowWithoutSanitization.
      optional boolean allowWithoutSanitization
      # For "camera" permission, may specify panTiltZoom.
      optional boolean panTiltZoom

  # Browser command ids used by executeBrowserCommand.
  experimental type BrowserCommandId extends string
    enum
      openTabSearch
      closeTabSearch

  # Set permission settings for given origin.
  experimental command setPermission
    parameters
      # Descriptor of permission to override.
      PermissionDescriptor permission
      # Setting of the permission.
      PermissionSetting setting
      # Origin the permission applies to, all origins if not specified.
      optional string origin
      # Context to override. When omitted, default browser context is used.
      optional BrowserContextID browserContextId

  # Grant specific permissions to the given origin and reject all others.
  experimental command grantPermissions
    parameters
      array of PermissionType permissions
      # Origin the permission applies to, all origins if not specified.
      optional string origin
      # BrowserContext to override permissions. When omitted, default browser context is used.
      optional BrowserContextID browserContextId

  # Reset all permission management for all origins.
  experimental command resetPermissions
    parameters
      # BrowserContext to reset permissions. When omitted, default browser context is used.
      optional BrowserContextID browserContextId

  # Set the behavior when downloading a file.
  experimental command setDownloadBehavior
    parameters
      # Whether to allow all or deny all download requests, or use default Chrome behavior if
      # available (otherwise deny). |allowAndName| allows download and names files according to
      # their dowmload guids.
      enum behavior
        deny
        allow
        allowAndName
        default
      # BrowserContext to set download behavior. When omitted, default browser context is used.
      optional BrowserContextID browserContextId
      # The default path to save downloaded files to. This is required if behavior is set to 'allow'
      # or 'allowAndName'.
      optional string downloadPath
      # Whether to emit download events (defaults to false).
      optional boolean eventsEnabled

  # Cancel a download if in progress
  experimental command cancelDownload
    parameters
      # Global unique identifier of the download.
      string guid
      # BrowserContext to perform the action in. When omitted, default browser context is used.
      optional BrowserContextID browserContextId

  # Fired when page is about to start a download.
  experimental event downloadWillBegin
    parameters
      # Id of the frame that caused the download to begin.
      Page.FrameId frameId
      # Global unique identifier of the download.
      string guid
      # URL of the resource being downloaded.
      string url
      # Suggested file name of the resource (the actual name of the file saved on disk may differ).
      string suggestedFilename

  # Fired when download makes progress. Last call has |done| == true.
  experimental event downloadProgress
    parameters
      # Global unique identifier of the download.
      string guid
      # Total expected bytes to download.
      number totalBytes
      # Total bytes received.
      number receivedBytes
      # Download status.
      enum state
        inProgress
        completed
        canceled

  # Close browser gracefully.
  command close

  # Crashes browser on the main thread.
  experimental command crash

  # Crashes GPU process.
  experimental command crashGpuProcess

  # Returns version information.
  command getVersion
    returns
      # Protocol version.
      string protocolVersion
      # Product name.
      string product
      # Product revision.
      string revision
      # User-Agent.
      string userAgent
      # V8 version.
      string jsVersion

  # Returns the command line switches for the browser process if, and only if
  # --enable-automation is on the commandline.
  experimental command getBrowserCommandLine
    returns
      # Commandline parameters
      array of string arguments

  # Chrome histogram bucket.
  experimental type Bucket extends object
    properties
      # Minimum value (inclusive).
      integer low
      # Maximum value (exclusive).
      integer high
      # Number of samples.
      integer count

  # Chrome histogram.
  experimental type Histogram extends object
    properties
      # Name.
      string name
      # Sum of sample values.
      integer sum
      # Total number of samples.
      integer count
      # Buckets.
      array of Bucket buckets

  # Get Chrome histograms.
  experimental command getHistograms
    parameters
      # Requested substring in name. Only histograms which have query as a
      # substring in their name are extracted. An empty or absent query returns
      # all histograms.
      optional string query
      # If true, retrieve delta since last delta call.
      optional boolean delta

    returns
      # Histograms.
      array of Histogram histograms

  # Get a Chrome histogram by name.
  experimental command getHistogram
    parameters
      # Requested histogram name.
      string name
      # If true, retrieve delta since last delta call.
      optional boolean delta
    returns
      # Histogram.
      Histogram histogram

  # Get position and size of the browser window.
  experimental command getWindowBounds
    parameters
      # Browser window id.
      WindowID windowId
    returns
      # Bounds information of the window. When window state is 'minimized', the restored window
      # position and size are returned.
      Bounds bounds

  # Get the browser window that contains the devtools target.
  experimental command getWindowForTarget
    parameters
      # Devtools agent host id. If called as a part of the session, associated targetId is used.
      optional Target.TargetID targetId
    returns
      # Browser window id.
      WindowID windowId
      # Bounds information of the window. When window state is 'minimized', the restored window
      # position and size are returned.
      Bounds bounds

  # Set position and/or size of the browser window.
  experimental command setWindowBounds
    parameters
      # Browser window id.
      WindowID windowId
      # New window bounds. The 'minimized', 'maximized' and 'fullscreen' states cannot be combined
      # with 'left', 'top', 'width' or 'height'. Leaves unspecified fields unchanged.
      Bounds bounds

  # Set dock tile details, platform-specific.
  experimental command setDockTile
    parameters
      optional string badgeLabel
      # Png encoded image.
      optional binary image

  # Invoke custom browser commands used by telemetry.
  experimental command executeBrowserCommand
    parameters
      BrowserCommandId commandId

  # Allows a site to use privacy sandbox features that require enrollment
  # without the site actually being enrolled. Only supported on page targets.
  command addPrivacySandboxEnrollmentOverride
    parameters
      string url

# This domain exposes CSS read/write operations. All CSS objects (stylesheets, rules, and styles)
# have an associated `id` used in subsequent operations on the related object. Each object type has
# a specific `id` structure, and those are not interchangeable between objects of different kinds.
# CSS objects can be loaded using the `get*ForNode()` calls (which accept a DOM node id). A client
# can also keep track of stylesheets via the `styleSheetAdded`/`styleSheetRemoved` events and
# subsequently load the required stylesheet contents using the `getStyleSheet[Text]()` methods.
experimental domain CSS
  depends on DOM
  depends on Page

  type StyleSheetId extends string

  # Stylesheet type: "injected" for stylesheets injected via extension, "user-agent" for user-agent
  # stylesheets, "inspector" for stylesheets created by the inspector (i.e. those holding the "via
  # inspector" rules), "regular" for regular stylesheets.
  type StyleSheetOrigin extends string
    enum
      injected
      user-agent
      inspector
      regular

  # CSS rule collection for a single pseudo style.
  type PseudoElementMatches extends object
    properties
      # Pseudo element type.
      DOM.PseudoType pseudoType
      # Pseudo element custom ident.
      optional string pseudoIdentifier
      # Matches of CSS rules applicable to the pseudo style.
      array of RuleMatch matches

  # Inherited CSS rule collection from ancestor node.
  type InheritedStyleEntry extends object
    properties
      # The ancestor node's inline style, if any, in the style inheritance chain.
      optional CSSStyle inlineStyle
      # Matches of CSS rules matching the ancestor node in the style inheritance chain.
      array of RuleMatch matchedCSSRules

  # Inherited pseudo element matches from pseudos of an ancestor node.
  type InheritedPseudoElementMatches extends object
    properties
      # Matches of pseudo styles from the pseudos of an ancestor node.
      array of PseudoElementMatches pseudoElements

  # Match data for a CSS rule.
  type RuleMatch extends object
    properties
      # CSS rule in the match.
      CSSRule rule
      # Matching selector indices in the rule's selectorList selectors (0-based).
      array of integer matchingSelectors

  # Data for a simple selector (these are delimited by commas in a selector list).
  type Value extends object
    properties
      # Value text.
      string text
      # Value range in the underlying resource (if available).
      optional SourceRange range
      # Specificity of the selector.
      experimental optional Specificity specificity

  # Specificity:
  # https://drafts.csswg.org/selectors/#specificity-rules
  experimental type Specificity extends object
    properties
      # The a component, which represents the number of ID selectors.
      integer a
      # The b component, which represents the number of class selectors, attributes selectors, and
      # pseudo-classes.
      integer b
      # The c component, which represents the number of type selectors and pseudo-elements.
      integer c

  # Selector list data.
  type SelectorList extends object
    properties
      # Selectors in the list.
      array of Value selectors
      # Rule selector text.
      string text

  # CSS stylesheet metainformation.
  type CSSStyleSheetHeader extends object
    properties
      # The stylesheet identifier.
      StyleSheetId styleSheetId
      # Owner frame identifier.
      Page.FrameId frameId
      # Stylesheet resource URL. Empty if this is a constructed stylesheet created using
      # new CSSStyleSheet() (but non-empty if this is a constructed sylesheet imported
      # as a CSS module script).
      string sourceURL
      # URL of source map associated with the stylesheet (if any).
      optional string sourceMapURL
      # Stylesheet origin.
      StyleSheetOrigin origin
      # Stylesheet title.
      string title
      # The backend id for the owner node of the stylesheet.
      optional DOM.BackendNodeId ownerNode
      # Denotes whether the stylesheet is disabled.
      boolean disabled
      # Whether the sourceURL field value comes from the sourceURL comment.
      optional boolean hasSourceURL
      # Whether this stylesheet is created for STYLE tag by parser. This flag is not set for
      # document.written STYLE tags.
      boolean isInline
      # Whether this stylesheet is mutable. Inline stylesheets become mutable
      # after they have been modified via CSSOM API.
      # `<link>` element's stylesheets become mutable only if DevTools modifies them.
      # Constructed stylesheets (new CSSStyleSheet()) are mutable immediately after creation.
      boolean isMutable
      # True if this stylesheet is created through new CSSStyleSheet() or imported as a
      # CSS module script.
      boolean isConstructed
      # Line offset of the stylesheet within the resource (zero based).
      number startLine
      # Column offset of the stylesheet within the resource (zero based).
      number startColumn
      # Size of the content (in characters).
      number length
      # Line offset of the end of the stylesheet within the resource (zero based).
      number endLine
      # Column offset of the end of the stylesheet within the resource (zero based).
      number endColumn
      # If the style sheet was loaded from a network resource, this indicates when the resource failed to load
      experimental optional boolean loadingFailed

  # CSS rule representation.
  type CSSRule extends object
    properties
      # The css style sheet identifier (absent for user agent stylesheet and user-specified
      # stylesheet rules) this rule came from.
      optional StyleSheetId styleSheetId
      # Rule selector data.
      SelectorList selectorList
      # Array of selectors from ancestor style rules, sorted by distance from the current rule.
      experimental optional array of string nestingSelectors
      # Parent stylesheet's origin.
      StyleSheetOrigin origin
      # Associated style declaration.
      CSSStyle style
      # Media list array (for rules involving media queries). The array enumerates media queries
      # starting with the innermost one, going outwards.
      optional array of CSSMedia media
      # Container query list array (for rules involving container queries).
      # The array enumerates container queries starting with the innermost one, going outwards.
      experimental optional array of CSSContainerQuery containerQueries
      # @supports CSS at-rule array.
      # The array enumerates @supports at-rules starting with the innermost one, going outwards.
      experimental optional array of CSSSupports supports
      # Cascade layer array. Contains the layer hierarchy that this rule belongs to starting
      # with the innermost layer and going outwards.
      experimental optional array of CSSLayer layers
      # @scope CSS at-rule array.
      # The array enumerates @scope at-rules starting with the innermost one, going outwards.
      experimental optional array of CSSScope scopes
      # The array keeps the types of ancestor CSSRules from the innermost going outwards.
      experimental optional array of CSSRuleType ruleTypes

  # Enum indicating the type of a CSS rule, used to represent the order of a style rule's ancestors.
  # This list only contains rule types that are collected during the ancestor rule collection.
  experimental type CSSRuleType extends string
    enum
      MediaRule
      SupportsRule
      ContainerRule
      LayerRule
      ScopeRule
      StyleRule

  # CSS coverage information.
  type RuleUsage extends object
    properties
      # The css style sheet identifier (absent for user agent stylesheet and user-specified
      # stylesheet rules) this rule came from.
      StyleSheetId styleSheetId
      # Offset of the start of the rule (including selector) from the beginning of the stylesheet.
      number startOffset
      # Offset of the end of the rule body from the beginning of the stylesheet.
      number endOffset
      # Indicates whether the rule was actually used by some element in the page.
      boolean used

  # Text range within a resource. All numbers are zero-based.
  type SourceRange extends object
    properties
      # Start line of range.
      integer startLine
      # Start column of range (inclusive).
      integer startColumn
      # End line of range
      integer endLine
      # End column of range (exclusive).
      integer endColumn

  type ShorthandEntry extends object
    properties
      # Shorthand name.
      string name
      # Shorthand value.
      string value
      # Whether the property has "!important" annotation (implies `false` if absent).
      optional boolean important

  type CSSComputedStyleProperty extends object
    properties
      # Computed style property name.
      string name
      # Computed style property value.
      string value

  # CSS style representation.
  type CSSStyle extends object
    properties
      # The css style sheet identifier (absent for user agent stylesheet and user-specified
      # stylesheet rules) this rule came from.
      optional StyleSheetId styleSheetId
      # CSS properties in the style.
      array of CSSProperty cssProperties
      # Computed values for all shorthands found in the style.
      array of ShorthandEntry shorthandEntries
      # Style declaration text (if available).
      optional string cssText
      # Style declaration range in the enclosing stylesheet (if available).
      optional SourceRange range

  # CSS property declaration data.
  type CSSProperty extends object
    properties
      # The property name.
      string name
      # The property value.
      string value
      # Whether the property has "!important" annotation (implies `false` if absent).
      optional boolean important
      # Whether the property is implicit (implies `false` if absent).
      optional boolean implicit
      # The full property text as specified in the style.
      optional string text
      # Whether the property is understood by the browser (implies `true` if absent).
      optional boolean parsedOk
      # Whether the property is disabled by the user (present for source-based properties only).
      optional boolean disabled
      # The entire property range in the enclosing style declaration (if available).
      optional SourceRange range
      # Parsed longhand components of this property if it is a shorthand.
      # This field will be empty if the given property is not a shorthand.
      experimental optional array of CSSProperty longhandProperties

  # CSS media rule descriptor.
  type CSSMedia extends object
    properties
      # Media query text.
      string text
      # Source of the media query: "mediaRule" if specified by a @media rule, "importRule" if
      # specified by an @import rule, "linkedSheet" if specified by a "media" attribute in a linked
      # stylesheet's LINK tag, "inlineSheet" if specified by a "media" attribute in an inline
      # stylesheet's STYLE tag.
      enum source
        mediaRule
        importRule
        linkedSheet
        inlineSheet
      # URL of the document containing the media query description.
      optional string sourceURL
      # The associated rule (@media or @import) header range in the enclosing stylesheet (if
      # available).
      optional SourceRange range
      # Identifier of the stylesheet containing this object (if exists).
      optional StyleSheetId styleSheetId
      # Array of media queries.
      optional array of MediaQuery mediaList

  # Media query descriptor.
  type MediaQuery extends object
    properties
      # Array of media query expressions.
      array of MediaQueryExpression expressions
      # Whether the media query condition is satisfied.
      boolean active

  # Media query expression descriptor.
  type MediaQueryExpression extends object
    properties
      # Media query expression value.
      number value
      # Media query expression units.
      string unit
      # Media query expression feature.
      string feature
      # The associated range of the value text in the enclosing stylesheet (if available).
      optional SourceRange valueRange
      # Computed length of media query expression (if applicable).
      optional number computedLength

  # CSS container query rule descriptor.
  experimental type CSSContainerQuery extends object
    properties
      # Container query text.
      string text
      # The associated rule header range in the enclosing stylesheet (if
      # available).
      optional SourceRange range
      # Identifier of the stylesheet containing this object (if exists).
      optional StyleSheetId styleSheetId
      # Optional name for the container.
      optional string name
      # Optional physical axes queried for the container.
      optional DOM.PhysicalAxes physicalAxes
      # Optional logical axes queried for the container.
      optional DOM.LogicalAxes logicalAxes

  # CSS Supports at-rule descriptor.
  experimental type CSSSupports extends object
    properties
      # Supports rule text.
      string text
      # Whether the supports condition is satisfied.
      boolean active
      # The associated rule header range in the enclosing stylesheet (if
      # available).
      optional SourceRange range
      # Identifier of the stylesheet containing this object (if exists).
      optional StyleSheetId styleSheetId

  # CSS Scope at-rule descriptor.
  experimental type CSSScope extends object
    properties
      # Scope rule text.
      string text
      # The associated rule header range in the enclosing stylesheet (if
      # available).
      optional SourceRange range
      # Identifier of the stylesheet containing this object (if exists).
      optional StyleSheetId styleSheetId

  # CSS Layer at-rule descriptor.
  experimental type CSSLayer extends object
    properties
      # Layer name.
      string text
      # The associated rule header range in the enclosing stylesheet (if
      # available).
      optional SourceRange range
      # Identifier of the stylesheet containing this object (if exists).
      optional StyleSheetId styleSheetId

  # CSS Layer data.
  experimental type CSSLayerData extends object
    properties
      # Layer name.
      string name
      # Direct sub-layers
      optional array of CSSLayerData subLayers
      # Layer order. The order determines the order of the layer in the cascade order.
      # A higher number has higher priority in the cascade order.
      number order

  # Information about amount of glyphs that were rendered with given font.
  type PlatformFontUsage extends object
    properties
      # Font's family name reported by platform.
      string familyName
      # Font's PostScript name reported by platform.
      string postScriptName
      # Indicates if the font was downloaded or resolved locally.
      boolean isCustomFont
      # Amount of glyphs that were rendered with this font.
      number glyphCount

  # Information about font variation axes for variable fonts
  type FontVariationAxis extends object
    properties
      # The font-variation-setting tag (a.k.a. "axis tag").
      string tag
      # Human-readable variation name in the default language (normally, "en").
      string name
      # The minimum value (inclusive) the font supports for this tag.
      number minValue
      # The maximum value (inclusive) the font supports for this tag.
      number maxValue
      # The default value.
      number defaultValue

  # Properties of a web font: https://www.w3.org/TR/2008/REC-CSS2-20080411/fonts.html#font-descriptions
  # and additional information such as platformFontFamily and fontVariationAxes.
  type FontFace extends object
    properties
      # The font-family.
      string fontFamily
      # The font-style.
      string fontStyle
      # The font-variant.
      string fontVariant
      # The font-weight.
      string fontWeight
      # The font-stretch.
      string fontStretch
      # The font-display.
      string fontDisplay
      # The unicode-range.
      string unicodeRange
      # The src.
      string src
      # The resolved platform font family
      string platformFontFamily
      # Available variation settings (a.k.a. "axes").
      optional array of FontVariationAxis fontVariationAxes

  # CSS try rule representation.
  type CSSTryRule extends object
    properties
      # The css style sheet identifier (absent for user agent stylesheet and user-specified
      # stylesheet rules) this rule came from.
      optional StyleSheetId styleSheetId
      # Parent stylesheet's origin.
      StyleSheetOrigin origin
      # Associated style declaration.
      CSSStyle style

  # CSS position-fallback rule representation.
  type CSSPositionFallbackRule extends object
    properties
      Value name
      # List of keyframes.
      array of CSSTryRule tryRules

  # CSS keyframes rule representation.
  type CSSKeyframesRule extends object
    properties
      # Animation name.
      Value animationName
      # List of keyframes.
      array of CSSKeyframeRule keyframes

  # Representation of a custom property registration through CSS.registerProperty
  type CSSPropertyRegistration extends object
    properties
      string propertyName
      optional Value initialValue
      boolean inherits
      string syntax


  # CSS font-palette-values rule representation.
  type CSSFontPaletteValuesRule extends object
    properties
      # The css style sheet identifier (absent for user agent stylesheet and user-specified
      # stylesheet rules) this rule came from.
      optional StyleSheetId styleSheetId
      # Parent stylesheet's origin.
      StyleSheetOrigin origin
      # Associated font palette name.
      Value fontPaletteName
      # Associated style declaration.
      CSSStyle style

  # CSS property at-rule representation.
  type CSSPropertyRule extends object
    properties
      # The css style sheet identifier (absent for user agent stylesheet and user-specified
      # stylesheet rules) this rule came from.
      optional StyleSheetId styleSheetId
      # Parent stylesheet's origin.
      StyleSheetOrigin origin
      # Associated property name.
      Value propertyName
      # Associated style declaration.
      CSSStyle style

  # CSS keyframe rule representation.
  type CSSKeyframeRule extends object
    properties
      # The css style sheet identifier (absent for user agent stylesheet and user-specified
      # stylesheet rules) this rule came from.
      optional StyleSheetId styleSheetId
      # Parent stylesheet's origin.
      StyleSheetOrigin origin
      # Associated key text.
      Value keyText
      # Associated style declaration.
      CSSStyle style

  # A descriptor of operation to mutate style declaration text.
  type StyleDeclarationEdit extends object
    properties
      # The css style sheet identifier.
      StyleSheetId styleSheetId
      # The range of the style text in the enclosing stylesheet.
      SourceRange range
      # New style text.
      string text

  # Inserts a new rule with the given `ruleText` in a stylesheet with given `styleSheetId`, at the
  # position specified by `location`.
  command addRule
    parameters
      # The css style sheet identifier where a new rule should be inserted.
      StyleSheetId styleSheetId
      # The text of a new rule.
      string ruleText
      # Text position of a new rule in the target style sheet.
      SourceRange location
    returns
      # The newly created rule.
      CSSRule rule

  # Returns all class names from specified stylesheet.
  command collectClassNames
    parameters
      StyleSheetId styleSheetId
    returns
      # Class name list.
      array of string classNames

  # Creates a new special "via-inspector" stylesheet in the frame with given `frameId`.
  command createStyleSheet
    parameters
      # Identifier of the frame where "via-inspector" stylesheet should be created.
      Page.FrameId frameId
    returns
      # Identifier of the created "via-inspector" stylesheet.
      StyleSheetId styleSheetId

  # Disables the CSS agent for the given page.
  command disable

  # Enables the CSS agent for the given page. Clients should not assume that the CSS agent has been
  # enabled until the result of this command is received.
  command enable

  # Ensures that the given node will have specified pseudo-classes whenever its style is computed by
  # the browser.
  command forcePseudoState
    parameters
      # The element id for which to force the pseudo state.
      DOM.NodeId nodeId
      # Element pseudo classes to force when computing the element's style.
      array of string forcedPseudoClasses

  command getBackgroundColors
    parameters
      # Id of the node to get background colors for.
      DOM.NodeId nodeId
    returns
      # The range of background colors behind this element, if it contains any visible text. If no
      # visible text is present, this will be undefined. In the case of a flat background color,
      # this will consist of simply that color. In the case of a gradient, this will consist of each
      # of the color stops. For anything more complicated, this will be an empty array. Images will
      # be ignored (as if the image had failed to load).
      optional array of string backgroundColors
      # The computed font size for this node, as a CSS computed value string (e.g. '12px').
      optional string computedFontSize
      # The computed font weight for this node, as a CSS computed value string (e.g. 'normal' or
      # '100').
      optional string computedFontWeight

  # Returns the computed style for a DOM node identified by `nodeId`.
  command getComputedStyleForNode
    parameters
      DOM.NodeId nodeId
    returns
      # Computed style for the specified DOM node.
      array of CSSComputedStyleProperty computedStyle

  # Returns the styles defined inline (explicitly in the "style" attribute and implicitly, using DOM
  # attributes) for a DOM node identified by `nodeId`.
  command getInlineStylesForNode
    parameters
      DOM.NodeId nodeId
    returns
      # Inline style for the specified DOM node.
      optional CSSStyle inlineStyle
      # Attribute-defined element style (e.g. resulting from "width=20 height=100%").
      optional CSSStyle attributesStyle

  # Returns requested styles for a DOM node identified by `nodeId`.
  command getMatchedStylesForNode
    parameters
      DOM.NodeId nodeId
    returns
      # Inline style for the specified DOM node.
      optional CSSStyle inlineStyle
      # Attribute-defined element style (e.g. resulting from "width=20 height=100%").
      optional CSSStyle attributesStyle
      # CSS rules matching this node, from all applicable stylesheets.
      optional array of RuleMatch matchedCSSRules
      # Pseudo style matches for this node.
      optional array of PseudoElementMatches pseudoElements
      # A chain of inherited styles (from the immediate node parent up to the DOM tree root).
      optional array of InheritedStyleEntry inherited
      # A chain of inherited pseudo element styles (from the immediate node parent up to the DOM tree root).
      optional array of InheritedPseudoElementMatches inheritedPseudoElements
      # A list of CSS keyframed animations matching this node.
      optional array of CSSKeyframesRule cssKeyframesRules
      # A list of CSS position fallbacks matching this node.
      optional array of CSSPositionFallbackRule cssPositionFallbackRules
      # A list of CSS at-property rules matching this node.
      optional array of CSSPropertyRule cssPropertyRules
      # A list of CSS property registrations matching this node.
      optional array of CSSPropertyRegistration cssPropertyRegistrations
      # A font-palette-values rule matching this node.
      optional CSSFontPaletteValuesRule cssFontPaletteValuesRule
      # Id of the first parent element that does not have display: contents.
      experimental optional DOM.NodeId parentLayoutNodeId

  # Returns all media queries parsed by the rendering engine.
  command getMediaQueries
    returns
      array of CSSMedia medias

  # Requests information about platform fonts which we used to render child TextNodes in the given
  # node.
  command getPlatformFontsForNode
    parameters
      DOM.NodeId nodeId
    returns
      # Usage statistics for every employed platform font.
      array of PlatformFontUsage fonts

  # Returns the current textual content for a stylesheet.
  command getStyleSheetText
    parameters
      StyleSheetId styleSheetId
    returns
      # The stylesheet text.
      string text

  # Returns all layers parsed by the rendering engine for the tree scope of a node.
  # Given a DOM element identified by nodeId, getLayersForNode returns the root
  # layer for the nearest ancestor document or shadow root. The layer root contains
  # the full layer tree for the tree scope and their ordering.
  experimental command getLayersForNode
    parameters
      DOM.NodeId nodeId
    returns
      CSSLayerData rootLayer

  # Starts tracking the given computed styles for updates. The specified array of properties
  # replaces the one previously specified. Pass empty array to disable tracking.
  # Use takeComputedStyleUpdates to retrieve the list of nodes that had properties modified.
  # The changes to computed style properties are only tracked for nodes pushed to the front-end
  # by the DOM agent. If no changes to the tracked properties occur after the node has been pushed
  # to the front-end, no updates will be issued for the node.
  experimental command trackComputedStyleUpdates
    parameters
      array of CSSComputedStyleProperty propertiesToTrack

  # Polls the next batch of computed style updates.
  experimental command takeComputedStyleUpdates
    returns
      # The list of node Ids that have their tracked computed styles updated.
      array of DOM.NodeId nodeIds

  # Find a rule with the given active property for the given node and set the new value for this
  # property
  command setEffectivePropertyValueForNode
    parameters
      # The element id for which to set property.
      DOM.NodeId nodeId
      string propertyName
      string value

  # Modifies the property rule property name.
  command setPropertyRulePropertyName
    parameters
      StyleSheetId styleSheetId
      SourceRange range
      string propertyName
    returns
      # The resulting key text after modification.
      Value propertyName

  # Modifies the keyframe rule key text.
  command setKeyframeKey
    parameters
      StyleSheetId styleSheetId
      SourceRange range
      string keyText
    returns
      # The resulting key text after modification.
      Value keyText

  # Modifies the rule selector.
  command setMediaText
    parameters
      StyleSheetId styleSheetId
      SourceRange range
      string text
    returns
      # The resulting CSS media rule after modification.
      CSSMedia media

  # Modifies the expression of a container query.
  experimental command setContainerQueryText
    parameters
      StyleSheetId styleSheetId
      SourceRange range
      string text
    returns
      # The resulting CSS container query rule after modification.
      CSSContainerQuery containerQuery

  # Modifies the expression of a supports at-rule.
  experimental command setSupportsText
    parameters
      StyleSheetId styleSheetId
      SourceRange range
      string text
    returns
      # The resulting CSS Supports rule after modification.
      CSSSupports supports

  # Modifies the expression of a scope at-rule.
  experimental command setScopeText
    parameters
      StyleSheetId styleSheetId
      SourceRange range
      string text
    returns
      # The resulting CSS Scope rule after modification.
      CSSScope scope

  # Modifies the rule selector.
  command setRuleSelector
    parameters
      StyleSheetId styleSheetId
      SourceRange range
      string selector
    returns
      # The resulting selector list after modification.
      SelectorList selectorList

  # Sets the new stylesheet text.
  command setStyleSheetText
    parameters
      StyleSheetId styleSheetId
      string text
    returns
      # URL of source map associated with script (if any).
      optional string sourceMapURL

  # Applies specified style edits one after another in the given order.
  command setStyleTexts
    parameters
      array of StyleDeclarationEdit edits
    returns
      # The resulting styles after modification.
      array of CSSStyle styles

  # Enables the selector recording.
  command startRuleUsageTracking

  # Stop tracking rule usage and return the list of rules that were used since last call to
  # `takeCoverageDelta` (or since start of coverage instrumentation).
  command stopRuleUsageTracking
    returns
      array of RuleUsage ruleUsage

  # Obtain list of rules that became used since last call to this method (or since start of coverage
  # instrumentation).
  command takeCoverageDelta
    returns
      array of RuleUsage coverage
      # Monotonically increasing time, in seconds.
      number timestamp

  # Enables/disables rendering of local CSS fonts (enabled by default).
  experimental command setLocalFontsEnabled
    parameters
      # Whether rendering of local fonts is enabled.
      boolean enabled

  # Fires whenever a web font is updated.  A non-empty font parameter indicates a successfully loaded
  # web font.
  event fontsUpdated
    parameters
      # The web font that has loaded.
      optional FontFace font

  # Fires whenever a MediaQuery result changes (for example, after a browser window has been
  # resized.) The current implementation considers only viewport-dependent media features.
  event mediaQueryResultChanged

  # Fired whenever an active document stylesheet is added.
  event styleSheetAdded
    parameters
      # Added stylesheet metainfo.
      CSSStyleSheetHeader header

  # Fired whenever a stylesheet is changed as a result of the client operation.
  event styleSheetChanged
    parameters
      StyleSheetId styleSheetId

  # Fired whenever an active document stylesheet is removed.
  event styleSheetRemoved
    parameters
      # Identifier of the removed stylesheet.
      StyleSheetId styleSheetId

experimental domain CacheStorage
  depends on Storage

  # Unique identifier of the Cache object.
  type CacheId extends string

  # type of HTTP response cached
  type CachedResponseType extends string
    enum
      basic
      cors
      default
      error
      opaqueResponse
      opaqueRedirect

  # Data entry.
  type DataEntry extends object
    properties
      # Request URL.
      string requestURL
      # Request method.
      string requestMethod
      # Request headers
      array of Header requestHeaders
      # Number of seconds since epoch.
      number responseTime
      # HTTP response status code.
      integer responseStatus
      # HTTP response status text.
      string responseStatusText
      # HTTP response type
      CachedResponseType responseType
      # Response headers
      array of Header responseHeaders

  # Cache identifier.
  type Cache extends object
    properties
      # An opaque unique id of the cache.
      CacheId cacheId
      # Security origin of the cache.
      string securityOrigin
      # Storage key of the cache.
      string storageKey
      # Storage bucket of the cache.
      optional Storage.StorageBucket storageBucket
      # The name of the cache.
      string cacheName

  type Header extends object
    properties
      string name
      string value

  # Cached response
  type CachedResponse extends object
    properties
      # Entry content, base64-encoded.
      binary body

  # Deletes a cache.
  command deleteCache
    parameters
      # Id of cache for deletion.
      CacheId cacheId

  # Deletes a cache entry.
  command deleteEntry
    parameters
      # Id of cache where the entry will be deleted.
      CacheId cacheId
      # URL spec of the request.
      string request

  # Requests cache names.
  command requestCacheNames
    parameters
      # At least and at most one of securityOrigin, storageKey, storageBucket must be specified.
      # Security origin.
      optional string securityOrigin
      # Storage key.
      optional string storageKey
      # Storage bucket. If not specified, it uses the default bucket.
      optional Storage.StorageBucket storageBucket
    returns
      # Caches for the security origin.
      array of Cache caches

  # Fetches cache entry.
  command requestCachedResponse
    parameters
      # Id of cache that contains the entry.
      CacheId cacheId
      # URL spec of the request.
      string requestURL
      # headers of the request.
      array of Header requestHeaders
    returns
      # Response read from the cache.
      CachedResponse response

  # Requests data from cache.
  command requestEntries
    parameters
      # ID of cache to get entries from.
      CacheId cacheId
      # Number of records to skip.
      optional integer skipCount
      # Number of records to fetch.
      optional integer pageSize
      # If present, only return the entries containing this substring in the path
      optional string pathFilter
    returns
      # Array of object store data entries.
      array of DataEntry cacheDataEntries
      # Count of returned entries from this storage. If pathFilter is empty, it
      # is the count of all entries from this storage.
      number returnCount

# A domain for interacting with Cast, Presentation API, and Remote Playback API
# functionalities.
experimental domain Cast

  type Sink extends object
    properties
      string name
      string id
      # Text describing the current session. Present only if there is an active
      # session on the sink.
      optional string session

  # Starts observing for sinks that can be used for tab mirroring, and if set,
  # sinks compatible with |presentationUrl| as well. When sinks are found, a
  # |sinksUpdated| event is fired.
  # Also starts observing for issue messages. When an issue is added or removed,
  # an |issueUpdated| event is fired.
  command enable
    parameters
      optional string presentationUrl

  # Stops observing for sinks and issues.
  command disable

  # Sets a sink to be used when the web page requests the browser to choose a
  # sink via Presentation API, Remote Playback API, or Cast SDK.
  command setSinkToUse
    parameters
      string sinkName

  # Starts mirroring the desktop to the sink.
  command startDesktopMirroring
    parameters
      string sinkName

  # Starts mirroring the tab to the sink.
  command startTabMirroring
    parameters
      string sinkName

  # Stops the active Cast session on the sink.
  command stopCasting
    parameters
      string sinkName

  # This is fired whenever the list of available sinks changes. A sink is a
  # device or a software surface that you can cast to.
  event sinksUpdated
    parameters
      array of Sink sinks

  # This is fired whenever the outstanding issue/error message changes.
  # |issueMessage| is empty if there is no issue.
  event issueUpdated
    parameters
      string issueMessage


# This domain exposes DOM read/write operations. Each DOM Node is represented with its mirror object
# that has an `id`. This `id` can be used to get additional information on the Node, resolve it into
# the JavaScript object wrapper, etc. It is important that client receives DOM events only for the
# nodes that are known to the client. Backend keeps track of the nodes that were sent to the client
# and never sends the same node twice. It is client's responsibility to collect information about
# the nodes that were sent to the client. Note that `iframe` owner elements will return
# corresponding document elements as their child nodes.
domain DOM
  depends on Runtime

  # Unique DOM node identifier.
  type NodeId extends integer

  # Unique DOM node identifier used to reference a node that may not have been pushed to the
  # front-end.
  type BackendNodeId extends integer

  # Backend node with a friendly name.
  type BackendNode extends object
    properties
      # `Node`'s nodeType.
      integer nodeType
      # `Node`'s nodeName.
      string nodeName
      BackendNodeId backendNodeId

  # Pseudo element type.
  type PseudoType extends string
    enum
      first-line
      first-letter
      before
      after
      marker
      backdrop
      selection
      target-text
      spelling-error
      grammar-error
      highlight
      first-line-inherited
      scrollbar
      scrollbar-thumb
      scrollbar-button
      scrollbar-track
      scrollbar-track-piece
      scrollbar-corner
      resizer
      input-list-button
      view-transition
      view-transition-group
      view-transition-image-pair
      view-transition-old
      view-transition-new

  # Shadow root type.
  type ShadowRootType extends string
    enum
      user-agent
      open
      closed

  # Document compatibility mode.
  type CompatibilityMode extends string
    enum
      QuirksMode
      LimitedQuirksMode
      NoQuirksMode

  # ContainerSelector physical axes
  type PhysicalAxes extends string
    enum
      Horizontal
      Vertical
      Both

  # ContainerSelector logical axes
  type LogicalAxes extends string
    enum
      Inline
      Block
      Both

  # DOM interaction is implemented in terms of mirror objects that represent the actual DOM nodes.
  # DOMNode is a base node mirror type.
  type Node extends object
    properties
      # Node identifier that is passed into the rest of the DOM messages as the `nodeId`. Backend
      # will only push node with given `id` once. It is aware of all requested nodes and will only
      # fire DOM events for nodes known to the client.
      NodeId nodeId
      # The id of the parent node if any.
      optional NodeId parentId
      # The BackendNodeId for this node.
      BackendNodeId backendNodeId
      # `Node`'s nodeType.
      integer nodeType
      # `Node`'s nodeName.
      string nodeName
      # `Node`'s localName.
      string localName
      # `Node`'s nodeValue.
      string nodeValue
      # Child count for `Container` nodes.
      optional integer childNodeCount
      # Child nodes of this node when requested with children.
      optional array of Node children
      # Attributes of the `Element` node in the form of flat array `[name1, value1, name2, value2]`.
      optional array of string attributes
      # Document URL that `Document` or `FrameOwner` node points to.
      optional string documentURL
      # Base URL that `Document` or `FrameOwner` node uses for URL completion.
      optional string baseURL
      # `DocumentType`'s publicId.
      optional string publicId
      # `DocumentType`'s systemId.
      optional string systemId
      # `DocumentType`'s internalSubset.
      optional string internalSubset
      # `Document`'s XML version in case of XML documents.
      optional string xmlVersion
      # `Attr`'s name.
      optional string name
      # `Attr`'s value.
      optional string value
      # Pseudo element type for this node.
      optional PseudoType pseudoType
      # Pseudo element identifier for this node. Only present if there is a
      # valid pseudoType.
      optional string pseudoIdentifier
      # Shadow root type.
      optional ShadowRootType shadowRootType
      # Frame ID for frame owner elements.
      optional Page.FrameId frameId
      # Content document for frame owner elements.
      optional Node contentDocument
      # Shadow root list for given element host.
      optional array of Node shadowRoots
      # Content document fragment for template elements.
      optional Node templateContent
      # Pseudo elements associated with this node.
      optional array of Node pseudoElements
      # Deprecated, as the HTML Imports API has been removed (crbug.com/937746).
      # This property used to return the imported document for the HTMLImport links.
      # The property is always undefined now.
      deprecated optional Node importedDocument
      # Distributed nodes for given insertion point.
      optional array of BackendNode distributedNodes
      # Whether the node is SVG.
      optional boolean isSVG
      optional CompatibilityMode compatibilityMode
      optional BackendNode assignedSlot

  # A structure holding an RGBA color.
  type RGBA extends object
    properties
      # The red component, in the [0-255] range.
      integer r
      # The green component, in the [0-255] range.
      integer g
      # The blue component, in the [0-255] range.
      integer b
      # The alpha component, in the [0-1] range (default: 1).
      optional number a

  # An array of quad vertices, x immediately followed by y for each point, points clock-wise.
  type Quad extends array of number

  # Box model.
  type BoxModel extends object
    properties
      # Content box
      Quad content
      # Padding box
      Quad padding
      # Border box
      Quad border
      # Margin box
      Quad margin
      # Node width
      integer width
      # Node height
      integer height
      # Shape outside coordinates
      optional ShapeOutsideInfo shapeOutside

  # CSS Shape Outside details.
  type ShapeOutsideInfo extends object
    properties
      # Shape bounds
      Quad bounds
      # Shape coordinate details
      array of any shape
      # Margin shape bounds
      array of any marginShape

  # Rectangle.
  type Rect extends object
    properties
      # X coordinate
      number x
      # Y coordinate
      number y
      # Rectangle width
      number width
      # Rectangle height
      number height

  type CSSComputedStyleProperty extends object
    properties
      # Computed style property name.
      string name
      # Computed style property value.
      string value

  # Collects class names for the node with given id and all of it's child nodes.
  experimental command collectClassNamesFromSubtree
    parameters
      # Id of the node to collect class names.
      NodeId nodeId
    returns
      # Class name list.
      array of string classNames

  # Creates a deep copy of the specified node and places it into the target container before the
  # given anchor.
  experimental command copyTo
    parameters
      # Id of the node to copy.
      NodeId nodeId
      # Id of the element to drop the copy into.
      NodeId targetNodeId
      # Drop the copy before this node (if absent, the copy becomes the last child of
      # `targetNodeId`).
      optional NodeId insertBeforeNodeId
    returns
      # Id of the node clone.
      NodeId nodeId

  # Describes node given its id, does not require domain to be enabled. Does not start tracking any
  # objects, can be used for automation.
  command describeNode
    parameters
      # Identifier of the node.
      optional NodeId nodeId
      # Identifier of the backend node.
      optional BackendNodeId backendNodeId
      # JavaScript object id of the node wrapper.
      optional Runtime.RemoteObjectId objectId
      # The maximum depth at which children should be retrieved, defaults to 1. Use -1 for the
      # entire subtree or provide an integer larger than 0.
      optional integer depth
      # Whether or not iframes and shadow roots should be traversed when returning the subtree
      # (default is false).
      optional boolean pierce
    returns
      # Node description.
      Node node

  # Scrolls the specified rect of the given node into view if not already visible.
  # Note: exactly one between nodeId, backendNodeId and objectId should be passed
  # to identify the node.
  experimental command scrollIntoViewIfNeeded
    parameters
      # Identifier of the node.
      optional NodeId nodeId
      # Identifier of the backend node.
      optional BackendNodeId backendNodeId
      # JavaScript object id of the node wrapper.
      optional Runtime.RemoteObjectId objectId
      # The rect to be scrolled into view, relative to the node's border box, in CSS pixels.
      # When omitted, center of the node will be used, similar to Element.scrollIntoView.
      optional Rect rect

  # Disables DOM agent for the given page.
  command disable

  # Discards search results from the session with the given id. `getSearchResults` should no longer
  # be called for that search.
  experimental command discardSearchResults
    parameters
      # Unique search session identifier.
      string searchId

  # Enables DOM agent for the given page.
  command enable
    parameters
      # Whether to include whitespaces in the children array of returned Nodes.
      experimental optional enum includeWhitespace
        # Strip whitespaces from child arrays (default).
        none
        # Return all children including block-level whitespace nodes.
        all

  # Focuses the given element.
  command focus
    parameters
      # Identifier of the node.
      optional NodeId nodeId
      # Identifier of the backend node.
      optional BackendNodeId backendNodeId
      # JavaScript object id of the node wrapper.
      optional Runtime.RemoteObjectId objectId

  # Returns attributes for the specified node.
  command getAttributes
    parameters
      # Id of the node to retrieve attibutes for.
      NodeId nodeId
    returns
      # An interleaved array of node attribute names and values.
      array of string attributes

  # Returns boxes for the given node.
  command getBoxModel
    parameters
      # Identifier of the node.
      optional NodeId nodeId
      # Identifier of the backend node.
      optional BackendNodeId backendNodeId
      # JavaScript object id of the node wrapper.
      optional Runtime.RemoteObjectId objectId
    returns
      # Box model for the node.
      BoxModel model

  # Returns quads that describe node position on the page. This method
  # might return multiple quads for inline nodes.
  experimental command getContentQuads
    parameters
      # Identifier of the node.
      optional NodeId nodeId
      # Identifier of the backend node.
      optional BackendNodeId backendNodeId
      # JavaScript object id of the node wrapper.
      optional Runtime.RemoteObjectId objectId
    returns
      # Quads that describe node layout relative to viewport.
      array of Quad quads

  # Returns the root DOM node (and optionally the subtree) to the caller.
  # Implicitly enables the DOM domain events for the current target.
  command getDocument
    parameters
      # The maximum depth at which children should be retrieved, defaults to 1. Use -1 for the
      # entire subtree or provide an integer larger than 0.
      optional integer depth
      # Whether or not iframes and shadow roots should be traversed when returning the subtree
      # (default is false).
      optional boolean pierce
    returns
      # Resulting node.
      Node root

  # Returns the root DOM node (and optionally the subtree) to the caller.
  # Deprecated, as it is not designed to work well with the rest of the DOM agent.
  # Use DOMSnapshot.captureSnapshot instead.
  deprecated command getFlattenedDocument
    parameters
      # The maximum depth at which children should be retrieved, defaults to 1. Use -1 for the
      # entire subtree or provide an integer larger than 0.
      optional integer depth
      # Whether or not iframes and shadow roots should be traversed when returning the subtree
      # (default is false).
      optional boolean pierce
    returns
      # Resulting node.
      array of Node nodes

  # Finds nodes with a given computed style in a subtree.
  experimental command getNodesForSubtreeByStyle
    parameters
      # Node ID pointing to the root of a subtree.
      NodeId nodeId
      # The style to filter nodes by (includes nodes if any of properties matches).
      array of CSSComputedStyleProperty computedStyles
      # Whether or not iframes and shadow roots in the same target should be traversed when returning the
      # results (default is false).
      optional boolean pierce
    returns
      # Resulting nodes.
      array of NodeId nodeIds

  # Returns node id at given location. Depending on whether DOM domain is enabled, nodeId is
  # either returned or not.
  command getNodeForLocation
    parameters
      # X coordinate.
      integer x
      # Y coordinate.
      integer y
      # False to skip to the nearest non-UA shadow root ancestor (default: false).
      optional boolean includeUserAgentShadowDOM
      # Whether to ignore pointer-events: none on elements and hit test them.
      optional boolean ignorePointerEventsNone
    returns
      # Resulting node.
      BackendNodeId backendNodeId
      # Frame this node belongs to.
      Page.FrameId frameId
      # Id of the node at given coordinates, only when enabled and requested document.
      optional NodeId nodeId

  # Returns node's HTML markup.
  command getOuterHTML
    parameters
      # Identifier of the node.
      optional NodeId nodeId
      # Identifier of the backend node.
      optional BackendNodeId backendNodeId
      # JavaScript object id of the node wrapper.
      optional Runtime.RemoteObjectId objectId
    returns
      # Outer HTML markup.
      string outerHTML

  # Returns the id of the nearest ancestor that is a relayout boundary.
  experimental command getRelayoutBoundary
    parameters
      # Id of the node.
      NodeId nodeId
    returns
      # Relayout boundary node id for the given node.
      NodeId nodeId

  # Returns search results from given `fromIndex` to given `toIndex` from the search with the given
  # identifier.
  experimental command getSearchResults
    parameters
      # Unique search session identifier.
      string searchId
      # Start index of the search result to be returned.
      integer fromIndex
      # End index of the search result to be returned.
      integer toIndex
    returns
      # Ids of the search result nodes.
      array of NodeId nodeIds

  # Hides any highlight.
  command hideHighlight
    # Use 'Overlay.hideHighlight' instead
    redirect Overlay

  # Highlights DOM node.
  command highlightNode
    # Use 'Overlay.highlightNode' instead
    redirect Overlay

  # Highlights given rectangle.
  command highlightRect
    # Use 'Overlay.highlightRect' instead
    redirect Overlay

  # Marks last undoable state.
  experimental command markUndoableState

  # Moves node into the new container, places it before the given anchor.
  command moveTo
    parameters
      # Id of the node to move.
      NodeId nodeId
      # Id of the element to drop the moved node into.
      NodeId targetNodeId
      # Drop node before this one (if absent, the moved node becomes the last child of
      # `targetNodeId`).
      optional NodeId insertBeforeNodeId
    returns
      # New id of the moved node.
      NodeId nodeId

  # Searches for a given string in the DOM tree. Use `getSearchResults` to access search results or
  # `cancelSearch` to end this search session.
  experimental command performSearch
    parameters
      # Plain text or query selector or XPath search query.
      string query
      # True to search in user agent shadow DOM.
      optional boolean includeUserAgentShadowDOM
    returns
      # Unique search session identifier.
      string searchId
      # Number of search results.
      integer resultCount

  # Requests that the node is sent to the caller given its path. // FIXME, use XPath
  experimental command pushNodeByPathToFrontend
    parameters
      # Path to node in the proprietary format.
      string path
    returns
      # Id of the node for given path.
      NodeId nodeId

  # Requests that a batch of nodes is sent to the caller given their backend node ids.
  experimental command pushNodesByBackendIdsToFrontend
    parameters
      # The array of backend node ids.
      array of BackendNodeId backendNodeIds
    returns
      # The array of ids of pushed nodes that correspond to the backend ids specified in
      # backendNodeIds.
      array of NodeId nodeIds

  # Executes `querySelector` on a given node.
  command querySelector
    parameters
      # Id of the node to query upon.
      NodeId nodeId
      # Selector string.
      string selector
    returns
      # Query selector result.
      NodeId nodeId

  # Executes `querySelectorAll` on a given node.
  command querySelectorAll
    parameters
      # Id of the node to query upon.
      NodeId nodeId
      # Selector string.
      string selector
    returns
      # Query selector result.
      array of NodeId nodeIds

  # Returns NodeIds of current top layer elements.
  # Top layer is rendered closest to the user within a viewport, therefore its elements always
  # appear on top of all other content.
  experimental command getTopLayerElements
    returns
      # NodeIds of top layer elements
      array of NodeId nodeIds

  # Re-does the last undone action.
  experimental command redo

  # Removes attribute with given name from an element with given id.
  command removeAttribute
    parameters
      # Id of the element to remove attribute from.
      NodeId nodeId
      # Name of the attribute to remove.
      string name

  # Removes node with given id.
  command removeNode
    parameters
      # Id of the node to remove.
      NodeId nodeId

  # Requests that children of the node with given id are returned to the caller in form of
  # `setChildNodes` events where not only immediate children are retrieved, but all children down to
  # the specified depth.
  command requestChildNodes
    parameters
      # Id of the node to get children for.
      NodeId nodeId
      # The maximum depth at which children should be retrieved, defaults to 1. Use -1 for the
      # entire subtree or provide an integer larger than 0.
      optional integer depth
      # Whether or not iframes and shadow roots should be traversed when returning the sub-tree
      # (default is false).
      optional boolean pierce

  # Requests that the node is sent to the caller given the JavaScript node object reference. All
  # nodes that form the path from the node to the root are also sent to the client as a series of
  # `setChildNodes` notifications.
  command requestNode
    parameters
      # JavaScript object id to convert into node.
      Runtime.RemoteObjectId objectId
    returns
      # Node id for given object.
      NodeId nodeId

  # Resolves the JavaScript node object for a given NodeId or BackendNodeId.
  command resolveNode
    parameters
      # Id of the node to resolve.
      optional NodeId nodeId
      # Backend identifier of the node to resolve.
      optional DOM.BackendNodeId backendNodeId
      # Symbolic group name that can be used to release multiple objects.
      optional string objectGroup
      # Execution context in which to resolve the node.
      optional Runtime.ExecutionContextId executionContextId
    returns
      # JavaScript object wrapper for given node.
      Runtime.RemoteObject object

  # Sets attribute for an element with given id.
  command setAttributeValue
    parameters
      # Id of the element to set attribute for.
      NodeId nodeId
      # Attribute name.
      string name
      # Attribute value.
      string value

  # Sets attributes on element with given id. This method is useful when user edits some existing
  # attribute value and types in several attribute name/value pairs.
  command setAttributesAsText
    parameters
      # Id of the element to set attributes for.
      NodeId nodeId
      # Text with a number of attributes. Will parse this text using HTML parser.
      string text
      # Attribute name to replace with new attributes derived from text in case text parsed
      # successfully.
      optional string name

  # Sets files for the given file input element.
  command setFileInputFiles
    parameters
      # Array of file paths to set.
      array of string files
      # Identifier of the node.
      optional NodeId nodeId
      # Identifier of the backend node.
      optional BackendNodeId backendNodeId
      # JavaScript object id of the node wrapper.
      optional Runtime.RemoteObjectId objectId

  # Sets if stack traces should be captured for Nodes. See `Node.getNodeStackTraces`. Default is disabled.
  experimental command setNodeStackTracesEnabled
    parameters
      # Enable or disable.
      boolean enable

  # Gets stack traces associated with a Node. As of now, only provides stack trace for Node creation.
  experimental command getNodeStackTraces
    parameters
      # Id of the node to get stack traces for.
      NodeId nodeId
    returns
      # Creation stack trace, if available.
      optional Runtime.StackTrace creation

  # Returns file information for the given
  # File wrapper.
  experimental command getFileInfo
    parameters
      # JavaScript object id of the node wrapper.
      Runtime.RemoteObjectId objectId
    returns
      string path

  # Enables console to refer to the node with given id via $x (see Command Line API for more details
  # $x functions).
  experimental command setInspectedNode
    parameters
      # DOM node id to be accessible by means of $x command line API.
      NodeId nodeId

  # Sets node name for a node with given id.
  command setNodeName
    parameters
      # Id of the node to set name for.
      NodeId nodeId
      # New node's name.
      string name
    returns
      # New node's id.
      NodeId nodeId

  # Sets node value for a node with given id.
  command setNodeValue
    parameters
      # Id of the node to set value for.
      NodeId nodeId
      # New node's value.
      string value

  # Sets node HTML markup, returns new node id.
  command setOuterHTML
    parameters
      # Id of the node to set markup for.
      NodeId nodeId
      # Outer HTML markup to set.
      string outerHTML

  # Undoes the last performed action.
  experimental command undo

  # Returns iframe node that owns iframe with the given domain.
  experimental command getFrameOwner
    parameters
      Page.FrameId frameId
    returns
      # Resulting node.
      BackendNodeId backendNodeId
      # Id of the node at given coordinates, only when enabled and requested document.
      optional NodeId nodeId

  # Returns the query container of the given node based on container query
  # conditions: containerName, physical, and logical axes. If no axes are
  # provided, the style container is returned, which is the direct parent or the
  # closest element with a matching container-name.
  experimental command getContainerForNode
    parameters
      NodeId nodeId
      optional string containerName
      optional PhysicalAxes physicalAxes
      optional LogicalAxes logicalAxes
    returns
      # The container node for the given node, or null if not found.
      optional NodeId nodeId

  # Returns the descendants of a container query container that have
  # container queries against this container.
  experimental command getQueryingDescendantsForContainer
    parameters
      # Id of the container node to find querying descendants from.
      NodeId nodeId
    returns
      # Descendant nodes with container queries against the given container.
      array of NodeId nodeIds

  # Fired when `Element`'s attribute is modified.
  event attributeModified
    parameters
      # Id of the node that has changed.
      NodeId nodeId
      # Attribute name.
      string name
      # Attribute value.
      string value

  # Fired when `Element`'s attribute is removed.
  event attributeRemoved
    parameters
      # Id of the node that has changed.
      NodeId nodeId
      # A ttribute name.
      string name

  # Mirrors `DOMCharacterDataModified` event.
  event characterDataModified
    parameters
      # Id of the node that has changed.
      NodeId nodeId
      # New text value.
      string characterData

  # Fired when `Container`'s child node count has changed.
  event childNodeCountUpdated
    parameters
      # Id of the node that has changed.
      NodeId nodeId
      # New node count.
      integer childNodeCount

  # Mirrors `DOMNodeInserted` event.
  event childNodeInserted
    parameters
      # Id of the node that has changed.
      NodeId parentNodeId
      # Id of the previous sibling.
      NodeId previousNodeId
      # Inserted node data.
      Node node

  # Mirrors `DOMNodeRemoved` event.
  event childNodeRemoved
    parameters
      # Parent id.
      NodeId parentNodeId
      # Id of the node that has been removed.
      NodeId nodeId

  # Called when distribution is changed.
  experimental event distributedNodesUpdated
    parameters
      # Insertion point where distributed nodes were updated.
      NodeId insertionPointId
      # Distributed nodes for given insertion point.
      array of BackendNode distributedNodes

  # Fired when `Document` has been totally updated. Node ids are no longer valid.
  event documentUpdated

  # Fired when `Element`'s inline style is modified via a CSS property modification.
  experimental event inlineStyleInvalidated
    parameters
      # Ids of the nodes for which the inline styles have been invalidated.
      array of NodeId nodeIds

  # Called when a pseudo element is added to an element.
  experimental event pseudoElementAdded
    parameters
      # Pseudo element's parent element id.
      NodeId parentId
      # The added pseudo element.
      Node pseudoElement

  # Called when top layer elements are changed.
  experimental event topLayerElementsUpdated

  # Called when a pseudo element is removed from an element.
  experimental event pseudoElementRemoved
    parameters
      # Pseudo element's parent element id.
      NodeId parentId
      # The removed pseudo element id.
      NodeId pseudoElementId

  # Fired when backend wants to provide client with the missing DOM structure. This happens upon
  # most of the calls requesting node ids.
  event setChildNodes
    parameters
      # Parent node id to populate with children.
      NodeId parentId
      # Child nodes array.
      array of Node nodes

  # Called when shadow root is popped from the element.
  experimental event shadowRootPopped
    parameters
      # Host element id.
      NodeId hostId
      # Shadow root id.
      NodeId rootId

  # Called when shadow root is pushed into the element.
  experimental event shadowRootPushed
    parameters
      # Host element id.
      NodeId hostId
      # Shadow root.
      Node root

# DOM debugging allows setting breakpoints on particular DOM operations and events. JavaScript
# execution will stop on these operations as if there was a regular breakpoint set.
domain DOMDebugger
  depends on DOM
  depends on Runtime

  # DOM breakpoint type.
  type DOMBreakpointType extends string
    enum
      subtree-modified
      attribute-modified
      node-removed

  # CSP Violation type.
  experimental type CSPViolationType extends string
    enum
      trustedtype-sink-violation
      trustedtype-policy-violation

  # Object event listener.
  type EventListener extends object
    properties
      # `EventListener`'s type.
      string type
      # `EventListener`'s useCapture.
      boolean useCapture
      # `EventListener`'s passive flag.
      boolean passive
      # `EventListener`'s once flag.
      boolean once
      # Script id of the handler code.
      Runtime.ScriptId scriptId
      # Line number in the script (0-based).
      integer lineNumber
      # Column number in the script (0-based).
      integer columnNumber
      # Event handler function value.
      optional Runtime.RemoteObject handler
      # Event original handler function value.
      optional Runtime.RemoteObject originalHandler
      # Node the listener is added to (if any).
      optional DOM.BackendNodeId backendNodeId

  # Returns event listeners of the given object.
  command getEventListeners
    parameters
      # Identifier of the object to return listeners for.
      Runtime.RemoteObjectId objectId
      # The maximum depth at which Node children should be retrieved, defaults to 1. Use -1 for the
      # entire subtree or provide an integer larger than 0.
      optional integer depth
      # Whether or not iframes and shadow roots should be traversed when returning the subtree
      # (default is false). Reports listeners for all contexts if pierce is enabled.
      optional boolean pierce
    returns
      # Array of relevant listeners.
      array of EventListener listeners

  # Removes DOM breakpoint that was set using `setDOMBreakpoint`.
  command removeDOMBreakpoint
    parameters
      # Identifier of the node to remove breakpoint from.
      DOM.NodeId nodeId
      # Type of the breakpoint to remove.
      DOMBreakpointType type

  # Removes breakpoint on particular DOM event.
  command removeEventListenerBreakpoint
    parameters
      # Event name.
      string eventName
      # EventTarget interface name.
      experimental optional string targetName

  # Removes breakpoint on particular native event.
  experimental deprecated command removeInstrumentationBreakpoint
    redirect EventBreakpoints
    parameters
      # Instrumentation name to stop on.
      string eventName

  # Removes breakpoint from XMLHttpRequest.
  command removeXHRBreakpoint
    parameters
      # Resource URL substring.
      string url

  # Sets breakpoint on particular CSP violations.
  experimental command setBreakOnCSPViolation
    parameters
      # CSP Violations to stop upon.
      array of CSPViolationType violationTypes

  # Sets breakpoint on particular operation with DOM.
  command setDOMBreakpoint
    parameters
      # Identifier of the node to set breakpoint on.
      DOM.NodeId nodeId
      # Type of the operation to stop upon.
      DOMBreakpointType type

  # Sets breakpoint on particular DOM event.
  command setEventListenerBreakpoint
    parameters
      # DOM Event name to stop on (any DOM event will do).
      string eventName
      # EventTarget interface name to stop on. If equal to `"*"` or not provided, will stop on any
      # EventTarget.
      experimental optional string targetName

  # Sets breakpoint on particular native event.
  experimental deprecated command setInstrumentationBreakpoint
    redirect EventBreakpoints
    parameters
      # Instrumentation name to stop on.
      string eventName

  # Sets breakpoint on XMLHttpRequest.
  command setXHRBreakpoint
    parameters
      # Resource URL substring. All XHRs having this substring in the URL will get stopped upon.
      string url

# EventBreakpoints permits setting JavaScript breakpoints on operations and events
# occurring in native code invoked from JavaScript. Once breakpoint is hit, it is
# reported through Debugger domain, similarly to regular breakpoints being hit.
experimental domain EventBreakpoints
  # Sets breakpoint on particular native event.
  command setInstrumentationBreakpoint
    parameters
      # Instrumentation name to stop on.
      string eventName

  # Removes breakpoint on particular native event.
  command removeInstrumentationBreakpoint
    parameters
      # Instrumentation name to stop on.
      string eventName

  # Removes all breakpoints
  command disable

# This domain facilitates obtaining document snapshots with DOM, layout, and style information.
experimental domain DOMSnapshot
  depends on CSS
  depends on DOM
  depends on DOMDebugger
  depends on Page

  # A Node in the DOM tree.
  type DOMNode extends object
    properties
      # `Node`'s nodeType.
      integer nodeType
      # `Node`'s nodeName.
      string nodeName
      # `Node`'s nodeValue.
      string nodeValue
      # Only set for textarea elements, contains the text value.
      optional string textValue
      # Only set for input elements, contains the input's associated text value.
      optional string inputValue
      # Only set for radio and checkbox input elements, indicates if the element has been checked
      optional boolean inputChecked
      # Only set for option elements, indicates if the element has been selected
      optional boolean optionSelected
      # `Node`'s id, corresponds to DOM.Node.backendNodeId.
      DOM.BackendNodeId backendNodeId
      # The indexes of the node's child nodes in the `domNodes` array returned by `getSnapshot`, if
      # any.
      optional array of integer childNodeIndexes
      # Attributes of an `Element` node.
      optional array of NameValue attributes
      # Indexes of pseudo elements associated with this node in the `domNodes` array returned by
      # `getSnapshot`, if any.
      optional array of integer pseudoElementIndexes
      # The index of the node's related layout tree node in the `layoutTreeNodes` array returned by
      # `getSnapshot`, if any.
      optional integer layoutNodeIndex
      # Document URL that `Document` or `FrameOwner` node points to.
      optional string documentURL
      # Base URL that `Document` or `FrameOwner` node uses for URL completion.
      optional string baseURL
      # Only set for documents, contains the document's content language.
      optional string contentLanguage
      # Only set for documents, contains the document's character set encoding.
      optional string documentEncoding
      # `DocumentType` node's publicId.
      optional string publicId
      # `DocumentType` node's systemId.
      optional string systemId
      # Frame ID for frame owner elements and also for the document node.
      optional Page.FrameId frameId
      # The index of a frame owner element's content document in the `domNodes` array returned by
      # `getSnapshot`, if any.
      optional integer contentDocumentIndex
      # Type of a pseudo element node.
      optional DOM.PseudoType pseudoType
      # Shadow root type.
      optional DOM.ShadowRootType shadowRootType
      # Whether this DOM node responds to mouse clicks. This includes nodes that have had click
      # event listeners attached via JavaScript as well as anchor tags that naturally navigate when
      # clicked.
      optional boolean isClickable
      # Details of the node's event listeners, if any.
      optional array of DOMDebugger.EventListener eventListeners
      # The selected url for nodes with a srcset attribute.
      optional string currentSourceURL
      # The url of the script (if any) that generates this node.
      optional string originURL
      # Scroll offsets, set when this node is a Document.
      optional number scrollOffsetX
      optional number scrollOffsetY

  # Details of post layout rendered text positions. The exact layout should not be regarded as
  # stable and may change between versions.
  type InlineTextBox extends object
    properties
      # The bounding box in document coordinates. Note that scroll offset of the document is ignored.
      DOM.Rect boundingBox
      # The starting index in characters, for this post layout textbox substring. Characters that
      # would be represented as a surrogate pair in UTF-16 have length 2.
      integer startCharacterIndex
      # The number of characters in this post layout textbox substring. Characters that would be
      # represented as a surrogate pair in UTF-16 have length 2.
      integer numCharacters

  # Details of an element in the DOM tree with a LayoutObject.
  type LayoutTreeNode extends object
    properties
      # The index of the related DOM node in the `domNodes` array returned by `getSnapshot`.
      integer domNodeIndex
      # The bounding box in document coordinates. Note that scroll offset of the document is ignored.
      DOM.Rect boundingBox
      # Contents of the LayoutText, if any.
      optional string layoutText
      # The post-layout inline text nodes, if any.
      optional array of InlineTextBox inlineTextNodes
      # Index into the `computedStyles` array returned by `getSnapshot`.
      optional integer styleIndex
      # Global paint order index, which is determined by the stacking order of the nodes. Nodes
      # that are painted together will have the same index. Only provided if includePaintOrder in
      # getSnapshot was true.
      optional integer paintOrder
      # Set to true to indicate the element begins a new stacking context.
      optional boolean isStackingContext

  # A subset of the full ComputedStyle as defined by the request whitelist.
  type ComputedStyle extends object
    properties
      # Name/value pairs of computed style properties.
      array of NameValue properties

  # A name/value pair.
  type NameValue extends object
    properties
      # Attribute/property name.
      string name
      # Attribute/property value.
      string value

  # Index of the string in the strings table.
  type StringIndex extends integer

  # Index of the string in the strings table.
  type ArrayOfStrings extends array of StringIndex

  # Data that is only present on rare nodes.
  type RareStringData extends object
    properties
      array of integer index
      array of StringIndex value

  type RareBooleanData extends object
    properties
      array of integer index

  type RareIntegerData extends object
    properties
      array of integer index
      array of integer value

  type Rectangle extends array of number

  # Document snapshot.
  type DocumentSnapshot extends object
    properties
      # Document URL that `Document` or `FrameOwner` node points to.
      StringIndex documentURL
      # Document title.
      StringIndex title
      # Base URL that `Document` or `FrameOwner` node uses for URL completion.
      StringIndex baseURL
      # Contains the document's content language.
      StringIndex contentLanguage
      # Contains the document's character set encoding.
      StringIndex encodingName
      # `DocumentType` node's publicId.
      StringIndex publicId
      # `DocumentType` node's systemId.
      StringIndex systemId
      # Frame ID for frame owner elements and also for the document node.
      StringIndex frameId
      # A table with dom nodes.
      NodeTreeSnapshot nodes
      # The nodes in the layout tree.
      LayoutTreeSnapshot layout
      # The post-layout inline text nodes.
      TextBoxSnapshot textBoxes
      # Horizontal scroll offset.
      optional number scrollOffsetX
      # Vertical scroll offset.
      optional number scrollOffsetY
      # Document content width.
      optional number contentWidth
      # Document content height.
      optional number contentHeight

  # Table containing nodes.
  type NodeTreeSnapshot extends object
    properties
      # Parent node index.
      optional array of integer parentIndex
      # `Node`'s nodeType.
      optional array of integer nodeType
      # Type of the shadow root the `Node` is in. String values are equal to the `ShadowRootType` enum.
      optional RareStringData shadowRootType
      # `Node`'s nodeName.
      optional array of StringIndex nodeName
      # `Node`'s nodeValue.
      optional array of StringIndex nodeValue
      # `Node`'s id, corresponds to DOM.Node.backendNodeId.
      optional array of DOM.BackendNodeId backendNodeId
      # Attributes of an `Element` node. Flatten name, value pairs.
      optional array of ArrayOfStrings attributes
      # Only set for textarea elements, contains the text value.
      optional RareStringData textValue
      # Only set for input elements, contains the input's associated text value.
      optional RareStringData inputValue
      # Only set for radio and checkbox input elements, indicates if the element has been checked
      optional RareBooleanData inputChecked
      # Only set for option elements, indicates if the element has been selected
      optional RareBooleanData optionSelected
      # The index of the document in the list of the snapshot documents.
      optional RareIntegerData contentDocumentIndex
      # Type of a pseudo element node.
      optional RareStringData pseudoType
      # Pseudo element identifier for this node. Only present if there is a
      # valid pseudoType.
      optional RareStringData pseudoIdentifier
      # Whether this DOM node responds to mouse clicks. This includes nodes that have had click
      # event listeners attached via JavaScript as well as anchor tags that naturally navigate when
      # clicked.
      optional RareBooleanData isClickable
      # The selected url for nodes with a srcset attribute.
      optional RareStringData currentSourceURL
      # The url of the script (if any) that generates this node.
      optional RareStringData originURL

  # Table of details of an element in the DOM tree with a LayoutObject.
  type LayoutTreeSnapshot extends object
    properties
      # Index of the corresponding node in the `NodeTreeSnapshot` array returned by `captureSnapshot`.
      array of integer nodeIndex
      # Array of indexes specifying computed style strings, filtered according to the `computedStyles` parameter passed to `captureSnapshot`.
      array of ArrayOfStrings styles
      # The absolute position bounding box.
      array of Rectangle bounds
      # Contents of the LayoutText, if any.
      array of StringIndex text
      # Stacking context information.
      RareBooleanData stackingContexts
      # Global paint order index, which is determined by the stacking order of the nodes. Nodes
      # that are painted together will have the same index. Only provided if includePaintOrder in
      # captureSnapshot was true.
      optional array of integer paintOrders
      # The offset rect of nodes. Only available when includeDOMRects is set to true
      optional array of Rectangle offsetRects
      # The scroll rect of nodes. Only available when includeDOMRects is set to true
      optional array of Rectangle scrollRects
      # The client rect of nodes. Only available when includeDOMRects is set to true
      optional array of Rectangle clientRects
      # The list of background colors that are blended with colors of overlapping elements.
      experimental optional array of StringIndex blendedBackgroundColors
      # The list of computed text opacities.
      experimental optional array of number textColorOpacities

  # Table of details of the post layout rendered text positions. The exact layout should not be regarded as
  # stable and may change between versions.
  type TextBoxSnapshot extends object
    properties
      # Index of the layout tree node that owns this box collection.
      array of integer layoutIndex
      # The absolute position bounding box.
      array of Rectangle bounds
      # The starting index in characters, for this post layout textbox substring. Characters that
      # would be represented as a surrogate pair in UTF-16 have length 2.
      array of integer start
      # The number of characters in this post layout textbox substring. Characters that would be
      # represented as a surrogate pair in UTF-16 have length 2.
      array of integer length

  # Disables DOM snapshot agent for the given page.
  command disable

  # Enables DOM snapshot agent for the given page.
  command enable

  # Returns a document snapshot, including the full DOM tree of the root node (including iframes,
  # template contents, and imported documents) in a flattened array, as well as layout and
  # white-listed computed style information for the nodes. Shadow DOM in the returned DOM tree is
  # flattened.
  deprecated command getSnapshot
    parameters
      # Whitelist of computed styles to return.
      array of string computedStyleWhitelist
      # Whether or not to retrieve details of DOM listeners (default false).
      optional boolean includeEventListeners
      # Whether to determine and include the paint order index of LayoutTreeNodes (default false).
      optional boolean includePaintOrder
      # Whether to include UA shadow tree in the snapshot (default false).
      optional boolean includeUserAgentShadowTree
    returns
      # The nodes in the DOM tree. The DOMNode at index 0 corresponds to the root document.
      array of DOMNode domNodes
      # The nodes in the layout tree.
      array of LayoutTreeNode layoutTreeNodes
      # Whitelisted ComputedStyle properties for each node in the layout tree.
      array of ComputedStyle computedStyles

  # Returns a document snapshot, including the full DOM tree of the root node (including iframes,
  # template contents, and imported documents) in a flattened array, as well as layout and
  # white-listed computed style information for the nodes. Shadow DOM in the returned DOM tree is
  # flattened.
  command captureSnapshot
    parameters
      # Whitelist of computed styles to return.
      array of string computedStyles
      # Whether to include layout object paint orders into the snapshot.
      optional boolean includePaintOrder
      # Whether to include DOM rectangles (offsetRects, clientRects, scrollRects) into the snapshot
      optional boolean includeDOMRects
      # Whether to include blended background colors in the snapshot (default: false).
      # Blended background color is achieved by blending background colors of all elements
      # that overlap with the current element.
      experimental optional boolean includeBlendedBackgroundColors
      # Whether to include text color opacity in the snapshot (default: false).
      # An element might have the opacity property set that affects the text color of the element.
      # The final text color opacity is computed based on the opacity of all overlapping elements.
      experimental optional boolean includeTextColorOpacities
    returns
      # The nodes in the DOM tree. The DOMNode at index 0 corresponds to the root document.
      array of DocumentSnapshot documents
      # Shared string table that all string properties refer to with indexes.
      array of string strings

# Query and modify DOM storage.
experimental domain DOMStorage

  type SerializedStorageKey extends string

  # DOM Storage identifier.
  type StorageId extends object
    properties
      # Security origin for the storage.
      optional string securityOrigin
      # Represents a key by which DOM Storage keys its CachedStorageAreas
      optional SerializedStorageKey storageKey
      # Whether the storage is local storage (not session storage).
      boolean isLocalStorage

  # DOM Storage item.
  type Item extends array of string

  command clear
    parameters
      StorageId storageId

  # Disables storage tracking, prevents storage events from being sent to the client.
  command disable

  # Enables storage tracking, storage events will now be delivered to the client.
  command enable

  command getDOMStorageItems
    parameters
      StorageId storageId
    returns
      array of Item entries

  command removeDOMStorageItem
    parameters
      StorageId storageId
      string key

  command setDOMStorageItem
    parameters
      StorageId storageId
      string key
      string value

  event domStorageItemAdded
    parameters
      StorageId storageId
      string key
      string newValue

  event domStorageItemRemoved
    parameters
      StorageId storageId
      string key

  event domStorageItemUpdated
    parameters
      StorageId storageId
      string key
      string oldValue
      string newValue

  event domStorageItemsCleared
    parameters
      StorageId storageId

experimental domain Database

  # Unique identifier of Database object.
  type DatabaseId extends string

  # Database object.
  type Database extends object
    properties
      # Database ID.
      DatabaseId id
      # Database domain.
      string domain
      # Database name.
      string name
      # Database version.
      string version

  # Database error.
  type Error extends object
    properties
      # Error message.
      string message
      # Error code.
      integer code

  # Disables database tracking, prevents database events from being sent to the client.
  command disable

  # Enables database tracking, database events will now be delivered to the client.
  command enable

  command executeSQL
    parameters
      DatabaseId databaseId
      string query
    returns
      optional array of string columnNames
      optional array of any values
      optional Error sqlError

  command getDatabaseTableNames
    parameters
      DatabaseId databaseId
    returns
      array of string tableNames

  event addDatabase
    parameters
      Database database

experimental domain DeviceOrientation

  # Clears the overridden Device Orientation.
  command clearDeviceOrientationOverride

  # Overrides the Device Orientation.
  command setDeviceOrientationOverride
    parameters
      # Mock alpha
      number alpha
      # Mock beta
      number beta
      # Mock gamma
      number gamma

# This domain emulates different environments for the page.
domain Emulation
  depends on DOM
  depends on Page
  depends on Runtime

  # Screen orientation.
  type ScreenOrientation extends object
    properties
      # Orientation type.
      enum type
        portraitPrimary
        portraitSecondary
        landscapePrimary
        landscapeSecondary
      # Orientation angle.
      integer angle

  type DisplayFeature extends object
    properties
      # Orientation of a display feature in relation to screen
      enum orientation
        vertical
        horizontal
      # The offset from the screen origin in either the x (for vertical
      # orientation) or y (for horizontal orientation) direction.
      integer offset
      # A display feature may mask content such that it is not physically
      # displayed - this length along with the offset describes this area.
      # A display feature that only splits content will have a 0 mask_length.
      integer maskLength

  type DevicePosture extends object
    properties
      # Current posture of the device
      enum type
        continuous
        folded

  type MediaFeature extends object
    properties
      string name
      string value

  # advance: If the scheduler runs out of immediate work, the virtual time base may fast forward to
  # allow the next delayed task (if any) to run; pause: The virtual time base may not advance;
  # pauseIfNetworkFetchesPending: The virtual time base may not advance if there are any pending
  # resource fetches.
  experimental type VirtualTimePolicy extends string
    enum
      advance
      pause
      pauseIfNetworkFetchesPending

  # Used to specify User Agent Cient Hints to emulate. See https://wicg.github.io/ua-client-hints
  experimental type UserAgentBrandVersion extends object
    properties
      string brand
      string version

  # Used to specify User Agent Cient Hints to emulate. See https://wicg.github.io/ua-client-hints
  # Missing optional values will be filled in by the target with what it would normally use.
  experimental type UserAgentMetadata extends object
    properties
      # Brands appearing in Sec-CH-UA.
      optional array of UserAgentBrandVersion brands
      # Brands appearing in Sec-CH-UA-Full-Version-List.
      optional array of UserAgentBrandVersion fullVersionList
      deprecated optional string fullVersion
      string platform
      string platformVersion
      string architecture
      string model
      boolean mobile
      optional string bitness
      optional boolean wow64

  # Used to specify sensor types to emulate.
  # See https://w3c.github.io/sensors/#automation for more information.
  experimental type SensorType extends string
    enum
      absolute-orientation
      accelerometer
      ambient-light
      gravity
      gyroscope
      linear-acceleration
      magnetometer
      proximity
      relative-orientation

  experimental type SensorMetadata extends object
    properties
      optional boolean available
      optional number minimumFrequency
      optional number maximumFrequency

  experimental type SensorReadingSingle extends object
    properties
      number value

  experimental type SensorReadingXYZ extends object
    properties
      number x
      number y
      number z

  experimental type SensorReadingQuaternion extends object
    properties
      number x
      number y
      number z
      number w

  experimental type SensorReading extends object
    properties
      optional SensorReadingSingle single
      optional SensorReadingXYZ xyz
      optional SensorReadingQuaternion quaternion

  # Tells whether emulation is supported.
  command canEmulate
    returns
      # True if emulation is supported.
      boolean result

  # Clears the overridden device metrics.
  command clearDeviceMetricsOverride

  # Clears the overridden Geolocation Position and Error.
  command clearGeolocationOverride

  # Requests that page scale factor is reset to initial values.
  experimental command resetPageScaleFactor

  # Enables or disables simulating a focused and active page.
  experimental command setFocusEmulationEnabled
    parameters
      # Whether to enable to disable focus emulation.
      boolean enabled

  # Automatically render all web contents using a dark theme.
  experimental command setAutoDarkModeOverride
    parameters
      # Whether to enable or disable automatic dark mode.
      # If not specified, any existing override will be cleared.
      optional boolean enabled

  # Enables CPU throttling to emulate slow CPUs.
  experimental command setCPUThrottlingRate
    parameters
      # Throttling rate as a slowdown factor (1 is no throttle, 2 is 2x slowdown, etc).
      number rate

  # Sets or clears an override of the default background color of the frame. This override is used
  # if the content does not specify one.
  command setDefaultBackgroundColorOverride
    parameters
      # RGBA of the default background color. If not specified, any existing override will be
      # cleared.
      optional DOM.RGBA color

  # Overrides the values of device screen dimensions (window.screen.width, window.screen.height,
  # window.innerWidth, window.innerHeight, and "device-width"/"device-height"-related CSS media
  # query results).
  command setDeviceMetricsOverride
    parameters
      # Overriding width value in pixels (minimum 0, maximum 10000000). 0 disables the override.
      integer width
      # Overriding height value in pixels (minimum 0, maximum 10000000). 0 disables the override.
      integer height
      # Overriding device scale factor value. 0 disables the override.
      number deviceScaleFactor
      # Whether to emulate mobile device. This includes viewport meta tag, overlay scrollbars, text
      # autosizing and more.
      boolean mobile
      # Scale to apply to resulting view image.
      experimental optional number scale
      # Overriding screen width value in pixels (minimum 0, maximum 10000000).
      experimental optional integer screenWidth
      # Overriding screen height value in pixels (minimum 0, maximum 10000000).
      experimental optional integer screenHeight
      # Overriding view X position on screen in pixels (minimum 0, maximum 10000000).
      experimental optional integer positionX
      # Overriding view Y position on screen in pixels (minimum 0, maximum 10000000).
      experimental optional integer positionY
      # Do not set visible view size, rely upon explicit setVisibleSize call.
      experimental optional boolean dontSetVisibleSize
      # Screen orientation override.
      optional ScreenOrientation screenOrientation
      # If set, the visible area of the page will be overridden to this viewport. This viewport
      # change is not observed by the page, e.g. viewport-relative elements do not change positions.
      experimental optional Page.Viewport viewport
      # If set, the display feature of a multi-segment screen. If not set, multi-segment support
      # is turned-off.
      experimental optional DisplayFeature displayFeature
      # If set, the posture of a foldable device. If not set the posture is set
      # to continuous.
      experimental optional DevicePosture devicePosture

  experimental command setScrollbarsHidden
    parameters
      # Whether scrollbars should be always hidden.
      boolean hidden

  experimental command setDocumentCookieDisabled
    parameters
      # Whether document.coookie API should be disabled.
      boolean disabled

  experimental command setEmitTouchEventsForMouse
    parameters
      # Whether touch emulation based on mouse input should be enabled.
      boolean enabled
      # Touch/gesture events configuration. Default: current platform.
      optional enum configuration
        mobile
        desktop

  # Emulates the given media type or media feature for CSS media queries.
  command setEmulatedMedia
    parameters
      # Media type to emulate. Empty string disables the override.
      optional string media
      # Media features to emulate.
      optional array of MediaFeature features

  # Emulates the given vision deficiency.
  experimental command setEmulatedVisionDeficiency
    parameters
      # Vision deficiency to emulate. Order: best-effort emulations come first, followed by any
      # physiologically accurate emulations for medically recognized color vision deficiencies.
      enum type
        none
        blurredVision
        reducedContrast
        achromatopsia
        deuteranopia
        protanopia
        tritanopia

  # Overrides the Geolocation Position or Error. Omitting any of the parameters emulates position
  # unavailable.
  command setGeolocationOverride
    parameters
      # Mock latitude
      optional number latitude
      # Mock longitude
      optional number longitude
      # Mock accuracy
      optional number accuracy

  experimental command getOverriddenSensorInformation
    parameters
      SensorType type
    returns
      number requestedSamplingFrequency

  # Overrides a platform sensor of a given type. If |enabled| is true, calls to
  # Sensor.start() will use a virtual sensor as backend rather than fetching
  # data from a real hardware sensor. Otherwise, existing virtual
  # sensor-backend Sensor objects will fire an error event and new calls to
  # Sensor.start() will attempt to use a real sensor instead.
  experimental command setSensorOverrideEnabled
    parameters
      boolean enabled
      SensorType type
      optional SensorMetadata metadata

  # Updates the sensor readings reported by a sensor type previously overriden
  # by setSensorOverrideEnabled.
  experimental command setSensorOverrideReadings
    parameters
      SensorType type
      SensorReading reading

  # Overrides the Idle state.
  experimental command setIdleOverride
    parameters
      # Mock isUserActive
      boolean isUserActive
      # Mock isScreenUnlocked
      boolean isScreenUnlocked

  # Clears Idle state overrides.
  experimental command clearIdleOverride

  # Overrides value returned by the javascript navigator object.
  experimental deprecated command setNavigatorOverrides
    parameters
      # The platform navigator.platform should return.
      string platform

  # Sets a specified page scale factor.
  experimental command setPageScaleFactor
    parameters
      # Page scale factor.
      number pageScaleFactor

  # Switches script execution in the page.
  command setScriptExecutionDisabled
    parameters
      # Whether script execution should be disabled in the page.
      boolean value

  # Enables touch on platforms which do not support them.
  command setTouchEmulationEnabled
    parameters
      # Whether the touch event emulation should be enabled.
      boolean enabled
      # Maximum touch points supported. Defaults to one.
      optional integer maxTouchPoints

  # Turns on virtual time for all frames (replacing real-time with a synthetic time source) and sets
  # the current virtual time policy.  Note this supersedes any previous time budget.
  experimental command setVirtualTimePolicy
    parameters
      VirtualTimePolicy policy
      # If set, after this many virtual milliseconds have elapsed virtual time will be paused and a
      # virtualTimeBudgetExpired event is sent.
      optional number budget
      # If set this specifies the maximum number of tasks that can be run before virtual is forced
      # forwards to prevent deadlock.
      optional integer maxVirtualTimeTaskStarvationCount
      # If set, base::Time::Now will be overridden to initially return this value.
      optional Network.TimeSinceEpoch initialVirtualTime
    returns
      # Absolute timestamp at which virtual time was first enabled (up time in milliseconds).
      number virtualTimeTicksBase

  # Overrides default host system locale with the specified one.
  experimental command setLocaleOverride
    parameters
      # ICU style C locale (e.g. "en_US"). If not specified or empty, disables the override and
      # restores default host system locale.
      optional string locale

  # Overrides default host system timezone with the specified one.
  experimental command setTimezoneOverride
    parameters
      # The timezone identifier. If empty, disables the override and
      # restores default host system timezone.
      string timezoneId

  # Resizes the frame/viewport of the page. Note that this does not affect the frame's container
  # (e.g. browser window). Can be used to produce screenshots of the specified size. Not supported
  # on Android.
  experimental deprecated command setVisibleSize
    parameters
      # Frame width (DIP).
      integer width
      # Frame height (DIP).
      integer height

  # Notification sent after the virtual time budget for the current VirtualTimePolicy has run out.
  experimental event virtualTimeBudgetExpired

  # Enum of image types that can be disabled.
  experimental type DisabledImageType extends string
    enum
      avif
      webp

  experimental command setDisabledImageTypes
    parameters
      # Image types to disable.
      array of DisabledImageType imageTypes

  experimental command setHardwareConcurrencyOverride
    parameters
      # Hardware concurrency to report
      integer hardwareConcurrency

  # Allows overriding user agent with the given string.
  command setUserAgentOverride
    parameters
      # User agent to use.
      string userAgent
      # Browser language to emulate.
      optional string acceptLanguage
      # The platform navigator.platform should return.
      optional string platform
      # To be sent in Sec-CH-UA-* headers and returned in navigator.userAgentData
      experimental optional UserAgentMetadata userAgentMetadata

  # Allows overriding the automation flag.
  experimental command setAutomationOverride
    parameters
      # Whether the override should be enabled.
      boolean enabled

# This domain provides experimental commands only supported in headless mode.
experimental domain HeadlessExperimental
  depends on Page
  depends on Runtime

  # Encoding options for a screenshot.
  type ScreenshotParams extends object
    properties
      # Image compression format (defaults to png).
      optional enum format
        jpeg
        png
        webp
      # Compression quality from range [0..100] (jpeg and webp only).
      optional integer quality
      # Optimize image encoding for speed, not for resulting size (defaults to false)
      optional boolean optimizeForSpeed

  # Sends a BeginFrame to the target and returns when the frame was completed. Optionally captures a
  # screenshot from the resulting frame. Requires that the target was created with enabled
  # BeginFrameControl. Designed for use with --run-all-compositor-stages-before-draw, see also
  # https://goo.gle/chrome-headless-rendering for more background.
  command beginFrame
    parameters
      # Timestamp of this BeginFrame in Renderer TimeTicks (milliseconds of uptime). If not set,
      # the current time will be used.
      optional number frameTimeTicks
      # The interval between BeginFrames that is reported to the compositor, in milliseconds.
      # Defaults to a 60 frames/second interval, i.e. about 16.666 milliseconds.
      optional number interval
      # Whether updates should not be committed and drawn onto the display. False by default. If
      # true, only side effects of the BeginFrame will be run, such as layout and animations, but
      # any visual updates may not be visible on the display or in screenshots.
      optional boolean noDisplayUpdates
      # If set, a screenshot of the frame will be captured and returned in the response. Otherwise,
      # no screenshot will be captured. Note that capturing a screenshot can fail, for example,
      # during renderer initialization. In such a case, no screenshot data will be returned.
      optional ScreenshotParams screenshot
    returns
      # Whether the BeginFrame resulted in damage and, thus, a new frame was committed to the
      # display. Reported for diagnostic uses, may be removed in the future.
      boolean hasDamage
      # Base64-encoded image data of the screenshot, if one was requested and successfully taken.
      optional binary screenshotData

  # Disables headless events for the target.
  deprecated command disable

  # Enables headless events for the target.
  deprecated command enable

# Input/Output operations for streams produced by DevTools.
domain IO

  # This is either obtained from another method or specified as `blob:<uuid>` where
  # `<uuid>` is an UUID of a Blob.
  type StreamHandle extends string

  # Close the stream, discard any temporary backing storage.
  command close
    parameters
      # Handle of the stream to close.
      StreamHandle handle

  # Read a chunk of the stream
  command read
    parameters
      # Handle of the stream to read.
      StreamHandle handle
      # Seek to the specified offset before reading (if not specificed, proceed with offset
      # following the last read). Some types of streams may only support sequential reads.
      optional integer offset
      # Maximum number of bytes to read (left upon the agent discretion if not specified).
      optional integer size
    returns
      # Set if the data is base64-encoded
      optional boolean base64Encoded
      # Data that were read.
      string data
      # Set if the end-of-file condition occurred while reading.
      boolean eof

  # Return UUID of Blob object specified by a remote object id.
  command resolveBlob
    parameters
      # Object id of a Blob object wrapper.
      Runtime.RemoteObjectId objectId
    returns
      # UUID of the specified Blob.
      string uuid

experimental domain IndexedDB
  depends on Runtime
  depends on Storage

  # Database with an array of object stores.
  type DatabaseWithObjectStores extends object
    properties
      # Database name.
      string name
      # Database version (type is not 'integer', as the standard
      # requires the version number to be 'unsigned long long')
      number version
      # Object stores in this database.
      array of ObjectStore objectStores

  # Object store.
  type ObjectStore extends object
    properties
      # Object store name.
      string name
      # Object store key path.
      KeyPath keyPath
      # If true, object store has auto increment flag set.
      boolean autoIncrement
      # Indexes in this object store.
      array of ObjectStoreIndex indexes

  # Object store index.
  type ObjectStoreIndex extends object
    properties
      # Index name.
      string name
      # Index key path.
      KeyPath keyPath
      # If true, index is unique.
      boolean unique
      # If true, index allows multiple entries for a key.
      boolean multiEntry

  # Key.
  type Key extends object
    properties
      # Key type.
      enum type
        number
        string
        date
        array
      # Number value.
      optional number number
      # String value.
      optional string string
      # Date value.
      optional number date
      # Array value.
      optional array of Key array

  # Key range.
  type KeyRange extends object
    properties
      # Lower bound.
      optional Key lower
      # Upper bound.
      optional Key upper
      # If true lower bound is open.
      boolean lowerOpen
      # If true upper bound is open.
      boolean upperOpen

  # Data entry.
  type DataEntry extends object
    properties
      # Key object.
      Runtime.RemoteObject key
      # Primary key object.
      Runtime.RemoteObject primaryKey
      # Value object.
      Runtime.RemoteObject value

  # Key path.
  type KeyPath extends object
    properties
      # Key path type.
      enum type
        null
        string
        array
      # String value.
      optional string string
      # Array value.
      optional array of string array

  # Clears all entries from an object store.
  command clearObjectStore
    parameters
      # At least and at most one of securityOrigin, storageKey, or storageBucket must be specified.
      # Security origin.
      optional string securityOrigin
      # Storage key.
      optional string storageKey
      # Storage bucket. If not specified, it uses the default bucket.
      optional Storage.StorageBucket storageBucket
      # Database name.
      string databaseName
      # Object store name.
      string objectStoreName

  # Deletes a database.
  command deleteDatabase
    parameters
      # At least and at most one of securityOrigin, storageKey, or storageBucket must be specified.
      # Security origin.
      optional string securityOrigin
      # Storage key.
      optional string storageKey
      # Storage bucket. If not specified, it uses the default bucket.
      optional Storage.StorageBucket storageBucket
      # Database name.
      string databaseName

  # Delete a range of entries from an object store
  command deleteObjectStoreEntries
    parameters
      # At least and at most one of securityOrigin, storageKey, or storageBucket must be specified.
      # Security origin.
      optional string securityOrigin
      # Storage key.
      optional string storageKey
      # Storage bucket. If not specified, it uses the default bucket.
      optional Storage.StorageBucket storageBucket
      string databaseName
      string objectStoreName
      # Range of entry keys to delete
      KeyRange keyRange

  # Disables events from backend.
  command disable

  # Enables events from backend.
  command enable

  # Requests data from object store or index.
  command requestData
    parameters
      # At least and at most one of securityOrigin, storageKey, or storageBucket must be specified.
      # Security origin.
      optional string securityOrigin
      # Storage key.
      optional string storageKey
      # Storage bucket. If not specified, it uses the default bucket.
      optional Storage.StorageBucket storageBucket
      # Database name.
      string databaseName
      # Object store name.
      string objectStoreName
      # Index name, empty string for object store data requests.
      string indexName
      # Number of records to skip.
      integer skipCount
      # Number of records to fetch.
      integer pageSize
      # Key range.
      optional KeyRange keyRange
    returns
      # Array of object store data entries.
      array of DataEntry objectStoreDataEntries
      # If true, there are more entries to fetch in the given range.
      boolean hasMore

  # Gets metadata of an object store.
  command getMetadata
    parameters
      # At least and at most one of securityOrigin, storageKey, or storageBucket must be specified.
      # Security origin.
      optional string securityOrigin
      # Storage key.
      optional string storageKey
      # Storage bucket. If not specified, it uses the default bucket.
      optional Storage.StorageBucket storageBucket
      # Database name.
      string databaseName
      # Object store name.
      string objectStoreName
    returns
      # the entries count
      number entriesCount
      # the current value of key generator, to become the next inserted
      # key into the object store. Valid if objectStore.autoIncrement
      # is true.
      number keyGeneratorValue

  # Requests database with given name in given frame.
  command requestDatabase
    parameters
      # At least and at most one of securityOrigin, storageKey, or storageBucket must be specified.
      # Security origin.
      optional string securityOrigin
      # Storage key.
      optional string storageKey
      # Storage bucket. If not specified, it uses the default bucket.
      optional Storage.StorageBucket storageBucket
      # Database name.
      string databaseName
    returns
      # Database with an array of object stores.
      DatabaseWithObjectStores databaseWithObjectStores

  # Requests database names for given security origin.
  command requestDatabaseNames
    parameters
      # At least and at most one of securityOrigin, storageKey, or storageBucket must be specified.
      # Security origin.
      optional string securityOrigin
      # Storage key.
      optional string storageKey
      # Storage bucket. If not specified, it uses the default bucket.
      optional Storage.StorageBucket storageBucket
    returns
      # Database names for origin.
      array of string databaseNames

domain Input

  type TouchPoint extends object
    properties
      # X coordinate of the event relative to the main frame's viewport in CSS pixels.
      number x
      # Y coordinate of the event relative to the main frame's viewport in CSS pixels. 0 refers to
      # the top of the viewport and Y increases as it proceeds towards the bottom of the viewport.
      number y
      # X radius of the touch area (default: 1.0).
      optional number radiusX
      # Y radius of the touch area (default: 1.0).
      optional number radiusY
      # Rotation angle (default: 0.0).
      optional number rotationAngle
      # Force (default: 1.0).
      optional number force
      # The normalized tangential pressure, which has a range of [-1,1] (default: 0).
      experimental optional number tangentialPressure
      # The plane angle between the Y-Z plane and the plane containing both the stylus axis and the Y axis, in degrees of the range [-90,90], a positive tiltX is to the right (default: 0)
      optional number tiltX
      # The plane angle between the X-Z plane and the plane containing both the stylus axis and the X axis, in degrees of the range [-90,90], a positive tiltY is towards the user (default: 0).
      optional number tiltY
      # The clockwise rotation of a pen stylus around its own major axis, in degrees in the range [0,359] (default: 0).
      experimental optional integer twist
      # Identifier used to track touch sources between events, must be unique within an event.
      optional number id

  experimental type GestureSourceType extends string
    enum
      default
      touch
      mouse

  type MouseButton extends string
    enum
        none
        left
        middle
        right
        back
        forward

  # UTC time in seconds, counted from January 1, 1970.
  type TimeSinceEpoch extends number

  experimental type DragDataItem extends object
    properties
      # Mime type of the dragged data.
      string mimeType
      # Depending of the value of `mimeType`, it contains the dragged link,
      # text, HTML markup or any other data.
      string data

      # Title associated with a link. Only valid when `mimeType` == "text/uri-list".
      optional string title

      # Stores the base URL for the contained markup. Only valid when `mimeType`
      # == "text/html".
      optional string baseURL


  experimental type DragData extends object
    properties
      array of DragDataItem items
      # List of filenames that should be included when dropping
      optional array of string files
      # Bit field representing allowed drag operations. Copy = 1, Link = 2, Move = 16
      integer dragOperationsMask

  # Dispatches a drag event into the page.
  experimental command dispatchDragEvent
    parameters
      # Type of the drag event.
      enum type
        dragEnter
        dragOver
        drop
        dragCancel
      # X coordinate of the event relative to the main frame's viewport in CSS pixels.
      number x
      # Y coordinate of the event relative to the main frame's viewport in CSS pixels. 0 refers to
      # the top of the viewport and Y increases as it proceeds towards the bottom of the viewport.
      number y
      DragData data
      # Bit field representing pressed modifier keys. Alt=1, Ctrl=2, Meta/Command=4, Shift=8
      # (default: 0).
      optional integer modifiers

  # Dispatches a key event to the page.
  command dispatchKeyEvent
    parameters
      # Type of the key event.
      enum type
        keyDown
        keyUp
        rawKeyDown
        char
      # Bit field representing pressed modifier keys. Alt=1, Ctrl=2, Meta/Command=4, Shift=8
      # (default: 0).
      optional integer modifiers
      # Time at which the event occurred.
      optional TimeSinceEpoch timestamp
      # Text as generated by processing a virtual key code with a keyboard layout. Not needed for
      # for `keyUp` and `rawKeyDown` events (default: "")
      optional string text
      # Text that would have been generated by the keyboard if no modifiers were pressed (except for
      # shift). Useful for shortcut (accelerator) key handling (default: "").
      optional string unmodifiedText
      # Unique key identifier (e.g., 'U+0041') (default: "").
      optional string keyIdentifier
      # Unique DOM defined string value for each physical key (e.g., 'KeyA') (default: "").
      optional string code
      # Unique DOM defined string value describing the meaning of the key in the context of active
      # modifiers, keyboard layout, etc (e.g., 'AltGr') (default: "").
      optional string key
      # Windows virtual key code (default: 0).
      optional integer windowsVirtualKeyCode
      # Native virtual key code (default: 0).
      optional integer nativeVirtualKeyCode
      # Whether the event was generated from auto repeat (default: false).
      optional boolean autoRepeat
      # Whether the event was generated from the keypad (default: false).
      optional boolean isKeypad
      # Whether the event was a system key event (default: false).
      optional boolean isSystemKey
      # Whether the event was from the left or right side of the keyboard. 1=Left, 2=Right (default:
      # 0).
      optional integer location
      # Editing commands to send with the key event (e.g., 'selectAll') (default: []).
      # These are related to but not equal the command names used in `document.execCommand` and NSStandardKeyBindingResponding.
      # See https://source.chromium.org/chromium/chromium/src/+/main:third_party/blink/renderer/core/editing/commands/editor_command_names.h for valid command names.
      experimental optional array of string commands

  # This method emulates inserting text that doesn't come from a key press,
  # for example an emoji keyboard or an IME.
  experimental command insertText
    parameters
      # The text to insert.
      string text

  # This method sets the current candidate text for ime.
  # Use imeCommitComposition to commit the final text.
  # Use imeSetComposition with empty string as text to cancel composition.
  experimental command imeSetComposition
    parameters
      # The text to insert
      string text
      # selection start
      integer selectionStart
      # selection end
      integer selectionEnd
      # replacement start
      optional integer replacementStart
      # replacement end
      optional integer replacementEnd

  # Dispatches a mouse event to the page.
  command dispatchMouseEvent
    parameters
      # Type of the mouse event.
      enum type
        mousePressed
        mouseReleased
        mouseMoved
        mouseWheel
      # X coordinate of the event relative to the main frame's viewport in CSS pixels.
      number x
      # Y coordinate of the event relative to the main frame's viewport in CSS pixels. 0 refers to
      # the top of the viewport and Y increases as it proceeds towards the bottom of the viewport.
      number y
      # Bit field representing pressed modifier keys. Alt=1, Ctrl=2, Meta/Command=4, Shift=8
      # (default: 0).
      optional integer modifiers
      # Time at which the event occurred.
      optional TimeSinceEpoch timestamp
      # Mouse button (default: "none").
      optional MouseButton button
      # A number indicating which buttons are pressed on the mouse when a mouse event is triggered.
      # Left=1, Right=2, Middle=4, Back=8, Forward=16, None=0.
      optional integer buttons
      # Number of times the mouse button was clicked (default: 0).
      optional integer clickCount
      # The normalized pressure, which has a range of [0,1] (default: 0).
      experimental optional number force
      # The normalized tangential pressure, which has a range of [-1,1] (default: 0).
      experimental optional number tangentialPressure
      # The plane angle between the Y-Z plane and the plane containing both the stylus axis and the Y axis, in degrees of the range [-90,90], a positive tiltX is to the right (default: 0).
      optional number tiltX
      # The plane angle between the X-Z plane and the plane containing both the stylus axis and the X axis, in degrees of the range [-90,90], a positive tiltY is towards the user (default: 0).
      optional number tiltY
      # The clockwise rotation of a pen stylus around its own major axis, in degrees in the range [0,359] (default: 0).
      experimental optional integer twist
      # X delta in CSS pixels for mouse wheel event (default: 0).
      optional number deltaX
      # Y delta in CSS pixels for mouse wheel event (default: 0).
      optional number deltaY
      # Pointer type (default: "mouse").
      optional enum pointerType
        mouse
        pen

  # Dispatches a touch event to the page.
  command dispatchTouchEvent
    parameters
      # Type of the touch event. TouchEnd and TouchCancel must not contain any touch points, while
      # TouchStart and TouchMove must contains at least one.
      enum type
        touchStart
        touchEnd
        touchMove
        touchCancel
      # Active touch points on the touch device. One event per any changed point (compared to
      # previous touch event in a sequence) is generated, emulating pressing/moving/releasing points
      # one by one.
      array of TouchPoint touchPoints
      # Bit field representing pressed modifier keys. Alt=1, Ctrl=2, Meta/Command=4, Shift=8
      # (default: 0).
      optional integer modifiers
      # Time at which the event occurred.
      optional TimeSinceEpoch timestamp

  # Cancels any active dragging in the page.
  command cancelDragging

  # Emulates touch event from the mouse event parameters.
  experimental command emulateTouchFromMouseEvent
    parameters
      # Type of the mouse event.
      enum type
        mousePressed
        mouseReleased
        mouseMoved
        mouseWheel
      # X coordinate of the mouse pointer in DIP.
      integer x
      # Y coordinate of the mouse pointer in DIP.
      integer y
      # Mouse button. Only "none", "left", "right" are supported.
      MouseButton button
      # Time at which the event occurred (default: current time).
      optional TimeSinceEpoch timestamp
      # X delta in DIP for mouse wheel event (default: 0).
      optional number deltaX
      # Y delta in DIP for mouse wheel event (default: 0).
      optional number deltaY
      # Bit field representing pressed modifier keys. Alt=1, Ctrl=2, Meta/Command=4, Shift=8
      # (default: 0).
      optional integer modifiers
      # Number of times the mouse button was clicked (default: 0).
      optional integer clickCount

  # Ignores input events (useful while auditing page).
  command setIgnoreInputEvents
    parameters
      # Ignores input events processing when set to true.
      boolean ignore

  # Prevents default drag and drop behavior and instead emits `Input.dragIntercepted` events.
  # Drag and drop behavior can be directly controlled via `Input.dispatchDragEvent`.
  experimental command setInterceptDrags
    parameters
      boolean enabled

  # Synthesizes a pinch gesture over a time period by issuing appropriate touch events.
  experimental command synthesizePinchGesture
    parameters
      # X coordinate of the start of the gesture in CSS pixels.
      number x
      # Y coordinate of the start of the gesture in CSS pixels.
      number y
      # Relative scale factor after zooming (>1.0 zooms in, <1.0 zooms out).
      number scaleFactor
      # Relative pointer speed in pixels per second (default: 800).
      optional integer relativeSpeed
      # Which type of input events to be generated (default: 'default', which queries the platform
      # for the preferred input type).
      optional GestureSourceType gestureSourceType

  # Synthesizes a scroll gesture over a time period by issuing appropriate touch events.
  experimental command synthesizeScrollGesture
    parameters
      # X coordinate of the start of the gesture in CSS pixels.
      number x
      # Y coordinate of the start of the gesture in CSS pixels.
      number y
      # The distance to scroll along the X axis (positive to scroll left).
      optional number xDistance
      # The distance to scroll along the Y axis (positive to scroll up).
      optional number yDistance
      # The number of additional pixels to scroll back along the X axis, in addition to the given
      # distance.
      optional number xOverscroll
      # The number of additional pixels to scroll back along the Y axis, in addition to the given
      # distance.
      optional number yOverscroll
      # Prevent fling (default: true).
      optional boolean preventFling
      # Swipe speed in pixels per second (default: 800).
      optional integer speed
      # Which type of input events to be generated (default: 'default', which queries the platform
      # for the preferred input type).
      optional GestureSourceType gestureSourceType
      # The number of times to repeat the gesture (default: 0).
      optional integer repeatCount
      # The number of milliseconds delay between each repeat. (default: 250).
      optional integer repeatDelayMs
      # The name of the interaction markers to generate, if not empty (default: "").
      optional string interactionMarkerName

  # Synthesizes a tap gesture over a time period by issuing appropriate touch events.
  experimental command synthesizeTapGesture
    parameters
      # X coordinate of the start of the gesture in CSS pixels.
      number x
      # Y coordinate of the start of the gesture in CSS pixels.
      number y
      # Duration between touchdown and touchup events in ms (default: 50).
      optional integer duration
      # Number of times to perform the tap (e.g. 2 for double tap, default: 1).
      optional integer tapCount
      # Which type of input events to be generated (default: 'default', which queries the platform
      # for the preferred input type).
      optional GestureSourceType gestureSourceType

  # Emitted only when `Input.setInterceptDrags` is enabled. Use this data with `Input.dispatchDragEvent` to
  # restore normal drag and drop behavior.
  experimental event dragIntercepted
    parameters
      DragData data

experimental domain Inspector

  # Disables inspector domain notifications.
  command disable

  # Enables inspector domain notifications.
  command enable

  # Fired when remote debugging connection is about to be terminated. Contains detach reason.
  event detached
    parameters
      # The reason why connection has been terminated.
      string reason

  # Fired when debugging target has crashed
  event targetCrashed

  # Fired when debugging target has reloaded after crash
  event targetReloadedAfterCrash

experimental domain LayerTree
  depends on DOM

  # Unique Layer identifier.
  type LayerId extends string

  # Unique snapshot identifier.
  type SnapshotId extends string

  # Rectangle where scrolling happens on the main thread.
  type ScrollRect extends object
    properties
      # Rectangle itself.
      DOM.Rect rect
      # Reason for rectangle to force scrolling on the main thread
      enum type
        RepaintsOnScroll
        TouchEventHandler
        WheelEventHandler

  # Sticky position constraints.
  type StickyPositionConstraint extends object
    properties
      # Layout rectangle of the sticky element before being shifted
      DOM.Rect stickyBoxRect
      # Layout rectangle of the containing block of the sticky element
      DOM.Rect containingBlockRect
      # The nearest sticky layer that shifts the sticky box
      optional LayerId nearestLayerShiftingStickyBox
      # The nearest sticky layer that shifts the containing block
      optional LayerId nearestLayerShiftingContainingBlock

  # Serialized fragment of layer picture along with its offset within the layer.
  type PictureTile extends object
    properties
      # Offset from owning layer left boundary
      number x
      # Offset from owning layer top boundary
      number y
      # Base64-encoded snapshot data.
      binary picture

  # Information about a compositing layer.
  type Layer extends object
    properties
      # The unique id for this layer.
      LayerId layerId
      # The id of parent (not present for root).
      optional LayerId parentLayerId
      # The backend id for the node associated with this layer.
      optional DOM.BackendNodeId backendNodeId
      # Offset from parent layer, X coordinate.
      number offsetX
      # Offset from parent layer, Y coordinate.
      number offsetY
      # Layer width.
      number width
      # Layer height.
      number height
      # Transformation matrix for layer, default is identity matrix
      optional array of number transform
      # Transform anchor point X, absent if no transform specified
      optional number anchorX
      # Transform anchor point Y, absent if no transform specified
      optional number anchorY
      # Transform anchor point Z, absent if no transform specified
      optional number anchorZ
      # Indicates how many time this layer has painted.
      integer paintCount
      # Indicates whether this layer hosts any content, rather than being used for
      # transform/scrolling purposes only.
      boolean drawsContent
      # Set if layer is not visible.
      optional boolean invisible
      # Rectangles scrolling on main thread only.
      optional array of ScrollRect scrollRects
      # Sticky position constraint information
      optional StickyPositionConstraint stickyPositionConstraint

  # Array of timings, one per paint step.
  type PaintProfile extends array of number

  # Provides the reasons why the given layer was composited.
  command compositingReasons
    parameters
      # The id of the layer for which we want to get the reasons it was composited.
      LayerId layerId
    returns
      # A list of strings specifying reasons for the given layer to become composited.
      array of string compositingReasons
      # A list of strings specifying reason IDs for the given layer to become composited.
      array of string compositingReasonIds

  # Disables compositing tree inspection.
  command disable

  # Enables compositing tree inspection.
  command enable

  # Returns the snapshot identifier.
  command loadSnapshot
    parameters
      # An array of tiles composing the snapshot.
      array of PictureTile tiles
    returns
      # The id of the snapshot.
      SnapshotId snapshotId

  # Returns the layer snapshot identifier.
  command makeSnapshot
    parameters
      # The id of the layer.
      LayerId layerId
    returns
      # The id of the layer snapshot.
      SnapshotId snapshotId

  command profileSnapshot
    parameters
      # The id of the layer snapshot.
      SnapshotId snapshotId
      # The maximum number of times to replay the snapshot (1, if not specified).
      optional integer minRepeatCount
      # The minimum duration (in seconds) to replay the snapshot.
      optional number minDuration
      # The clip rectangle to apply when replaying the snapshot.
      optional DOM.Rect clipRect
    returns
      # The array of paint profiles, one per run.
      array of PaintProfile timings

  # Releases layer snapshot captured by the back-end.
  command releaseSnapshot
    parameters
      # The id of the layer snapshot.
      SnapshotId snapshotId

  # Replays the layer snapshot and returns the resulting bitmap.
  command replaySnapshot
    parameters
      # The id of the layer snapshot.
      SnapshotId snapshotId
      # The first step to replay from (replay from the very start if not specified).
      optional integer fromStep
      # The last step to replay to (replay till the end if not specified).
      optional integer toStep
      # The scale to apply while replaying (defaults to 1).
      optional number scale
    returns
      # A data: URL for resulting image.
      string dataURL

  # Replays the layer snapshot and returns canvas log.
  command snapshotCommandLog
    parameters
      # The id of the layer snapshot.
      SnapshotId snapshotId
    returns
      # The array of canvas function calls.
      array of object commandLog

  event layerPainted
    parameters
      # The id of the painted layer.
      LayerId layerId
      # Clip rectangle.
      DOM.Rect clip

  event layerTreeDidChange
    parameters
      # Layer tree, absent if not in the comspositing mode.
      optional array of Layer layers

# Provides access to log entries.
domain Log
  depends on Runtime
  depends on Network

  # Log entry.
  type LogEntry extends object
    properties
      # Log entry source.
      enum source
        xml
        javascript
        network
        storage
        appcache
        rendering
        security
        deprecation
        worker
        violation
        intervention
        recommendation
        other
      # Log entry severity.
      enum level
        verbose
        info
        warning
        error
      # Logged text.
      string text
      optional enum category
        cors
      # Timestamp when this entry was added.
      Runtime.Timestamp timestamp
      # URL of the resource if known.
      optional string url
      # Line number in the resource.
      optional integer lineNumber
      # JavaScript stack trace.
      optional Runtime.StackTrace stackTrace
      # Identifier of the network request associated with this entry.
      optional Network.RequestId networkRequestId
      # Identifier of the worker associated with this entry.
      optional string workerId
      # Call arguments.
      optional array of Runtime.RemoteObject args

  # Violation configuration setting.
  type ViolationSetting extends object
    properties
      # Violation type.
      enum name
        longTask
        longLayout
        blockedEvent
        blockedParser
        discouragedAPIUse
        handler
        recurringHandler
      # Time threshold to trigger upon.
      number threshold

  # Clears the log.
  command clear

  # Disables log domain, prevents further log entries from being reported to the client.
  command disable

  # Enables log domain, sends the entries collected so far to the client by means of the
  # `entryAdded` notification.
  command enable

  # start violation reporting.
  command startViolationsReport
    parameters
      # Configuration for violations.
      array of ViolationSetting config

  # Stop violation reporting.
  command stopViolationsReport

  # Issued when new message was logged.
  event entryAdded
    parameters
      # The entry.
      LogEntry entry

experimental domain Memory

  # Memory pressure level.
  type PressureLevel extends string
    enum
      moderate
      critical

  command getDOMCounters
    returns
      integer documents
      integer nodes
      integer jsEventListeners

  command prepareForLeakDetection

  # Simulate OomIntervention by purging V8 memory.
  command forciblyPurgeJavaScriptMemory

  # Enable/disable suppressing memory pressure notifications in all processes.
  command setPressureNotificationsSuppressed
    parameters
      # If true, memory pressure notifications will be suppressed.
      boolean suppressed

  # Simulate a memory pressure notification in all processes.
  command simulatePressureNotification
    parameters
      # Memory pressure level of the notification.
      PressureLevel level

  # Start collecting native memory profile.
  command startSampling
    parameters
      # Average number of bytes between samples.
      optional integer samplingInterval
      # Do not randomize intervals between samples.
      optional boolean suppressRandomness

  # Stop collecting native memory profile.
  command stopSampling

  # Retrieve native memory allocations profile
  # collected since renderer process startup.
  command getAllTimeSamplingProfile
    returns
      SamplingProfile profile

  # Retrieve native memory allocations profile
  # collected since browser process startup.
  command getBrowserSamplingProfile
    returns
      SamplingProfile profile

  # Retrieve native memory allocations profile collected since last
  # `startSampling` call.
  command getSamplingProfile
    returns
      SamplingProfile profile

  # Heap profile sample.
  type SamplingProfileNode extends object
    properties
      # Size of the sampled allocation.
      number size
      # Total bytes attributed to this sample.
      number total
      # Execution stack at the point of allocation.
      array of string stack

  # Array of heap profile samples.
  type SamplingProfile extends object
    properties
      array of SamplingProfileNode samples
      array of Module modules

  # Executable module information
  type Module extends object
    properties
      # Name of the module.
      string name
      # UUID of the module.
      string uuid
      # Base address where the module is loaded into memory. Encoded as a decimal
      # or hexadecimal (0x prefixed) string.
      string baseAddress
      # Size of the module in bytes.
      number size

# Network domain allows tracking network activities of the page. It exposes information about http,
# file, data and other requests and responses, their headers, bodies, timing, etc.
domain Network
  depends on Debugger
  depends on Runtime
  depends on Security

  # Resource type as it was perceived by the rendering engine.
  type ResourceType extends string
    enum
      Document
      Stylesheet
      Image
      Media
      Font
      Script
      TextTrack
      XHR
      Fetch
      Prefetch
      EventSource
      WebSocket
      Manifest
      SignedExchange
      Ping
      CSPViolationReport
      Preflight
      Other

  # Unique loader identifier.
  type LoaderId extends string

  # Unique request identifier.
  type RequestId extends string

  # Unique intercepted request identifier.
  type InterceptionId extends string

  # Network level fetch failure reason.
  type ErrorReason extends string
    enum
      Failed
      Aborted
      TimedOut
      AccessDenied
      ConnectionClosed
      ConnectionReset
      ConnectionRefused
      ConnectionAborted
      ConnectionFailed
      NameNotResolved
      InternetDisconnected
      AddressUnreachable
      BlockedByClient
      BlockedByResponse

  # UTC time in seconds, counted from January 1, 1970.
  type TimeSinceEpoch extends number

  # Monotonically increasing time in seconds since an arbitrary point in the past.
  type MonotonicTime extends number

  # Request / response headers as keys / values of JSON object.
  type Headers extends object

  # The underlying connection technology that the browser is supposedly using.
  type ConnectionType extends string
    enum
      none
      cellular2g
      cellular3g
      cellular4g
      bluetooth
      ethernet
      wifi
      wimax
      other

  # Represents the cookie's 'SameSite' status:
  # https://tools.ietf.org/html/draft-west-first-party-cookies
  type CookieSameSite extends string
    enum
      Strict
      Lax
      None

  # Represents the cookie's 'Priority' status:
  # https://tools.ietf.org/html/draft-west-cookie-priority-00
  experimental type CookiePriority extends string
    enum
      Low
      Medium
      High

  # Represents the source scheme of the origin that originally set the cookie.
  # A value of "Unset" allows protocol clients to emulate legacy cookie scope for the scheme.
  # This is a temporary ability and it will be removed in the future.
  experimental type CookieSourceScheme extends string
    enum
      Unset
      NonSecure
      Secure

  # Timing information for the request.
  type ResourceTiming extends object
    properties
      # Timing's requestTime is a baseline in seconds, while the other numbers are ticks in
      # milliseconds relatively to this requestTime.
      number requestTime
      # Started resolving proxy.
      number proxyStart
      # Finished resolving proxy.
      number proxyEnd
      # Started DNS address resolve.
      number dnsStart
      # Finished DNS address resolve.
      number dnsEnd
      # Started connecting to the remote host.
      number connectStart
      # Connected to the remote host.
      number connectEnd
      # Started SSL handshake.
      number sslStart
      # Finished SSL handshake.
      number sslEnd
      # Started running ServiceWorker.
      experimental number workerStart
      # Finished Starting ServiceWorker.
      experimental number workerReady
      # Started fetch event.
      experimental number workerFetchStart
      # Settled fetch event respondWith promise.
      experimental number workerRespondWithSettled
      # Started sending request.
      number sendStart
      # Finished sending request.
      number sendEnd
      # Time the server started pushing request.
      experimental number pushStart
      # Time the server finished pushing request.
      experimental number pushEnd
      # Started receiving response headers.
      experimental number receiveHeadersStart
      # Finished receiving response headers.
      number receiveHeadersEnd

  # Loading priority of a resource request.
  type ResourcePriority extends string
    enum
      VeryLow
      Low
      Medium
      High
      VeryHigh

  # Post data entry for HTTP request
  type PostDataEntry extends object
    properties
      optional binary bytes

  # HTTP request data.
  type Request extends object
    properties
      # Request URL (without fragment).
      string url
      # Fragment of the requested URL starting with hash, if present.
      optional string urlFragment
      # HTTP request method.
      string method
      # HTTP request headers.
      Headers headers
      # HTTP POST request data.
      optional string postData
      # True when the request has POST data. Note that postData might still be omitted when this flag is true when the data is too long.
      optional boolean hasPostData
      # Request body elements. This will be converted from base64 to binary
      experimental optional array of PostDataEntry postDataEntries
      # The mixed content type of the request.
      optional Security.MixedContentType mixedContentType
      # Priority of the resource request at the time request is sent.
      ResourcePriority initialPriority
      # The referrer policy of the request, as defined in https://www.w3.org/TR/referrer-policy/
      enum referrerPolicy
        unsafe-url
        no-referrer-when-downgrade
        no-referrer
        origin
        origin-when-cross-origin
        same-origin
        strict-origin
        strict-origin-when-cross-origin
      # Whether is loaded via link preload.
      optional boolean isLinkPreload
      # Set for requests when the TrustToken API is used. Contains the parameters
      # passed by the developer (e.g. via "fetch") as understood by the backend.
      experimental optional TrustTokenParams trustTokenParams
      # True if this resource request is considered to be the 'same site' as the
      # request correspondinfg to the main frame.
      experimental optional boolean isSameSite

  # Details of a signed certificate timestamp (SCT).
  type SignedCertificateTimestamp extends object
    properties
      # Validation status.
      string status
      # Origin.
      string origin
      # Log name / description.
      string logDescription
      # Log ID.
      string logId
      # Issuance date. Unlike TimeSinceEpoch, this contains the number of
      # milliseconds since January 1, 1970, UTC, not the number of seconds.
      number timestamp
      # Hash algorithm.
      string hashAlgorithm
      # Signature algorithm.
      string signatureAlgorithm
      # Signature data.
      string signatureData

  # Security details about a request.
  type SecurityDetails extends object
    properties
      # Protocol name (e.g. "TLS 1.2" or "QUIC").
      string protocol
      # Key Exchange used by the connection, or the empty string if not applicable.
      string keyExchange
      # (EC)DH group used by the connection, if applicable.
      optional string keyExchangeGroup
      # Cipher name.
      string cipher
      # TLS MAC. Note that AEAD ciphers do not have separate MACs.
      optional string mac
      # Certificate ID value.
      Security.CertificateId certificateId
      # Certificate subject name.
      string subjectName
      # Subject Alternative Name (SAN) DNS names and IP addresses.
      array of string sanList
      # Name of the issuing CA.
      string issuer
      # Certificate valid from date.
      TimeSinceEpoch validFrom
      # Certificate valid to (expiration) date
      TimeSinceEpoch validTo
      # List of signed certificate timestamps (SCTs).
      array of SignedCertificateTimestamp signedCertificateTimestampList
      # Whether the request complied with Certificate Transparency policy
      CertificateTransparencyCompliance certificateTransparencyCompliance
      # The signature algorithm used by the server in the TLS server signature,
      # represented as a TLS SignatureScheme code point. Omitted if not
      # applicable or not known.
      optional integer serverSignatureAlgorithm
      # Whether the connection used Encrypted ClientHello
      boolean encryptedClientHello

  # Whether the request complied with Certificate Transparency policy.
  type CertificateTransparencyCompliance extends string
    enum
      unknown
      not-compliant
      compliant

  # The reason why request was blocked.
  type BlockedReason extends string
    enum
      other
      csp
      mixed-content
      origin
      inspector
      subresource-filter
      content-type
      coep-frame-resource-needs-coep-header
      coop-sandboxed-iframe-cannot-navigate-to-coop-page
      corp-not-same-origin
      corp-not-same-origin-after-defaulted-to-same-origin-by-coep
      corp-not-same-site

  # The reason why request was blocked.
  type CorsError extends string
    enum
      DisallowedByMode
      InvalidResponse
      WildcardOriginNotAllowed
      MissingAllowOriginHeader
      MultipleAllowOriginValues
      InvalidAllowOriginValue
      AllowOriginMismatch
      InvalidAllowCredentials
      CorsDisabledScheme
      PreflightInvalidStatus
      PreflightDisallowedRedirect
      PreflightWildcardOriginNotAllowed
      PreflightMissingAllowOriginHeader
      PreflightMultipleAllowOriginValues
      PreflightInvalidAllowOriginValue
      PreflightAllowOriginMismatch
      PreflightInvalidAllowCredentials
      # TODO(https://crbug.com/1263483): Remove this once frontend code does
      # not reference it anymore.
      PreflightMissingAllowExternal
      # TODO(https://crbug.com/1263483): Remove this once frontend code does
      # not reference it anymore.
      PreflightInvalidAllowExternal
      PreflightMissingAllowPrivateNetwork
      PreflightInvalidAllowPrivateNetwork
      InvalidAllowMethodsPreflightResponse
      InvalidAllowHeadersPreflightResponse
      MethodDisallowedByPreflightResponse
      HeaderDisallowedByPreflightResponse
      RedirectContainsCredentials
      # Request was a private network request initiated by a non-secure context.
      InsecurePrivateNetwork
      # Request carried a target IP address space property that did not match
      # the target resource's address space.
      InvalidPrivateNetworkAccess
      # Request was a private network request yet did not carry a target IP
      # address space.
      UnexpectedPrivateNetworkAccess
      NoCorsRedirectModeNotFollow
      # Request was a private network request and needed user permission yet did
      # not carry `Private-Network-Access-Id` in the preflight response.
      # https://github.com/WICG/private-network-access/blob/main/permission_prompt/explainer.md
      PreflightMissingPrivateNetworkAccessId
      # Request was a private network request and needed user permission yet did
      # not carry `Private-Network-Access-Name` in the preflight response.
      # https://github.com/WICG/private-network-access/blob/main/permission_prompt/explainer.md
      PreflightMissingPrivateNetworkAccessName
      # Request was a private network request and needed user permission yet not
      # able to request for permission.
      # https://github.com/WICG/private-network-access/blob/main/permission_prompt/explainer.md
      PrivateNetworkAccessPermissionUnavailable
      # Request was a private network request and is denied by user permission.
      # https://github.com/WICG/private-network-access/blob/main/permission_prompt/explainer.md
      PrivateNetworkAccessPermissionDenied

  type CorsErrorStatus extends object
    properties
      CorsError corsError
      string failedParameter

  # Source of serviceworker response.
  type ServiceWorkerResponseSource extends string
    enum
      cache-storage
      http-cache
      fallback-code
      network

  # Determines what type of Trust Token operation is executed and
  # depending on the type, some additional parameters. The values
  # are specified in third_party/blink/renderer/core/fetch/trust_token.idl.
  experimental type TrustTokenParams extends object
    properties
      TrustTokenOperationType operation

      # Only set for "token-redemption" operation and determine whether
      # to request a fresh SRR or use a still valid cached SRR.
      enum refreshPolicy
        UseCached
        Refresh

      # Origins of issuers from whom to request tokens or redemption
      # records.
      optional array of string issuers

  experimental type TrustTokenOperationType extends string
    enum
      # Type "token-request" in the Trust Token API.
      Issuance
      # Type "token-redemption" in the Trust Token API.
      Redemption
      # Type "send-redemption-record" in the Trust Token API.
      Signing

  # The reason why Chrome uses a specific transport protocol for HTTP semantics.
  experimental type AlternateProtocolUsage extends string
    enum
      # Alternate Protocol was used without racing a normal connection.
      alternativeJobWonWithoutRace
      # Alternate Protocol was used by winning a race with a normal connection.
      alternativeJobWonRace
      # Alternate Protocol was not used by losing a race with a normal connection.
      mainJobWonRace
      # Alternate Protocol was not used because no Alternate-Protocol information
      # was available when the request was issued, but an Alternate-Protocol header
      # was present in the response.
      mappingMissing
      # Alternate Protocol was not used because it was marked broken.
      broken
      # HTTPS DNS protocol upgrade job was used without racing with a normal
      # connection and an Alternate Protocol job.
      dnsAlpnH3JobWonWithoutRace
      # HTTPS DNS protocol upgrade job won a race with a normal connection and
      # an Alternate Protocol job.
      dnsAlpnH3JobWonRace
      # This value is used when the reason is unknown.
      unspecifiedReason

  experimental type ServiceWorkerRouterInfo extends object
    properties
      integer ruleIdMatched

  # HTTP response data.
  type Response extends object
    properties
      # Response URL. This URL can be different from CachedResource.url in case of redirect.
      string url
      # HTTP response status code.
      integer status
      # HTTP response status text.
      string statusText
      # HTTP response headers.
      Headers headers
      # HTTP response headers text. This has been replaced by the headers in Network.responseReceivedExtraInfo.
      deprecated optional string headersText
      # Resource mimeType as determined by the browser.
      string mimeType
      # Refined HTTP request headers that were actually transmitted over the network.
      optional Headers requestHeaders
      # HTTP request headers text. This has been replaced by the headers in Network.requestWillBeSentExtraInfo.
      deprecated optional string requestHeadersText
      # Specifies whether physical connection was actually reused for this request.
      boolean connectionReused
      # Physical connection id that was actually used for this request.
      number connectionId
      # Remote IP address.
      optional string remoteIPAddress
      # Remote port.
      optional integer remotePort
      # Specifies that the request was served from the disk cache.
      optional boolean fromDiskCache
      # Specifies that the request was served from the ServiceWorker.
      optional boolean fromServiceWorker
      # Specifies that the request was served from the prefetch cache.
      optional boolean fromPrefetchCache
      # Infomation about how Service Worker Static Router was used.
      experimental optional ServiceWorkerRouterInfo serviceWorkerRouterInfo
      # Total number of bytes received for this request so far.
      number encodedDataLength
      # Timing information for the given request.
      optional ResourceTiming timing
      # Response source of response from ServiceWorker.
      optional ServiceWorkerResponseSource serviceWorkerResponseSource
      # The time at which the returned response was generated.
      optional TimeSinceEpoch responseTime
      # Cache Storage Cache Name.
      optional string cacheStorageCacheName
      # Protocol used to fetch this request.
      optional string protocol
      # The reason why Chrome uses a specific transport protocol for HTTP semantics.
      experimental optional AlternateProtocolUsage alternateProtocolUsage
      # Security state of the request resource.
      Security.SecurityState securityState
      # Security details for the request.
      optional SecurityDetails securityDetails

  # WebSocket request data.
  type WebSocketRequest extends object
    properties
      # HTTP request headers.
      Headers headers

  # WebSocket response data.
  type WebSocketResponse extends object
    properties
      # HTTP response status code.
      integer status
      # HTTP response status text.
      string statusText
      # HTTP response headers.
      Headers headers
      # HTTP response headers text.
      optional string headersText
      # HTTP request headers.
      optional Headers requestHeaders
      # HTTP request headers text.
      optional string requestHeadersText

  # WebSocket message data. This represents an entire WebSocket message, not just a fragmented frame as the name suggests.
  type WebSocketFrame extends object
    properties
      # WebSocket message opcode.
      number opcode
      # WebSocket message mask.
      boolean mask
      # WebSocket message payload data.
      # If the opcode is 1, this is a text message and payloadData is a UTF-8 string.
      # If the opcode isn't 1, then payloadData is a base64 encoded string representing binary data.
      string payloadData

  # Information about the cached resource.
  type CachedResource extends object
    properties
      # Resource URL. This is the url of the original network request.
      string url
      # Type of this resource.
      ResourceType type
      # Cached response data.
      optional Response response
      # Cached response body size.
      number bodySize

  # Information about the request initiator.
  type Initiator extends object
    properties
      # Type of this initiator.
      enum type
        parser
        script
        preload
        SignedExchange
        preflight
        other
      # Initiator JavaScript stack trace, set for Script only.
      optional Runtime.StackTrace stack
      # Initiator URL, set for Parser type or for Script type (when script is importing module) or for SignedExchange type.
      optional string url
      # Initiator line number, set for Parser type or for Script type (when script is importing
      # module) (0-based).
      optional number lineNumber
      # Initiator column number, set for Parser type or for Script type (when script is importing
      # module) (0-based).
      optional number columnNumber
      # Set if another request triggered this request (e.g. preflight).
      optional RequestId requestId

  # Cookie object
  type Cookie extends object
    properties
      # Cookie name.
      string name
      # Cookie value.
      string value
      # Cookie domain.
      string domain
      # Cookie path.
      string path
      # Cookie expiration date as the number of seconds since the UNIX epoch.
      number expires
      # Cookie size.
      integer size
      # True if cookie is http-only.
      boolean httpOnly
      # True if cookie is secure.
      boolean secure
      # True in case of session cookie.
      boolean session
      # Cookie SameSite type.
      optional CookieSameSite sameSite
      # Cookie Priority
      experimental CookiePriority priority
      # True if cookie is SameParty.
      experimental deprecated boolean sameParty
      # Cookie source scheme type.
      experimental CookieSourceScheme sourceScheme
      # Cookie source port. Valid values are {-1, [1, 65535]}, -1 indicates an unspecified port.
      # An unspecified port value allows protocol clients to emulate legacy cookie scope for the port.
      # This is a temporary ability and it will be removed in the future.
      experimental integer sourcePort
      # Cookie partition key. The site of the top-level URL the browser was visiting at the start
      # of the request to the endpoint that set the cookie.
      experimental optional string partitionKey
      # True if cookie partition key is opaque.
      experimental optional boolean partitionKeyOpaque

  # Types of reasons why a cookie may not be stored from a response.
  experimental type SetCookieBlockedReason extends string
    enum
      # The cookie had the "Secure" attribute but was not received over a secure connection.
      SecureOnly
      # The cookie had the "SameSite=Strict" attribute but came from a cross-origin response.
      # This includes navigation requests intitiated by other origins.
      SameSiteStrict
      # The cookie had the "SameSite=Lax" attribute but came from a cross-origin response.
      SameSiteLax
      # The cookie didn't specify a "SameSite" attribute and was defaulted to "SameSite=Lax" and
      # broke the same rules specified in the SameSiteLax value.
      SameSiteUnspecifiedTreatedAsLax
      # The cookie had the "SameSite=None" attribute but did not specify the "Secure" attribute,
      # which is required in order to use "SameSite=None".
      SameSiteNoneInsecure
      # The cookie was not stored due to user preferences.
      UserPreferences
      # The cookie was blocked due to third-party cookie phaseout.
      ThirdPartyPhaseout
      # The cookie was blocked by third-party cookie blocking between sites in
      # the same First-Party Set.
      ThirdPartyBlockedInFirstPartySet
      # The syntax of the Set-Cookie header of the response was invalid.
      SyntaxError
      # The scheme of the connection is not allowed to store cookies.
      SchemeNotSupported
      # The cookie was not sent over a secure connection and would have overwritten a cookie with
      # the Secure attribute.
      OverwriteSecure
      # The cookie's domain attribute was invalid with regards to the current host url.
      InvalidDomain
      # The cookie used the "__Secure-" or "__Host-" prefix in its name and broke the additional
      # rules applied to cookies with these prefixes as defined in
      # https://tools.ietf.org/html/draft-west-cookie-prefixes-05
      InvalidPrefix
      # An unknown error was encountered when trying to store this cookie.
      UnknownError
      # The cookie had the "SameSite=Strict" attribute but came from a response
      # with the same registrable domain but a different scheme.
      # This includes navigation requests intitiated by other origins.
      # This is the "Schemeful Same-Site" version of the blocked reason.
      SchemefulSameSiteStrict
      # The cookie had the "SameSite=Lax" attribute but came from a response
      # with the same registrable domain but a different scheme.
      # This is the "Schemeful Same-Site" version of the blocked reason.
      SchemefulSameSiteLax
      # The cookie didn't specify a "SameSite" attribute and was defaulted to
      # "SameSite=Lax" and broke the same rules specified in the SchemefulSameSiteLax
      # value.
      # This is the "Schemeful Same-Site" version of the blocked reason.
      SchemefulSameSiteUnspecifiedTreatedAsLax
      # The cookie had the "SameParty" attribute but came from a cross-party response.
      SamePartyFromCrossPartyContext
      # The cookie had the "SameParty" attribute but did not specify the "Secure" attribute
      # (which is required in order to use "SameParty"); or specified the "SameSite=Strict"
      # attribute (which is forbidden when using "SameParty").
      SamePartyConflictsWithOtherAttributes
      # The cookie's name/value pair size exceeded the size limit defined in
      # RFC6265bis.
      NameValuePairExceedsMaxSize
      # The cookie contained a forbidden ASCII control character, or the tab
      # character if it appears in the middle of the cookie name, value, an
      # attribute name, or an attribute value.
      DisallowedCharacter
      # Cookie contains no content or only whitespace.
      NoCookieContent

  # Types of reasons why a cookie may not be sent with a request.
  experimental type CookieBlockedReason extends string
    enum
      # The cookie had the "Secure" attribute and the connection was not secure.
      SecureOnly
      # The cookie's path was not within the request url's path.
      NotOnPath
      # The cookie's domain is not configured to match the request url's domain, even though they
      # share a common TLD+1 (TLD+1 of foo.bar.example.com is example.com).
      DomainMismatch
      # The cookie had the "SameSite=Strict" attribute and the request was made on on a different
      # site. This includes navigation requests initiated by other sites.
      SameSiteStrict
      # The cookie had the "SameSite=Lax" attribute and the request was made on a different site.
      # This does not include navigation requests initiated by other sites.
      SameSiteLax
      # The cookie didn't specify a SameSite attribute when it was stored and was defaulted to
      # "SameSite=Lax" and broke the same rules specified in the SameSiteLax value. The cookie had
      # to have been set with "SameSite=None" to enable third-party usage.
      SameSiteUnspecifiedTreatedAsLax
      # The cookie had the "SameSite=None" attribute and the connection was not secure. Cookies
      # without SameSite restrictions must be sent over a secure connection.
      SameSiteNoneInsecure
      # The cookie was not sent due to user preferences.
      UserPreferences
      # The cookie was blocked due to third-party cookie phaseout.
      ThirdPartyPhaseout
      # The cookie was blocked by third-party cookie blocking between sites in
      # the same First-Party Set.
      ThirdPartyBlockedInFirstPartySet
      # An unknown error was encountered when trying to send this cookie.
      UnknownError
      # The cookie had the "SameSite=Strict" attribute but came from a response
      # with the same registrable domain but a different scheme.
      # This includes navigation requests intitiated by other origins.
      # This is the "Schemeful Same-Site" version of the blocked reason.
      SchemefulSameSiteStrict
      # The cookie had the "SameSite=Lax" attribute but came from a response
      # with the same registrable domain but a different scheme.
      # This is the "Schemeful Same-Site" version of the blocked reason.
      SchemefulSameSiteLax
      # The cookie didn't specify a "SameSite" attribute and was defaulted to
      # "SameSite=Lax" and broke the same rules specified in the SchemefulSameSiteLax
      # value.
      # This is the "Schemeful Same-Site" version of the blocked reason.
      SchemefulSameSiteUnspecifiedTreatedAsLax
      # The cookie had the "SameParty" attribute and the request was made from a cross-party context.
      SamePartyFromCrossPartyContext
      # The cookie's name/value pair size exceeded the size limit defined in
      # RFC6265bis.
      NameValuePairExceedsMaxSize

  # A cookie which was not stored from a response with the corresponding reason.
  experimental type BlockedSetCookieWithReason extends object
    properties
      # The reason(s) this cookie was blocked.
      array of SetCookieBlockedReason blockedReasons
      # The string representing this individual cookie as it would appear in the header.
      # This is not the entire "cookie" or "set-cookie" header which could have multiple cookies.
      string cookieLine
      # The cookie object which represents the cookie which was not stored. It is optional because
      # sometimes complete cookie information is not available, such as in the case of parsing
      # errors.
      optional Cookie cookie

  # A cookie with was not sent with a request with the corresponding reason.
  experimental type BlockedCookieWithReason extends object
    properties
      # The reason(s) the cookie was blocked.
      array of CookieBlockedReason blockedReasons
      # The cookie object representing the cookie which was not sent.
      Cookie cookie

  # Cookie parameter object
  type CookieParam extends object
    properties
      # Cookie name.
      string name
      # Cookie value.
      string value
      # The request-URI to associate with the setting of the cookie. This value can affect the
      # default domain, path, source port, and source scheme values of the created cookie.
      optional string url
      # Cookie domain.
      optional string domain
      # Cookie path.
      optional string path
      # True if cookie is secure.
      optional boolean secure
      # True if cookie is http-only.
      optional boolean httpOnly
      # Cookie SameSite type.
      optional CookieSameSite sameSite
      # Cookie expiration date, session cookie if not set
      optional TimeSinceEpoch expires
      # Cookie Priority.
      experimental optional CookiePriority priority
      # True if cookie is SameParty.
      experimental optional boolean sameParty
      # Cookie source scheme type.
      experimental optional CookieSourceScheme sourceScheme
      # Cookie source port. Valid values are {-1, [1, 65535]}, -1 indicates an unspecified port.
      # An unspecified port value allows protocol clients to emulate legacy cookie scope for the port.
      # This is a temporary ability and it will be removed in the future.
      experimental optional integer sourcePort
      # Cookie partition key. The site of the top-level URL the browser was visiting at the start
      # of the request to the endpoint that set the cookie.
      # If not set, the cookie will be set as not partitioned.
      experimental optional string partitionKey

  # Authorization challenge for HTTP status code 401 or 407.
  experimental type AuthChallenge extends object
    properties
      # Source of the authentication challenge.
      optional enum source
        Server
        Proxy
      # Origin of the challenger.
      string origin
      # The authentication scheme used, such as basic or digest
      string scheme
      # The realm of the challenge. May be empty.
      string realm

  # Response to an AuthChallenge.
  experimental type AuthChallengeResponse extends object
    properties
      # The decision on what to do in response to the authorization challenge.  Default means
      # deferring to the default behavior of the net stack, which will likely either the Cancel
      # authentication or display a popup dialog box.
      enum response
        Default
        CancelAuth
        ProvideCredentials
      # The username to provide, possibly empty. Should only be set if response is
      # ProvideCredentials.
      optional string username
      # The password to provide, possibly empty. Should only be set if response is
      # ProvideCredentials.
      optional string password

  # Stages of the interception to begin intercepting. Request will intercept before the request is
  # sent. Response will intercept after the response is received.
  experimental type InterceptionStage extends string
    enum
      Request
      HeadersReceived

  # Request pattern for interception.
  experimental type RequestPattern extends object
    properties
      # Wildcards (`'*'` -> zero or more, `'?'` -> exactly one) are allowed. Escape character is
      # backslash. Omitting is equivalent to `"*"`.
      optional string urlPattern
      # If set, only requests for matching resource types will be intercepted.
      optional ResourceType resourceType
      # Stage at which to begin intercepting requests. Default is Request.
      optional InterceptionStage interceptionStage

  # Information about a signed exchange signature.
  # https://wicg.github.io/webpackage/draft-yasskin-httpbis-origin-signed-exchanges-impl.html#rfc.section.3.1
  experimental type SignedExchangeSignature extends object
    properties
      # Signed exchange signature label.
      string label
      # The hex string of signed exchange signature.
      string signature
      # Signed exchange signature integrity.
      string integrity
      # Signed exchange signature cert Url.
      optional string certUrl
      # The hex string of signed exchange signature cert sha256.
      optional string certSha256
      # Signed exchange signature validity Url.
      string validityUrl
      # Signed exchange signature date.
      integer date
      # Signed exchange signature expires.
      integer expires
      # The encoded certificates.
      optional array of string certificates

  # Information about a signed exchange header.
  # https://wicg.github.io/webpackage/draft-yasskin-httpbis-origin-signed-exchanges-impl.html#cbor-representation
  experimental type SignedExchangeHeader extends object
    properties
      # Signed exchange request URL.
      string requestUrl
      # Signed exchange response code.
      integer responseCode
      # Signed exchange response headers.
      Headers responseHeaders
      # Signed exchange response signature.
      array of SignedExchangeSignature signatures
      # Signed exchange header integrity hash in the form of `sha256-<base64-hash-value>`.
      string headerIntegrity

  # Field type for a signed exchange related error.
  experimental type SignedExchangeErrorField extends string
    enum
      signatureSig
      signatureIntegrity
      signatureCertUrl
      signatureCertSha256
      signatureValidityUrl
      signatureTimestamps

  # Information about a signed exchange response.
  experimental type SignedExchangeError extends object
    properties
      # Error message.
      string message
      # The index of the signature which caused the error.
      optional integer signatureIndex
      # The field which caused the error.
      optional SignedExchangeErrorField errorField

  # Information about a signed exchange response.
  experimental type SignedExchangeInfo extends object
    properties
      # The outer response of signed HTTP exchange which was received from network.
      Response outerResponse
      # Information about the signed exchange header.
      optional SignedExchangeHeader header
      # Security details for the signed exchange header.
      optional SecurityDetails securityDetails
      # Errors occurred while handling the signed exchagne.
      optional array of SignedExchangeError errors

  # List of content encodings supported by the backend.
  experimental type ContentEncoding extends string
    enum
      deflate
      gzip
      br
      zstd

  # Sets a list of content encodings that will be accepted. Empty list means no encoding is accepted.
  experimental command setAcceptedEncodings
    parameters
      # List of accepted content encodings.
      array of ContentEncoding encodings

  # Clears accepted encodings set by setAcceptedEncodings
  experimental command clearAcceptedEncodingsOverride

  # Tells whether clearing browser cache is supported.
  deprecated command canClearBrowserCache
    returns
      # True if browser cache can be cleared.
      boolean result

  # Tells whether clearing browser cookies is supported.
  deprecated command canClearBrowserCookies
    returns
      # True if browser cookies can be cleared.
      boolean result

  # Tells whether emulation of network conditions is supported.
  deprecated command canEmulateNetworkConditions
    returns
      # True if emulation of network conditions is supported.
      boolean result

  # Clears browser cache.
  command clearBrowserCache

  # Clears browser cookies.
  command clearBrowserCookies

  # Response to Network.requestIntercepted which either modifies the request to continue with any
  # modifications, or blocks it, or completes it with the provided response bytes. If a network
  # fetch occurs as a result which encounters a redirect an additional Network.requestIntercepted
  # event will be sent with the same InterceptionId.
  # Deprecated, use Fetch.continueRequest, Fetch.fulfillRequest and Fetch.failRequest instead.
  experimental deprecated command continueInterceptedRequest
    parameters
      InterceptionId interceptionId
      # If set this causes the request to fail with the given reason. Passing `Aborted` for requests
      # marked with `isNavigationRequest` also cancels the navigation. Must not be set in response
      # to an authChallenge.
      optional ErrorReason errorReason
      # If set the requests completes using with the provided base64 encoded raw response, including
      # HTTP status line and headers etc... Must not be set in response to an authChallenge.
      optional binary rawResponse
      # If set the request url will be modified in a way that's not observable by page. Must not be
      # set in response to an authChallenge.
      optional string url
      # If set this allows the request method to be overridden. Must not be set in response to an
      # authChallenge.
      optional string method
      # If set this allows postData to be set. Must not be set in response to an authChallenge.
      optional string postData
      # If set this allows the request headers to be changed. Must not be set in response to an
      # authChallenge.
      optional Headers headers
      # Response to a requestIntercepted with an authChallenge. Must not be set otherwise.
      optional AuthChallengeResponse authChallengeResponse

  # Deletes browser cookies with matching name and url or domain/path pair.
  command deleteCookies
    parameters
      # Name of the cookies to remove.
      string name
      # If specified, deletes all the cookies with the given name where domain and path match
      # provided URL.
      optional string url
      # If specified, deletes only cookies with the exact domain.
      optional string domain
      # If specified, deletes only cookies with the exact path.
      optional string path

  # Disables network tracking, prevents network events from being sent to the client.
  command disable

  # Activates emulation of network conditions.
  command emulateNetworkConditions
    parameters
      # True to emulate internet disconnection.
      boolean offline
      # Minimum latency from request sent to response headers received (ms).
      number latency
      # Maximal aggregated download throughput (bytes/sec). -1 disables download throttling.
      number downloadThroughput
      # Maximal aggregated upload throughput (bytes/sec).  -1 disables upload throttling.
      number uploadThroughput
      # Connection type if known.
      optional ConnectionType connectionType

  # Enables network tracking, network events will now be delivered to the client.
  command enable
    parameters
      # Buffer size in bytes to use when preserving network payloads (XHRs, etc).
      experimental optional integer maxTotalBufferSize
      # Per-resource buffer size in bytes to use when preserving network payloads (XHRs, etc).
      experimental optional integer maxResourceBufferSize
      # Longest post body size (in bytes) that would be included in requestWillBeSent notification
      optional integer maxPostDataSize

  # Returns all browser cookies. Depending on the backend support, will return detailed cookie
  # information in the `cookies` field.
  # Deprecated. Use Storage.getCookies instead.
  deprecated command getAllCookies
    returns
      # Array of cookie objects.
      array of Cookie cookies

  # Returns the DER-encoded certificate.
  experimental command getCertificate
    parameters
      # Origin to get certificate for.
      string origin
    returns
      array of string tableNames

  # Returns all browser cookies for the current URL. Depending on the backend support, will return
  # detailed cookie information in the `cookies` field.
  command getCookies
    parameters
      # The list of URLs for which applicable cookies will be fetched.
      # If not specified, it's assumed to be set to the list containing
      # the URLs of the page and all of its subframes.
      optional array of string urls
    returns
      # Array of cookie objects.
      array of Cookie cookies

  # Returns content served for the given request.
  command getResponseBody
    parameters
      # Identifier of the network request to get content for.
      RequestId requestId
    returns
      # Response body.
      string body
      # True, if content was sent as base64.
      boolean base64Encoded

  # Returns post data sent with the request. Returns an error when no data was sent with the request.
  command getRequestPostData
    parameters
      # Identifier of the network request to get content for.
      RequestId requestId
    returns
      # Request body string, omitting files from multipart requests
      string postData

  # Returns content served for the given currently intercepted request.
  experimental command getResponseBodyForInterception
    parameters
      # Identifier for the intercepted request to get body for.
      InterceptionId interceptionId
    returns
      # Response body.
      string body
      # True, if content was sent as base64.
      boolean base64Encoded

  # Returns a handle to the stream representing the response body. Note that after this command,
  # the intercepted request can't be continued as is -- you either need to cancel it or to provide
  # the response body. The stream only supports sequential read, IO.read will fail if the position
  # is specified.
  experimental command takeResponseBodyForInterceptionAsStream
    parameters
      InterceptionId interceptionId
    returns
      IO.StreamHandle stream

  # This method sends a new XMLHttpRequest which is identical to the original one. The following
  # parameters should be identical: method, url, async, request body, extra headers, withCredentials
  # attribute, user, password.
  experimental command replayXHR
    parameters
      # Identifier of XHR to replay.
      RequestId requestId

  # Searches for given string in response content.
  experimental command searchInResponseBody
    parameters
      # Identifier of the network response to search.
      RequestId requestId
      # String to search for.
      string query
      # If true, search is case sensitive.
      optional boolean caseSensitive
      # If true, treats string parameter as regex.
      optional boolean isRegex
    returns
      # List of search matches.
      array of Debugger.SearchMatch result

  # Blocks URLs from loading.
  experimental command setBlockedURLs
    parameters
      # URL patterns to block. Wildcards ('*') are allowed.
      array of string urls

  # Toggles ignoring of service worker for each request.
  experimental command setBypassServiceWorker
    parameters
      # Bypass service worker and load from network.
      boolean bypass

  # Toggles ignoring cache for each request. If `true`, cache will not be used.
  command setCacheDisabled
    parameters
      # Cache disabled state.
      boolean cacheDisabled

  # Sets a cookie with the given cookie data; may overwrite equivalent cookies if they exist.
  command setCookie
    parameters
      # Cookie name.
      string name
      # Cookie value.
      string value
      # The request-URI to associate with the setting of the cookie. This value can affect the
      # default domain, path, source port, and source scheme values of the created cookie.
      optional string url
      # Cookie domain.
      optional string domain
      # Cookie path.
      optional string path
      # True if cookie is secure.
      optional boolean secure
      # True if cookie is http-only.
      optional boolean httpOnly
      # Cookie SameSite type.
      optional CookieSameSite sameSite
      # Cookie expiration date, session cookie if not set
      optional TimeSinceEpoch expires
      # Cookie Priority type.
      experimental optional CookiePriority priority
      # True if cookie is SameParty.
      experimental optional boolean sameParty
      # Cookie source scheme type.
      experimental optional CookieSourceScheme sourceScheme
      # Cookie source port. Valid values are {-1, [1, 65535]}, -1 indicates an unspecified port.
      # An unspecified port value allows protocol clients to emulate legacy cookie scope for the port.
      # This is a temporary ability and it will be removed in the future.
      experimental optional integer sourcePort
      # Cookie partition key. The site of the top-level URL the browser was visiting at the start
      # of the request to the endpoint that set the cookie.
      # If not set, the cookie will be set as not partitioned.
      experimental optional string partitionKey
    returns
      # Always set to true. If an error occurs, the response indicates protocol error.
      deprecated boolean success

  # Sets given cookies.
  command setCookies
    parameters
      # Cookies to be set.
      array of CookieParam cookies

  # Specifies whether to always send extra HTTP headers with the requests from this page.
  command setExtraHTTPHeaders
    parameters
      # Map with extra HTTP headers.
      Headers headers

  # Specifies whether to attach a page script stack id in requests
  experimental command setAttachDebugStack
    parameters
      # Whether to attach a page script stack for debugging purpose.
      boolean enabled

  # Sets the requests to intercept that match the provided patterns and optionally resource types.
  # Deprecated, please use Fetch.enable instead.
  experimental deprecated command setRequestInterception
    parameters
      # Requests matching any of these patterns will be forwarded and wait for the corresponding
      # continueInterceptedRequest call.
      array of RequestPattern patterns

  # Allows overriding user agent with the given string.
  command setUserAgentOverride
    redirect Emulation
    parameters
      # User agent to use.
      string userAgent
      # Browser language to emulate.
      optional string acceptLanguage
      # The platform navigator.platform should return.
      optional string platform
      # To be sent in Sec-CH-UA-* headers and returned in navigator.userAgentData
      experimental optional Emulation.UserAgentMetadata userAgentMetadata


  # Fired when data chunk was received over the network.
  event dataReceived
    parameters
      # Request identifier.
      RequestId requestId
      # Timestamp.
      MonotonicTime timestamp
      # Data chunk length.
      integer dataLength
      # Actual bytes received (might be less than dataLength for compressed encodings).
      integer encodedDataLength

  # Fired when EventSource message is received.
  event eventSourceMessageReceived
    parameters
      # Request identifier.
      RequestId requestId
      # Timestamp.
      MonotonicTime timestamp
      # Message type.
      string eventName
      # Message identifier.
      string eventId
      # Message content.
      string data

  # Fired when HTTP request has failed to load.
  event loadingFailed
    parameters
      # Request identifier.
      RequestId requestId
      # Timestamp.
      MonotonicTime timestamp
      # Resource type.
      ResourceType type
      # User friendly error message.
      string errorText
      # True if loading was canceled.
      optional boolean canceled
      # The reason why loading was blocked, if any.
      optional BlockedReason blockedReason
       # The reason why loading was blocked by CORS, if any.
      optional CorsErrorStatus corsErrorStatus

  # Fired when HTTP request has finished loading.
  event loadingFinished
    parameters
      # Request identifier.
      RequestId requestId
      # Timestamp.
      MonotonicTime timestamp
      # Total number of bytes received for this request.
      number encodedDataLength

  # Details of an intercepted HTTP request, which must be either allowed, blocked, modified or
  # mocked.
  # Deprecated, use Fetch.requestPaused instead.
  experimental deprecated event requestIntercepted
    parameters
      # Each request the page makes will have a unique id, however if any redirects are encountered
      # while processing that fetch, they will be reported with the same id as the original fetch.
      # Likewise if HTTP authentication is needed then the same fetch id will be used.
      InterceptionId interceptionId
      Request request
      # The id of the frame that initiated the request.
      Page.FrameId frameId
      # How the requested resource will be used.
      ResourceType resourceType
      # Whether this is a navigation request, which can abort the navigation completely.
      boolean isNavigationRequest
      # Set if the request is a navigation that will result in a download.
      # Only present after response is received from the server (i.e. HeadersReceived stage).
      optional boolean isDownload
      # Redirect location, only sent if a redirect was intercepted.
      optional string redirectUrl
      # Details of the Authorization Challenge encountered. If this is set then
      # continueInterceptedRequest must contain an authChallengeResponse.
      optional AuthChallenge authChallenge
      # Response error if intercepted at response stage or if redirect occurred while intercepting
      # request.
      optional ErrorReason responseErrorReason
      # Response code if intercepted at response stage or if redirect occurred while intercepting
      # request or auth retry occurred.
      optional integer responseStatusCode
      # Response headers if intercepted at the response stage or if redirect occurred while
      # intercepting request or auth retry occurred.
      optional Headers responseHeaders
      # If the intercepted request had a corresponding requestWillBeSent event fired for it, then
      # this requestId will be the same as the requestId present in the requestWillBeSent event.
      optional RequestId requestId

  # Fired if request ended up loading from cache.
  event requestServedFromCache
    parameters
      # Request identifier.
      RequestId requestId

  # Fired when page is about to send HTTP request.
  event requestWillBeSent
    parameters
      # Request identifier.
      RequestId requestId
      # Loader identifier. Empty string if the request is fetched from worker.
      LoaderId loaderId
      # URL of the document this request is loaded for.
      string documentURL
      # Request data.
      Request request
      # Timestamp.
      MonotonicTime timestamp
      # Timestamp.
      TimeSinceEpoch wallTime
      # Request initiator.
      Initiator initiator
      # In the case that redirectResponse is populated, this flag indicates whether
      # requestWillBeSentExtraInfo and responseReceivedExtraInfo events will be or were emitted
      # for the request which was just redirected.
      experimental boolean redirectHasExtraInfo
      # Redirect response data.
      optional Response redirectResponse
      # Type of this resource.
      optional ResourceType type
      # Frame identifier.
      optional Page.FrameId frameId
      # Whether the request is initiated by a user gesture. Defaults to false.
      optional boolean hasUserGesture

  # Fired when resource loading priority is changed
  experimental event resourceChangedPriority
    parameters
      # Request identifier.
      RequestId requestId
      # New priority
      ResourcePriority newPriority
      # Timestamp.
      MonotonicTime timestamp

  # Fired when a signed exchange was received over the network
  experimental event signedExchangeReceived
    parameters
      # Request identifier.
      RequestId requestId
      # Information about the signed exchange response.
      SignedExchangeInfo info

  # Fired when HTTP response is available.
  event responseReceived
    parameters
      # Request identifier.
      RequestId requestId
      # Loader identifier. Empty string if the request is fetched from worker.
      LoaderId loaderId
      # Timestamp.
      MonotonicTime timestamp
      # Resource type.
      ResourceType type
      # Response data.
      Response response
      # Indicates whether requestWillBeSentExtraInfo and responseReceivedExtraInfo events will be
      # or were emitted for this request.
      experimental boolean hasExtraInfo
      # Frame identifier.
      optional Page.FrameId frameId

  # Fired when WebSocket is closed.
  event webSocketClosed
    parameters
      # Request identifier.
      RequestId requestId
      # Timestamp.
      MonotonicTime timestamp

  # Fired upon WebSocket creation.
  event webSocketCreated
    parameters
      # Request identifier.
      RequestId requestId
      # WebSocket request URL.
      string url
      # Request initiator.
      optional Initiator initiator

  # Fired when WebSocket message error occurs.
  event webSocketFrameError
    parameters
      # Request identifier.
      RequestId requestId
      # Timestamp.
      MonotonicTime timestamp
      # WebSocket error message.
      string errorMessage

  # Fired when WebSocket message is received.
  event webSocketFrameReceived
    parameters
      # Request identifier.
      RequestId requestId
      # Timestamp.
      MonotonicTime timestamp
      # WebSocket response data.
      WebSocketFrame response

  # Fired when WebSocket message is sent.
  event webSocketFrameSent
    parameters
      # Request identifier.
      RequestId requestId
      # Timestamp.
      MonotonicTime timestamp
      # WebSocket response data.
      WebSocketFrame response

  # Fired when WebSocket handshake response becomes available.
  event webSocketHandshakeResponseReceived
    parameters
      # Request identifier.
      RequestId requestId
      # Timestamp.
      MonotonicTime timestamp
      # WebSocket response data.
      WebSocketResponse response

  # Fired when WebSocket is about to initiate handshake.
  event webSocketWillSendHandshakeRequest
    parameters
      # Request identifier.
      RequestId requestId
      # Timestamp.
      MonotonicTime timestamp
      # UTC Timestamp.
      TimeSinceEpoch wallTime
      # WebSocket request data.
      WebSocketRequest request

  # Fired upon WebTransport creation.
  event webTransportCreated
    parameters
      # WebTransport identifier.
      RequestId transportId
      # WebTransport request URL.
      string url
      # Timestamp.
      MonotonicTime timestamp
      # Request initiator.
      optional Initiator initiator

  # Fired when WebTransport handshake is finished.
  event webTransportConnectionEstablished
    parameters
      # WebTransport identifier.
      RequestId transportId
      # Timestamp.
      MonotonicTime timestamp

  # Fired when WebTransport is disposed.
  event webTransportClosed
    parameters
      # WebTransport identifier.
      RequestId transportId
      # Timestamp.
      MonotonicTime timestamp

  experimental type PrivateNetworkRequestPolicy extends string
    enum
      Allow
      BlockFromInsecureToMorePrivate
      WarnFromInsecureToMorePrivate
      PreflightBlock
      PreflightWarn

  experimental type IPAddressSpace extends string
    enum
      Local
      Private
      Public
      Unknown

  experimental type ConnectTiming extends object
    properties
      # Timing's requestTime is a baseline in seconds, while the other numbers are ticks in
      # milliseconds relatively to this requestTime. Matches ResourceTiming's requestTime for
      # the same request (but not for redirected requests).
      number requestTime

  experimental type ClientSecurityState extends object
    properties
      boolean initiatorIsSecureContext
      IPAddressSpace initiatorIPAddressSpace
      PrivateNetworkRequestPolicy privateNetworkRequestPolicy

  # Fired when additional information about a requestWillBeSent event is available from the
  # network stack. Not every requestWillBeSent event will have an additional
  # requestWillBeSentExtraInfo fired for it, and there is no guarantee whether requestWillBeSent
  # or requestWillBeSentExtraInfo will be fired first for the same request.
  experimental event requestWillBeSentExtraInfo
    parameters
      # Request identifier. Used to match this information to an existing requestWillBeSent event.
      RequestId requestId
      # A list of cookies potentially associated to the requested URL. This includes both cookies sent with
      # the request and the ones not sent; the latter are distinguished by having blockedReason field set.
      array of BlockedCookieWithReason associatedCookies
      # Raw request headers as they will be sent over the wire.
      Headers headers
      # Connection timing information for the request.
      experimental ConnectTiming connectTiming
      # The client security state set for the request.
      optional ClientSecurityState clientSecurityState
      # Whether the site has partitioned cookies stored in a partition different than the current one.
      optional boolean siteHasCookieInOtherPartition

  # Fired when additional information about a responseReceived event is available from the network
  # stack. Not every responseReceived event will have an additional responseReceivedExtraInfo for
  # it, and responseReceivedExtraInfo may be fired before or after responseReceived.
  experimental event responseReceivedExtraInfo
    parameters
      # Request identifier. Used to match this information to another responseReceived event.
      RequestId requestId
      # A list of cookies which were not stored from the response along with the corresponding
      # reasons for blocking. The cookies here may not be valid due to syntax errors, which
      # are represented by the invalid cookie line string instead of a proper cookie.
      array of BlockedSetCookieWithReason blockedCookies
      # Raw response headers as they were received over the wire.
      Headers headers
      # The IP address space of the resource. The address space can only be determined once the transport
      # established the connection, so we can't send it in `requestWillBeSentExtraInfo`.
      IPAddressSpace resourceIPAddressSpace
      # The status code of the response. This is useful in cases the request failed and no responseReceived
      # event is triggered, which is the case for, e.g., CORS errors. This is also the correct status code
      # for cached requests, where the status in responseReceived is a 200 and this will be 304.
      integer statusCode
      # Raw response header text as it was received over the wire. The raw text may not always be
      # available, such as in the case of HTTP/2 or QUIC.
      optional string headersText
      # The cookie partition key that will be used to store partitioned cookies set in this response.
      # Only sent when partitioned cookies are enabled.
      optional string cookiePartitionKey
      # True if partitioned cookies are enabled, but the partition key is not serializeable to string.
      optional boolean cookiePartitionKeyOpaque

  # Fired exactly once for each Trust Token operation. Depending on
  # the type of the operation and whether the operation succeeded or
  # failed, the event is fired before the corresponding request was sent
  # or after the response was received.
  experimental event trustTokenOperationDone
    parameters
      # Detailed success or error status of the operation.
      # 'AlreadyExists' also signifies a successful operation, as the result
      # of the operation already exists und thus, the operation was abort
      # preemptively (e.g. a cache hit).
      enum status
        Ok
        InvalidArgument
        MissingIssuerKeys
        FailedPrecondition
        ResourceExhausted
        AlreadyExists
        Unavailable
        Unauthorized
        BadResponse
        InternalError
        UnknownError
        FulfilledLocally
      TrustTokenOperationType type
      RequestId requestId
      # Top level origin. The context in which the operation was attempted.
      optional string topLevelOrigin
      # Origin of the issuer in case of a "Issuance" or "Redemption" operation.
      optional string issuerOrigin
      # The number of obtained Trust Tokens on a successful "Issuance" operation.
      optional integer issuedTokenCount

  # Fired once when parsing the .wbn file has succeeded.
  # The event contains the information about the web bundle contents.
  experimental event subresourceWebBundleMetadataReceived
    parameters
      # Request identifier. Used to match this information to another event.
      RequestId requestId
      # A list of URLs of resources in the subresource Web Bundle.
      array of string urls

  # Fired once when parsing the .wbn file has failed.
  experimental event subresourceWebBundleMetadataError
    parameters
      # Request identifier. Used to match this information to another event.
      RequestId requestId
      # Error message
      string errorMessage

  # Fired when handling requests for resources within a .wbn file.
  # Note: this will only be fired for resources that are requested by the webpage.
  experimental event subresourceWebBundleInnerResponseParsed
    parameters
      # Request identifier of the subresource request
      RequestId innerRequestId
      # URL of the subresource resource.
      string innerRequestURL
      # Bundle request identifier. Used to match this information to another event.
      # This made be absent in case when the instrumentation was enabled only
      # after webbundle was parsed.
      optional RequestId bundleRequestId

  # Fired when request for resources within a .wbn file failed.
  experimental event subresourceWebBundleInnerResponseError
    parameters
      # Request identifier of the subresource request
      RequestId innerRequestId
      # URL of the subresource resource.
      string innerRequestURL
      # Error message
      string errorMessage
      # Bundle request identifier. Used to match this information to another event.
      # This made be absent in case when the instrumentation was enabled only
      # after webbundle was parsed.
      optional RequestId bundleRequestId

  experimental type CrossOriginOpenerPolicyValue extends string
    enum
      SameOrigin
      SameOriginAllowPopups
      RestrictProperties
      UnsafeNone
      SameOriginPlusCoep
      RestrictPropertiesPlusCoep

  experimental type CrossOriginOpenerPolicyStatus extends object
    properties
      CrossOriginOpenerPolicyValue value
      CrossOriginOpenerPolicyValue reportOnlyValue
      optional string reportingEndpoint
      optional string reportOnlyReportingEndpoint

  experimental type CrossOriginEmbedderPolicyValue extends string
    enum
      None
      Credentialless
      RequireCorp

  experimental type CrossOriginEmbedderPolicyStatus extends object
    properties
      CrossOriginEmbedderPolicyValue value
      CrossOriginEmbedderPolicyValue reportOnlyValue
      optional string reportingEndpoint
      optional string reportOnlyReportingEndpoint

  experimental type ContentSecurityPolicySource extends string
    enum
      HTTP
      Meta

  experimental type ContentSecurityPolicyStatus extends object
    properties
      string effectiveDirectives
      boolean isEnforced
      ContentSecurityPolicySource source

  experimental type SecurityIsolationStatus extends object
    properties
      optional CrossOriginOpenerPolicyStatus coop
      optional CrossOriginEmbedderPolicyStatus coep
      optional array of ContentSecurityPolicyStatus csp

  # Returns information about the COEP/COOP isolation status.
  experimental command getSecurityIsolationStatus
    parameters
      # If no frameId is provided, the status of the target is provided.
      optional Page.FrameId frameId
    returns
      SecurityIsolationStatus status

  # Enables tracking for the Reporting API, events generated by the Reporting API will now be delivered to the client.
  # Enabling triggers 'reportingApiReportAdded' for all existing reports.
  experimental command enableReportingApi
    parameters
      # Whether to enable or disable events for the Reporting API
      boolean enable

  # The status of a Reporting API report.
  experimental type ReportStatus extends string
    enum
      # Report has been queued and no attempt has been made to deliver it yet,
      # or attempted previous upload failed (impermanently).
      Queued
      # There is an ongoing attempt to upload this report.
      Pending
      # Deletion of this report was requested while it was pending, so it will
      # be removed after possibly outstanding upload attempts complete (successful
      # or not).
      MarkedForRemoval
      # Successfully uploaded and MarkedForRemoval.
      Success

  experimental type ReportId extends string

  # An object representing a report generated by the Reporting API.
  experimental type ReportingApiReport extends object
    properties
      ReportId id
      # The URL of the document that triggered the report.
      string initiatorUrl
      # The name of the endpoint group that should be used to deliver the report.
      string destination
      # The type of the report (specifies the set of data that is contained in the report body).
      string type
      # When the report was generated.
      Network.TimeSinceEpoch timestamp
      # How many uploads deep the related request was.
      integer depth
      # The number of delivery attempts made so far, not including an active attempt.
      integer completedAttempts
      object body
      ReportStatus status

  # Is sent whenever a new report is added.
  # And after 'enableReportingApi' for all existing reports.
  experimental event reportingApiReportAdded
    parameters
      ReportingApiReport report

  experimental event reportingApiReportUpdated
    parameters
      ReportingApiReport report

  experimental type ReportingApiEndpoint extends object
    properties
      # The URL of the endpoint to which reports may be delivered.
      string url
      # Name of the endpoint group.
      string groupName

  experimental event reportingApiEndpointsChangedForOrigin
    parameters
      # Origin of the document(s) which configured the endpoints.
      string origin
      array of ReportingApiEndpoint endpoints

  # An object providing the result of a network resource load.
  experimental type LoadNetworkResourcePageResult extends object
    properties
      boolean success
      # Optional values used for error reporting.
      optional number netError
      optional string netErrorName
      optional number httpStatusCode
      # If successful, one of the following two fields holds the result.
      optional IO.StreamHandle stream
      # Response headers.
      optional Network.Headers headers

  # An options object that may be extended later to better support CORS,
  # CORB and streaming.
  experimental type LoadNetworkResourceOptions extends object
    properties
      boolean disableCache
      boolean includeCredentials

  # Fetches the resource and returns the content.
  experimental command loadNetworkResource
    parameters
      # Frame id to get the resource for. Mandatory for frame targets, and
      # should be omitted for worker targets.
      optional Page.FrameId frameId
      # URL of the resource to get content for.
      string url
      # Options for the request.
      LoadNetworkResourceOptions options
    returns
      LoadNetworkResourcePageResult resource

# This domain provides various functionality related to drawing atop the inspected page.
experimental domain Overlay
  depends on DOM
  depends on Page
  depends on Runtime

  # Configuration data for drawing the source order of an elements children.
  type SourceOrderConfig extends object
    properties
      # the color to outline the givent element in.
      DOM.RGBA parentOutlineColor
      # the color to outline the child elements in.
      DOM.RGBA childOutlineColor

  # Configuration data for the highlighting of Grid elements.
  type GridHighlightConfig extends object
    properties
      # Whether the extension lines from grid cells to the rulers should be shown (default: false).
      optional boolean showGridExtensionLines
      # Show Positive line number labels (default: false).
      optional boolean showPositiveLineNumbers
      # Show Negative line number labels (default: false).
      optional boolean showNegativeLineNumbers
      # Show area name labels (default: false).
      optional boolean showAreaNames
      # Show line name labels (default: false).
      optional boolean showLineNames
      # Show track size labels (default: false).
      optional boolean showTrackSizes
      # The grid container border highlight color (default: transparent).
      optional DOM.RGBA gridBorderColor
      # The cell border color (default: transparent). Deprecated, please use rowLineColor and columnLineColor instead.
      deprecated optional DOM.RGBA cellBorderColor
      # The row line color (default: transparent).
      optional DOM.RGBA rowLineColor
      # The column line color (default: transparent).
      optional DOM.RGBA columnLineColor
      # Whether the grid border is dashed (default: false).
      optional boolean gridBorderDash
      # Whether the cell border is dashed (default: false). Deprecated, please us rowLineDash and columnLineDash instead.
      deprecated optional boolean cellBorderDash
      # Whether row lines are dashed (default: false).
      optional boolean rowLineDash
      # Whether column lines are dashed (default: false).
      optional boolean columnLineDash
      # The row gap highlight fill color (default: transparent).
      optional DOM.RGBA rowGapColor
      # The row gap hatching fill color (default: transparent).
      optional DOM.RGBA rowHatchColor
      # The column gap highlight fill color (default: transparent).
      optional DOM.RGBA columnGapColor
      # The column gap hatching fill color (default: transparent).
      optional DOM.RGBA columnHatchColor
      # The named grid areas border color (Default: transparent).
      optional DOM.RGBA areaBorderColor
      # The grid container background color (Default: transparent).
      optional DOM.RGBA gridBackgroundColor

  # Configuration data for the highlighting of Flex container elements.
  type FlexContainerHighlightConfig extends object
    properties
      # The style of the container border
      optional LineStyle containerBorder
      # The style of the separator between lines
      optional LineStyle lineSeparator
      # The style of the separator between items
      optional LineStyle itemSeparator
      # Style of content-distribution space on the main axis (justify-content).
      optional BoxStyle mainDistributedSpace
      # Style of content-distribution space on the cross axis (align-content).
      optional BoxStyle crossDistributedSpace
      # Style of empty space caused by row gaps (gap/row-gap).
      optional BoxStyle rowGapSpace
      # Style of empty space caused by columns gaps (gap/column-gap).
      optional BoxStyle columnGapSpace
      # Style of the self-alignment line (align-items).
      optional LineStyle crossAlignment

  # Configuration data for the highlighting of Flex item elements.
  type FlexItemHighlightConfig extends object
    properties
      # Style of the box representing the item's base size
      optional BoxStyle baseSizeBox
      # Style of the border around the box representing the item's base size
      optional LineStyle baseSizeBorder
      # Style of the arrow representing if the item grew or shrank
      optional LineStyle flexibilityArrow

  # Style information for drawing a line.
  type LineStyle extends object
    properties
      # The color of the line (default: transparent)
      optional DOM.RGBA color
      # The line pattern (default: solid)
      optional enum pattern
        dashed
        dotted

  # Style information for drawing a box.
  type BoxStyle extends object
    properties
      # The background color for the box (default: transparent)
      optional DOM.RGBA fillColor
      # The hatching color for the box (default: transparent)
      optional DOM.RGBA hatchColor

  type ContrastAlgorithm extends string
    enum
      aa
      aaa
      apca

  # Configuration data for the highlighting of page elements.
  type HighlightConfig extends object
    properties
      # Whether the node info tooltip should be shown (default: false).
      optional boolean showInfo
      # Whether the node styles in the tooltip (default: false).
      optional boolean showStyles
      # Whether the rulers should be shown (default: false).
      optional boolean showRulers
      # Whether the a11y info should be shown (default: true).
      optional boolean showAccessibilityInfo
      # Whether the extension lines from node to the rulers should be shown (default: false).
      optional boolean showExtensionLines
      # The content box highlight fill color (default: transparent).
      optional DOM.RGBA contentColor
      # The padding highlight fill color (default: transparent).
      optional DOM.RGBA paddingColor
      # The border highlight fill color (default: transparent).
      optional DOM.RGBA borderColor
      # The margin highlight fill color (default: transparent).
      optional DOM.RGBA marginColor
      # The event target element highlight fill color (default: transparent).
      optional DOM.RGBA eventTargetColor
      # The shape outside fill color (default: transparent).
      optional DOM.RGBA shapeColor
      # The shape margin fill color (default: transparent).
      optional DOM.RGBA shapeMarginColor
      # The grid layout color (default: transparent).
      optional DOM.RGBA cssGridColor
      # The color format used to format color styles (default: hex).
      optional ColorFormat colorFormat
      # The grid layout highlight configuration (default: all transparent).
      optional GridHighlightConfig gridHighlightConfig
      # The flex container highlight configuration (default: all transparent).
      optional FlexContainerHighlightConfig flexContainerHighlightConfig
      # The flex item highlight configuration (default: all transparent).
      optional FlexItemHighlightConfig flexItemHighlightConfig
      # The contrast algorithm to use for the contrast ratio (default: aa).
      optional ContrastAlgorithm contrastAlgorithm
      # The container query container highlight configuration (default: all transparent).
      optional ContainerQueryContainerHighlightConfig containerQueryContainerHighlightConfig

  type ColorFormat extends string
    enum
      rgb
      hsl
      hwb
      hex

  # Configurations for Persistent Grid Highlight
  type GridNodeHighlightConfig extends object
    properties
      # A descriptor for the highlight appearance.
      GridHighlightConfig gridHighlightConfig
      # Identifier of the node to highlight.
      DOM.NodeId nodeId

  type FlexNodeHighlightConfig extends object
    properties
      # A descriptor for the highlight appearance of flex containers.
      FlexContainerHighlightConfig flexContainerHighlightConfig
      # Identifier of the node to highlight.
      DOM.NodeId nodeId

  type ScrollSnapContainerHighlightConfig extends object
    properties
      # The style of the snapport border (default: transparent)
      optional LineStyle snapportBorder
      # The style of the snap area border (default: transparent)
      optional LineStyle snapAreaBorder
      # The margin highlight fill color (default: transparent).
      optional DOM.RGBA scrollMarginColor
      # The padding highlight fill color (default: transparent).
      optional DOM.RGBA scrollPaddingColor

  type ScrollSnapHighlightConfig extends object
    properties
      # A descriptor for the highlight appearance of scroll snap containers.
      ScrollSnapContainerHighlightConfig scrollSnapContainerHighlightConfig
      # Identifier of the node to highlight.
      DOM.NodeId nodeId

  # Configuration for dual screen hinge
  type HingeConfig extends object
    properties
      # A rectangle represent hinge
      DOM.Rect rect
      # The content box highlight fill color (default: a dark color).
      optional DOM.RGBA contentColor
      # The content box highlight outline color (default: transparent).
      optional DOM.RGBA outlineColor

  # Configuration for Window Controls Overlay
  type WindowControlsOverlayConfig extends object
    properties
      # Whether the title bar CSS should be shown when emulating the Window Controls Overlay.
      boolean showCSS
      # Seleted platforms to show the overlay.
      string selectedPlatform
      # The theme color defined in app manifest.
      string themeColor

  type ContainerQueryHighlightConfig extends object
    properties
      # A descriptor for the highlight appearance of container query containers.
      ContainerQueryContainerHighlightConfig containerQueryContainerHighlightConfig
      # Identifier of the container node to highlight.
      DOM.NodeId nodeId

  type ContainerQueryContainerHighlightConfig extends object
    properties
      # The style of the container border.
      optional LineStyle containerBorder
      # The style of the descendants' borders.
      optional LineStyle descendantBorder

  type IsolatedElementHighlightConfig extends object
    properties
      # A descriptor for the highlight appearance of an element in isolation mode.
      IsolationModeHighlightConfig isolationModeHighlightConfig
      # Identifier of the isolated element to highlight.
      DOM.NodeId nodeId

  type IsolationModeHighlightConfig extends object
    properties
      # The fill color of the resizers (default: transparent).
      optional DOM.RGBA resizerColor
      # The fill color for resizer handles (default: transparent).
      optional DOM.RGBA resizerHandleColor
      # The fill color for the mask covering non-isolated elements (default: transparent).
      optional DOM.RGBA maskColor

  type InspectMode extends string
    enum
      searchForNode
      searchForUAShadowDOM
      captureAreaScreenshot
      showDistances
      none

  # Disables domain notifications.
  command disable

  # Enables domain notifications.
  command enable

  # For testing.
  command getHighlightObjectForTest
    parameters
      # Id of the node to get highlight object for.
      DOM.NodeId nodeId
      # Whether to include distance info.
      optional boolean includeDistance
      # Whether to include style info.
      optional boolean includeStyle
      # The color format to get config with (default: hex).
      optional ColorFormat colorFormat
      # Whether to show accessibility info (default: true).
      optional boolean showAccessibilityInfo
    returns
      # Highlight data for the node.
      object highlight

  # For Persistent Grid testing.
  command getGridHighlightObjectsForTest
    parameters
      # Ids of the node to get highlight object for.
      array of DOM.NodeId nodeIds
    returns
      # Grid Highlight data for the node ids provided.
      object highlights

  # For Source Order Viewer testing.
  command getSourceOrderHighlightObjectForTest
    parameters
      # Id of the node to highlight.
      DOM.NodeId nodeId
    returns
      # Source order highlight data for the node id provided.
      object highlight

  # Hides any highlight.
  command hideHighlight

  # Highlights owner element of the frame with given id.
  # Deprecated: Doesn't work reliablity and cannot be fixed due to process
  # separatation (the owner node might be in a different process). Determine
  # the owner node in the client and use highlightNode.
  deprecated command highlightFrame
    parameters
      # Identifier of the frame to highlight.
      Page.FrameId frameId
      # The content box highlight fill color (default: transparent).
      optional DOM.RGBA contentColor
      # The content box highlight outline color (default: transparent).
      optional DOM.RGBA contentOutlineColor

  # Highlights DOM node with given id or with the given JavaScript object wrapper. Either nodeId or
  # objectId must be specified.
  command highlightNode
    parameters
      # A descriptor for the highlight appearance.
      HighlightConfig highlightConfig
      # Identifier of the node to highlight.
      optional DOM.NodeId nodeId
      # Identifier of the backend node to highlight.
      optional DOM.BackendNodeId backendNodeId
      # JavaScript object id of the node to be highlighted.
      optional Runtime.RemoteObjectId objectId
      # Selectors to highlight relevant nodes.
      optional string selector

  # Highlights given quad. Coordinates are absolute with respect to the main frame viewport.
  command highlightQuad
    parameters
      # Quad to highlight
      DOM.Quad quad
      # The highlight fill color (default: transparent).
      optional DOM.RGBA color
      # The highlight outline color (default: transparent).
      optional DOM.RGBA outlineColor

  # Highlights given rectangle. Coordinates are absolute with respect to the main frame viewport.
  command highlightRect
    parameters
      # X coordinate
      integer x
      # Y coordinate
      integer y
      # Rectangle width
      integer width
      # Rectangle height
      integer height
      # The highlight fill color (default: transparent).
      optional DOM.RGBA color
      # The highlight outline color (default: transparent).
      optional DOM.RGBA outlineColor

  # Highlights the source order of the children of the DOM node with given id or with the given
  # JavaScript object wrapper. Either nodeId or objectId must be specified.
  command highlightSourceOrder
    parameters
      # A descriptor for the appearance of the overlay drawing.
      SourceOrderConfig sourceOrderConfig
      # Identifier of the node to highlight.
      optional DOM.NodeId nodeId
      # Identifier of the backend node to highlight.
      optional DOM.BackendNodeId backendNodeId
      # JavaScript object id of the node to be highlighted.
      optional Runtime.RemoteObjectId objectId

  # Enters the 'inspect' mode. In this mode, elements that user is hovering over are highlighted.
  # Backend then generates 'inspectNodeRequested' event upon element selection.
  command setInspectMode
    parameters
      # Set an inspection mode.
      InspectMode mode
      # A descriptor for the highlight appearance of hovered-over nodes. May be omitted if `enabled
      # == false`.
      optional HighlightConfig highlightConfig

  # Highlights owner element of all frames detected to be ads.
  command setShowAdHighlights
    parameters
      # True for showing ad highlights
      boolean show

  command setPausedInDebuggerMessage
    parameters
      # The message to display, also triggers resume and step over controls.
      optional string message

  # Requests that backend shows debug borders on layers
  command setShowDebugBorders
    parameters
      # True for showing debug borders
      boolean show

  # Requests that backend shows the FPS counter
  command setShowFPSCounter
    parameters
      # True for showing the FPS counter
      boolean show

  # Highlight multiple elements with the CSS Grid overlay.
  command setShowGridOverlays
    parameters
      # An array of node identifiers and descriptors for the highlight appearance.
      array of GridNodeHighlightConfig gridNodeHighlightConfigs

  command setShowFlexOverlays
    parameters
      # An array of node identifiers and descriptors for the highlight appearance.
      array of FlexNodeHighlightConfig flexNodeHighlightConfigs

  command setShowScrollSnapOverlays
    parameters
      # An array of node identifiers and descriptors for the highlight appearance.
      array of ScrollSnapHighlightConfig scrollSnapHighlightConfigs

  command setShowContainerQueryOverlays
    parameters
      # An array of node identifiers and descriptors for the highlight appearance.
      array of ContainerQueryHighlightConfig containerQueryHighlightConfigs

  # Requests that backend shows paint rectangles
  command setShowPaintRects
    parameters
      # True for showing paint rectangles
      boolean result

  # Requests that backend shows layout shift regions
  command setShowLayoutShiftRegions
    parameters
      # True for showing layout shift regions
      boolean result

  # Requests that backend shows scroll bottleneck rects
  command setShowScrollBottleneckRects
    parameters
      # True for showing scroll bottleneck rects
      boolean show

  # Deprecated, no longer has any effect.
  deprecated command setShowHitTestBorders
    parameters
      # True for showing hit-test borders
      boolean show

  # Request that backend shows an overlay with web vital metrics.
  command setShowWebVitals
    parameters
      boolean show

  # Paints viewport size upon main frame resize.
  command setShowViewportSizeOnResize
    parameters
      # Whether to paint size or not.
      boolean show

  # Add a dual screen device hinge
  command setShowHinge
    parameters
      # hinge data, null means hideHinge
      optional HingeConfig hingeConfig

  # Show elements in isolation mode with overlays.
  command setShowIsolatedElements
    parameters
      # An array of node identifiers and descriptors for the highlight appearance.
      array of IsolatedElementHighlightConfig isolatedElementHighlightConfigs

  # Show Window Controls Overlay for PWA
  command setShowWindowControlsOverlay
    parameters
      # Window Controls Overlay data, null means hide Window Controls Overlay
      optional WindowControlsOverlayConfig windowControlsOverlayConfig

  # Fired when the node should be inspected. This happens after call to `setInspectMode` or when
  # user manually inspects an element.
  event inspectNodeRequested
    parameters
      # Id of the node to inspect.
      DOM.BackendNodeId backendNodeId

  # Fired when the node should be highlighted. This happens after call to `setInspectMode`.
  event nodeHighlightRequested
    parameters
      DOM.NodeId nodeId

  # Fired when user asks to capture screenshot of some area on the page.
  event screenshotRequested
    parameters
      # Viewport to capture, in device independent pixels (dip).
      Page.Viewport viewport

  # Fired when user cancels the inspect mode.
  event inspectModeCanceled

# Actions and events related to the inspected page belong to the page domain.
domain Page
  depends on Debugger
  depends on DOM
  depends on IO
  depends on Network
  depends on Runtime

  # Unique frame identifier.
  type FrameId extends string

  # Indicates whether a frame has been identified as an ad.
  experimental type AdFrameType extends string
    enum
      none
      # This frame is a subframe of an ad frame.
      child
      # This frame is the root of an ad frame.
      root

  experimental type AdFrameExplanation extends string
    enum
      ParentIsAd
      CreatedByAdScript
      MatchedBlockingRule

  # Indicates whether a frame has been identified as an ad and why.
  experimental type AdFrameStatus extends object
    properties
      AdFrameType adFrameType
      optional array of AdFrameExplanation explanations

  # Identifies the bottom-most script which caused the frame to be labelled
  # as an ad.
  experimental type AdScriptId extends object
    properties
      # Script Id of the bottom-most script which caused the frame to be labelled
      # as an ad.
      Runtime.ScriptId scriptId
      # Id of adScriptId's debugger.
      Runtime.UniqueDebuggerId debuggerId

  # Indicates whether the frame is a secure context and why it is the case.
  experimental type SecureContextType extends string
    enum
      # The origin is a secure context.
      Secure
      # The host is localhost and hence is considered secure.
      SecureLocalhost
      # The origin has an insecure scheme and is not localhost.
      InsecureScheme
      # One of the ancestor frames is not a secure context.
      InsecureAncestor

  # Indicates whether the frame is cross-origin isolated and why it is the case.
  experimental type CrossOriginIsolatedContextType extends string
    enum
      # The origin is cross-origin isolated.
      Isolated
      # The origin is not cross-origin isolated.
      NotIsolated
      # The cross-origin isolation feature is disabled.
      NotIsolatedFeatureDisabled

  experimental type GatedAPIFeatures extends string
    enum
      SharedArrayBuffers
      SharedArrayBuffersTransferAllowed
      PerformanceMeasureMemory
      PerformanceProfile

  # All Permissions Policy features. This enum should match the one defined
  # in third_party/blink/renderer/core/permissions_policy/permissions_policy_features.json5.
  experimental type PermissionsPolicyFeature extends string
    enum
      accelerometer
      ambient-light-sensor
      attribution-reporting
      autoplay
      bluetooth
      browsing-topics
      camera
      ch-dpr
      ch-device-memory
      ch-downlink
      ch-ect
      ch-prefers-color-scheme
      ch-prefers-reduced-motion
      ch-prefers-reduced-transparency
      ch-rtt
      ch-save-data
      ch-ua
      ch-ua-arch
      ch-ua-bitness
      ch-ua-platform
      ch-ua-model
      ch-ua-mobile
      ch-ua-form-factor
      ch-ua-full-version
      ch-ua-full-version-list
      ch-ua-platform-version
      ch-ua-wow64
      ch-viewport-height
      ch-viewport-width
      ch-width
      clipboard-read
      clipboard-write
      compute-pressure
      cross-origin-isolated
      direct-sockets
      display-capture
      document-domain
      encrypted-media
      execution-while-out-of-viewport
      execution-while-not-rendered
      focus-without-user-activation
      fullscreen
      frobulate
      gamepad
      geolocation
      gyroscope
      hid
      identity-credentials-get
      idle-detection
      interest-cohort
      join-ad-interest-group
      keyboard-map
      local-fonts
      magnetometer
      microphone
      midi
      otp-credentials
      payment
      picture-in-picture
      private-aggregation
      private-state-token-issuance
      private-state-token-redemption
      publickey-credentials-get
      run-ad-auction
      screen-wake-lock
      serial
      shared-autofill
      shared-storage
      shared-storage-select-url
      smart-card
      storage-access
      sync-xhr
      unload
      usb
      usb-unrestricted
      vertical-scroll
      web-printing
      web-share
      # Alias for 'window-placement' (crbug.com/1328581).
      window-management
      window-placement
      xr-spatial-tracking

  # Reason for a permissions policy feature to be disabled.
  experimental type PermissionsPolicyBlockReason extends string
    enum
      # Declaration in HTTP header.
      Header
      # Declaration in iframe attribute.
      IframeAttribute
      # Inside fenced frame.
      InFencedFrameTree
      # Inside an Isolated Application.
      InIsolatedApp

  experimental type PermissionsPolicyBlockLocator extends object
    properties
      FrameId frameId
      PermissionsPolicyBlockReason blockReason

  experimental type PermissionsPolicyFeatureState extends object
    properties
      PermissionsPolicyFeature feature
      boolean allowed
      optional PermissionsPolicyBlockLocator locator

  # Origin Trial(https://www.chromium.org/blink/origin-trials) support.
  # Status for an Origin Trial token.
  experimental type OriginTrialTokenStatus extends string
    enum
      Success
      NotSupported
      Insecure
      Expired
      WrongOrigin
      InvalidSignature
      Malformed
      WrongVersion
      FeatureDisabled
      TokenDisabled
      FeatureDisabledForUser
      UnknownTrial

  # Status for an Origin Trial.
  experimental type OriginTrialStatus extends string
    enum
      Enabled
      ValidTokenNotProvided
      OSNotSupported
      TrialNotAllowed

  experimental type OriginTrialUsageRestriction extends string
    enum
      None
      Subset

  experimental type OriginTrialToken extends object
    properties
      string origin
      boolean matchSubDomains
      string trialName
      Network.TimeSinceEpoch expiryTime
      boolean isThirdParty
      OriginTrialUsageRestriction usageRestriction

  experimental type OriginTrialTokenWithStatus extends object
    properties
      string rawTokenText
      # `parsedToken` is present only when the token is extractable and
      # parsable.
      optional OriginTrialToken parsedToken
      OriginTrialTokenStatus status

  experimental type OriginTrial extends object
    properties
      string trialName
      OriginTrialStatus status
      array of OriginTrialTokenWithStatus tokensWithStatus

  # Information about the Frame on the page.
  type Frame extends object
    properties
      # Frame unique identifier.
      FrameId id
      # Parent frame identifier.
      optional FrameId parentId
      # Identifier of the loader associated with this frame.
      Network.LoaderId loaderId
      # Frame's name as specified in the tag.
      optional string name
      # Frame document's URL without fragment.
      string url
      # Frame document's URL fragment including the '#'.
      experimental optional string urlFragment
      # Frame document's registered domain, taking the public suffixes list into account.
      # Extracted from the Frame's url.
      # Example URLs: http://www.google.com/file.html -> "google.com"
      #               http://a.b.co.uk/file.html      -> "b.co.uk"
      experimental string domainAndRegistry
      # Frame document's security origin.
      string securityOrigin
      # Frame document's mimeType as determined by the browser.
      string mimeType
      # If the frame failed to load, this contains the URL that could not be loaded. Note that unlike url above, this URL may contain a fragment.
      experimental optional string unreachableUrl
      # Indicates whether this frame was tagged as an ad and why.
      experimental optional AdFrameStatus adFrameStatus
      # Indicates whether the main document is a secure context and explains why that is the case.
      experimental SecureContextType secureContextType
      # Indicates whether this is a cross origin isolated context.
      experimental CrossOriginIsolatedContextType crossOriginIsolatedContextType
      # Indicated which gated APIs / features are available.
      experimental array of GatedAPIFeatures gatedAPIFeatures

  # Information about the Resource on the page.
  experimental type FrameResource extends object
    properties
      # Resource URL.
      string url
      # Type of this resource.
      Network.ResourceType type
      # Resource mimeType as determined by the browser.
      string mimeType
      # last-modified timestamp as reported by server.
      optional Network.TimeSinceEpoch lastModified
      # Resource content size.
      optional number contentSize
      # True if the resource failed to load.
      optional boolean failed
      # True if the resource was canceled during loading.
      optional boolean canceled

  # Information about the Frame hierarchy along with their cached resources.
  experimental type FrameResourceTree extends object
    properties
      # Frame information for this tree item.
      Frame frame
      # Child frames.
      optional array of FrameResourceTree childFrames
      # Information about frame resources.
      array of FrameResource resources

  # Information about the Frame hierarchy.
  type FrameTree extends object
    properties
      # Frame information for this tree item.
      Frame frame
      # Child frames.
      optional array of FrameTree childFrames

  # Unique script identifier.
  type ScriptIdentifier extends string

  # Transition type.
  type TransitionType extends string
    enum
      link
      typed
      address_bar
      auto_bookmark
      auto_subframe
      manual_subframe
      generated
      auto_toplevel
      form_submit
      reload
      keyword
      keyword_generated
      other

  # Navigation history entry.
  type NavigationEntry extends object
    properties
      # Unique id of the navigation history entry.
      integer id
      # URL of the navigation history entry.
      string url
      # URL that the user typed in the url bar.
      string userTypedURL
      # Title of the navigation history entry.
      string title
      # Transition type.
      TransitionType transitionType

  # Screencast frame metadata.
  experimental type ScreencastFrameMetadata extends object
    properties
      # Top offset in DIP.
      number offsetTop
      # Page scale factor.
      number pageScaleFactor
      # Device screen width in DIP.
      number deviceWidth
      # Device screen height in DIP.
      number deviceHeight
      # Position of horizontal scroll in CSS pixels.
      number scrollOffsetX
      # Position of vertical scroll in CSS pixels.
      number scrollOffsetY
      # Frame swap timestamp.
      optional Network.TimeSinceEpoch timestamp

  # Javascript dialog type.
  type DialogType extends string
    enum
      alert
      confirm
      prompt
      beforeunload

  # Error while paring app manifest.
  type AppManifestError extends object
    properties
      # Error message.
      string message
      # If criticial, this is a non-recoverable parse error.
      integer critical
      # Error line.
      integer line
      # Error column.
      integer column

  # Parsed app manifest properties.
  experimental type AppManifestParsedProperties extends object
    properties
      # Computed scope value
      string scope

  # Layout viewport position and dimensions.
  type LayoutViewport extends object
    properties
      # Horizontal offset relative to the document (CSS pixels).
      integer pageX
      # Vertical offset relative to the document (CSS pixels).
      integer pageY
      # Width (CSS pixels), excludes scrollbar if present.
      integer clientWidth
      # Height (CSS pixels), excludes scrollbar if present.
      integer clientHeight

  # Visual viewport position, dimensions, and scale.
  type VisualViewport extends object
    properties
      # Horizontal offset relative to the layout viewport (CSS pixels).
      number offsetX
      # Vertical offset relative to the layout viewport (CSS pixels).
      number offsetY
      # Horizontal offset relative to the document (CSS pixels).
      number pageX
      # Vertical offset relative to the document (CSS pixels).
      number pageY
      # Width (CSS pixels), excludes scrollbar if present.
      number clientWidth
      # Height (CSS pixels), excludes scrollbar if present.
      number clientHeight
      # Scale relative to the ideal viewport (size at width=device-width).
      number scale
      # Page zoom factor (CSS to device independent pixels ratio).
      optional number zoom

  # Viewport for capturing screenshot.
  type Viewport extends object
    properties
      # X offset in device independent pixels (dip).
      number x
      # Y offset in device independent pixels (dip).
      number y
      # Rectangle width in device independent pixels (dip).
      number width
      # Rectangle height in device independent pixels (dip).
      number height
      # Page scale factor.
      number scale

  # Generic font families collection.
  experimental type FontFamilies extends object
    properties
      # The standard font-family.
      optional string standard
      # The fixed font-family.
      optional string fixed
      # The serif font-family.
      optional string serif
      # The sansSerif font-family.
      optional string sansSerif
      # The cursive font-family.
      optional string cursive
      # The fantasy font-family.
      optional string fantasy
      # The math font-family.
      optional string math

  # Font families collection for a script.
  experimental type ScriptFontFamilies extends object
    properties
      # Name of the script which these font families are defined for.
      string script
      # Generic font families collection for the script.
      FontFamilies fontFamilies

  # Default font sizes.
  experimental type FontSizes extends object
    properties
      # Default standard font size.
      optional integer standard
      # Default fixed font size.
      optional integer fixed

  experimental type ClientNavigationReason extends string
    enum
      formSubmissionGet
      formSubmissionPost
      httpHeaderRefresh
      scriptInitiated
      metaTagRefresh
      pageBlockInterstitial
      reload
      anchorClick

  experimental type ClientNavigationDisposition extends string
    enum
      currentTab
      newTab
      newWindow
      download

  experimental type InstallabilityErrorArgument extends object
    properties
      # Argument name (e.g. name:'minimum-icon-size-in-pixels').
      string name
      # Argument value (e.g. value:'64').
      string value

  # The installability error
  experimental type InstallabilityError extends object
    properties
      # The error id (e.g. 'manifest-missing-suitable-icon').
      string errorId
      # The list of error arguments (e.g. {name:'minimum-icon-size-in-pixels', value:'64'}).
      array of InstallabilityErrorArgument errorArguments

  # The referring-policy used for the navigation.
  experimental type ReferrerPolicy extends string
    enum
      noReferrer
      noReferrerWhenDowngrade
      origin
      originWhenCrossOrigin
      sameOrigin
      strictOrigin
      strictOriginWhenCrossOrigin
      unsafeUrl

  # Per-script compilation cache parameters for `Page.produceCompilationCache`
  experimental type CompilationCacheParams extends object
    properties
      # The URL of the script to produce a compilation cache entry for.
      string url
      # A hint to the backend whether eager compilation is recommended.
      # (the actual compilation mode used is upon backend discretion).
      optional boolean eager

  # Deprecated, please use addScriptToEvaluateOnNewDocument instead.
  experimental deprecated command addScriptToEvaluateOnLoad
    parameters
      string scriptSource
    returns
      # Identifier of the added script.
      ScriptIdentifier identifier

  # Evaluates given script in every frame upon creation (before loading frame's scripts).
  command addScriptToEvaluateOnNewDocument
    parameters
      string source
      # If specified, creates an isolated world with the given name and evaluates given script in it.
      # This world name will be used as the ExecutionContextDescription::name when the corresponding
      # event is emitted.
      experimental optional string worldName
      # Specifies whether command line API should be available to the script, defaults
      # to false.
      experimental optional boolean includeCommandLineAPI
      # If true, runs the script immediately on existing execution contexts or worlds.
      # Default: false.
      experimental optional boolean runImmediately
    returns
      # Identifier of the added script.
      ScriptIdentifier identifier

  # Brings page to front (activates tab).
  command bringToFront

  # Capture page screenshot.
  command captureScreenshot
    parameters
      # Image compression format (defaults to png).
      optional enum format
        jpeg
        png
        webp
      # Compression quality from range [0..100] (jpeg only).
      optional integer quality
      # Capture the screenshot of a given region only.
      optional Viewport clip
      # Capture the screenshot from the surface, rather than the view. Defaults to true.
      experimental optional boolean fromSurface
      # Capture the screenshot beyond the viewport. Defaults to false.
      experimental optional boolean captureBeyondViewport
      # Optimize image encoding for speed, not for resulting size (defaults to false)
      experimental optional boolean optimizeForSpeed
    returns
      # Base64-encoded image data.
      binary data

  # Returns a snapshot of the page as a string. For MHTML format, the serialization includes
  # iframes, shadow DOM, external resources, and element-inline styles.
  experimental command captureSnapshot
    parameters
      # Format (defaults to mhtml).
      optional enum format
        mhtml
    returns
      # Serialized page data.
      string data

  # Clears the overridden device metrics.
  experimental deprecated command clearDeviceMetricsOverride
    # Use 'Emulation.clearDeviceMetricsOverride' instead
    redirect Emulation

  # Clears the overridden Device Orientation.
  experimental deprecated command clearDeviceOrientationOverride
    # Use 'DeviceOrientation.clearDeviceOrientationOverride' instead
    redirect DeviceOrientation

  # Clears the overridden Geolocation Position and Error.
  deprecated command clearGeolocationOverride
    # Use 'Emulation.clearGeolocationOverride' instead
    redirect Emulation

  # Creates an isolated world for the given frame.
  command createIsolatedWorld
    parameters
      # Id of the frame in which the isolated world should be created.
      FrameId frameId
      # An optional name which is reported in the Execution Context.
      optional string worldName
      # Whether or not universal access should be granted to the isolated world. This is a powerful
      # option, use with caution.
      optional boolean grantUniveralAccess
    returns
      # Execution context of the isolated world.
      Runtime.ExecutionContextId executionContextId

  # Deletes browser cookie with given name, domain and path.
  experimental deprecated command deleteCookie
    # Use 'Network.deleteCookie' instead
    redirect Network
    parameters
      # Name of the cookie to remove.
      string cookieName
      # URL to match cooke domain and path.
      string url

  # Disables page domain notifications.
  command disable

  # Enables page domain notifications.
  command enable

  command getAppManifest
    returns
      # Manifest location.
      string url
      array of AppManifestError errors
      # Manifest content.
      optional string data
      # Parsed manifest properties
      experimental optional AppManifestParsedProperties parsed

  experimental command getInstallabilityErrors
    returns
      array of InstallabilityError installabilityErrors

  # Deprecated because it's not guaranteed that the returned icon is in fact the one used for PWA installation.
  experimental deprecated command getManifestIcons
    returns
      optional binary primaryIcon

  # Returns the unique (PWA) app id.
  # Only returns values if the feature flag 'WebAppEnableManifestId' is enabled
  experimental command getAppId
    returns
      # App id, either from manifest's id attribute or computed from start_url
      optional string appId
      # Recommendation for manifest's id attribute to match current id computed from start_url
      optional string recommendedId

  experimental command getAdScriptId
    parameters
      FrameId frameId
    returns
      # Identifies the bottom-most script which caused the frame to be labelled
      # as an ad. Only sent if frame is labelled as an ad and id is available.
      optional AdScriptId adScriptId

  # Returns present frame tree structure.
  command getFrameTree
    returns
      # Present frame tree structure.
      FrameTree frameTree

  # Returns metrics relating to the layouting of the page, such as viewport bounds/scale.
  command getLayoutMetrics
    returns
      # Deprecated metrics relating to the layout viewport. Is in device pixels. Use `cssLayoutViewport` instead.
      deprecated LayoutViewport layoutViewport
      # Deprecated metrics relating to the visual viewport. Is in device pixels. Use `cssVisualViewport` instead.
      deprecated VisualViewport visualViewport
      # Deprecated size of scrollable area. Is in DP. Use `cssContentSize` instead.
      deprecated DOM.Rect contentSize
      # Metrics relating to the layout viewport in CSS pixels.
      LayoutViewport cssLayoutViewport
      # Metrics relating to the visual viewport in CSS pixels.
      VisualViewport cssVisualViewport
      # Size of scrollable area in CSS pixels.
      DOM.Rect cssContentSize

  # Returns navigation history for the current page.
  command getNavigationHistory
    returns
      # Index of the current navigation history entry.
      integer currentIndex
      # Array of navigation history entries.
      array of NavigationEntry entries

  # Resets navigation history for the current page.
  command resetNavigationHistory

  # Returns content of the given resource.
  experimental command getResourceContent
    parameters
      # Frame id to get resource for.
      FrameId frameId
      # URL of the resource to get content for.
      string url
    returns
      # Resource content.
      string content
      # True, if content was served as base64.
      boolean base64Encoded

  # Returns present frame / resource tree structure.
  experimental command getResourceTree
    returns
      # Present frame / resource tree structure.
      FrameResourceTree frameTree

  # Accepts or dismisses a JavaScript initiated dialog (alert, confirm, prompt, or onbeforeunload).
  command handleJavaScriptDialog
    parameters
      # Whether to accept or dismiss the dialog.
      boolean accept
      # The text to enter into the dialog prompt before accepting. Used only if this is a prompt
      # dialog.
      optional string promptText

  # Navigates current page to the given URL.
  command navigate
    parameters
      # URL to navigate the page to.
      string url
      # Referrer URL.
      optional string referrer
      # Intended transition type.
      optional TransitionType transitionType
      # Frame id to navigate, if not specified navigates the top frame.
      optional FrameId frameId
      # Referrer-policy used for the navigation.
      experimental optional ReferrerPolicy referrerPolicy
    returns
      # Frame id that has navigated (or failed to navigate)
      FrameId frameId
      # Loader identifier. This is omitted in case of same-document navigation,
      # as the previously committed loaderId would not change.
      optional Network.LoaderId loaderId
      # User friendly error message, present if and only if navigation has failed.
      optional string errorText

  # Navigates current page to the given history entry.
  command navigateToHistoryEntry
    parameters
      # Unique id of the entry to navigate to.
      integer entryId

  # Print page as PDF.
  command printToPDF
    parameters
      # Paper orientation. Defaults to false.
      optional boolean landscape
      # Display header and footer. Defaults to false.
      optional boolean displayHeaderFooter
      # Print background graphics. Defaults to false.
      optional boolean printBackground
      # Scale of the webpage rendering. Defaults to 1.
      optional number scale
      # Paper width in inches. Defaults to 8.5 inches.
      optional number paperWidth
      # Paper height in inches. Defaults to 11 inches.
      optional number paperHeight
      # Top margin in inches. Defaults to 1cm (~0.4 inches).
      optional number marginTop
      # Bottom margin in inches. Defaults to 1cm (~0.4 inches).
      optional number marginBottom
      # Left margin in inches. Defaults to 1cm (~0.4 inches).
      optional number marginLeft
      # Right margin in inches. Defaults to 1cm (~0.4 inches).
      optional number marginRight
      # Paper ranges to print, one based, e.g., '1-5, 8, 11-13'. Pages are
      # printed in the document order, not in the order specified, and no
      # more than once.
      # Defaults to empty string, which implies the entire document is printed.
      # The page numbers are quietly capped to actual page count of the
      # document, and ranges beyond the end of the document are ignored.
      # If this results in no pages to print, an error is reported.
      # It is an error to specify a range with start greater than end.
      optional string pageRanges
      # HTML template for the print header. Should be valid HTML markup with following
      # classes used to inject printing values into them:
      # - `date`: formatted print date
      # - `title`: document title
      # - `url`: document location
      # - `pageNumber`: current page number
      # - `totalPages`: total pages in the document
      #
      # For example, `<span class=title></span>` would generate span containing the title.
      optional string headerTemplate
      # HTML template for the print footer. Should use the same format as the `headerTemplate`.
      optional string footerTemplate
      # Whether or not to prefer page size as defined by css. Defaults to false,
      # in which case the content will be scaled to fit the paper size.
      optional boolean preferCSSPageSize
      # return as stream
      experimental optional enum transferMode
        ReturnAsBase64
        ReturnAsStream
      # Whether or not to generate tagged (accessible) PDF. Defaults to embedder choice.
      experimental optional boolean generateTaggedPDF
      # Whether or not to embed the document outline into the PDF.
      experimental optional boolean generateDocumentOutline
    returns
      # Base64-encoded pdf data. Empty if |returnAsStream| is specified.
      binary data
      # A handle of the stream that holds resulting PDF data.
      experimental optional IO.StreamHandle stream

  # Reloads given page optionally ignoring the cache.
  command reload
    parameters
      # If true, browser cache is ignored (as if the user pressed Shift+refresh).
      optional boolean ignoreCache
      # If set, the script will be injected into all frames of the inspected page after reload.
      # Argument will be ignored if reloading dataURL origin.
      optional string scriptToEvaluateOnLoad

  # Deprecated, please use removeScriptToEvaluateOnNewDocument instead.
  experimental deprecated command removeScriptToEvaluateOnLoad
    parameters
      ScriptIdentifier identifier

  # Removes given script from the list.
  command removeScriptToEvaluateOnNewDocument
    parameters
      ScriptIdentifier identifier

  # Acknowledges that a screencast frame has been received by the frontend.
  experimental command screencastFrameAck
    parameters
      # Frame number.
      integer sessionId

  # Searches for given string in resource content.
  experimental command searchInResource
    parameters
      # Frame id for resource to search in.
      FrameId frameId
      # URL of the resource to search in.
      string url
      # String to search for.
      string query
      # If true, search is case sensitive.
      optional boolean caseSensitive
      # If true, treats string parameter as regex.
      optional boolean isRegex
    returns
      # List of search matches.
      array of Debugger.SearchMatch result

  # Enable Chrome's experimental ad filter on all sites.
  experimental command setAdBlockingEnabled
    parameters
      # Whether to block ads.
      boolean enabled

  # Enable page Content Security Policy by-passing.
  experimental command setBypassCSP
    parameters
      # Whether to bypass page CSP.
      boolean enabled

  # Get Permissions Policy state on given frame.
  experimental command getPermissionsPolicyState
    parameters
      FrameId frameId
    returns
      array of PermissionsPolicyFeatureState states

  # Get Origin Trials on given frame.
  experimental command getOriginTrials
    parameters
      FrameId frameId
    returns
      array of OriginTrial originTrials

  # Overrides the values of device screen dimensions (window.screen.width, window.screen.height,
  # window.innerWidth, window.innerHeight, and "device-width"/"device-height"-related CSS media
  # query results).
  experimental deprecated command setDeviceMetricsOverride
    # Use 'Emulation.setDeviceMetricsOverride' instead
    redirect Emulation
    parameters
      # Overriding width value in pixels (minimum 0, maximum 10000000). 0 disables the override.
      integer width
      # Overriding height value in pixels (minimum 0, maximum 10000000). 0 disables the override.
      integer height
      # Overriding device scale factor value. 0 disables the override.
      number deviceScaleFactor
      # Whether to emulate mobile device. This includes viewport meta tag, overlay scrollbars, text
      # autosizing and more.
      boolean mobile
      # Scale to apply to resulting view image.
      optional number scale
      # Overriding screen width value in pixels (minimum 0, maximum 10000000).
      optional integer screenWidth
      # Overriding screen height value in pixels (minimum 0, maximum 10000000).
      optional integer screenHeight
      # Overriding view X position on screen in pixels (minimum 0, maximum 10000000).
      optional integer positionX
      # Overriding view Y position on screen in pixels (minimum 0, maximum 10000000).
      optional integer positionY
      # Do not set visible view size, rely upon explicit setVisibleSize call.
      optional boolean dontSetVisibleSize
      # Screen orientation override.
      optional Emulation.ScreenOrientation screenOrientation
      # The viewport dimensions and scale. If not set, the override is cleared.
      optional Viewport viewport

  # Overrides the Device Orientation.
  experimental deprecated command setDeviceOrientationOverride
    # Use 'DeviceOrientation.setDeviceOrientationOverride' instead
    redirect DeviceOrientation
    parameters
      # Mock alpha
      number alpha
      # Mock beta
      number beta
      # Mock gamma
      number gamma

  # Set generic font families.
  experimental command setFontFamilies
    parameters
      # Specifies font families to set. If a font family is not specified, it won't be changed.
      FontFamilies fontFamilies
      # Specifies font families to set for individual scripts.
      optional array of ScriptFontFamilies forScripts

  # Set default font sizes.
  experimental command setFontSizes
    parameters
      # Specifies font sizes to set. If a font size is not specified, it won't be changed.
      FontSizes fontSizes

  # Sets given markup as the document's HTML.
  command setDocumentContent
    parameters
      # Frame id to set HTML for.
      FrameId frameId
      # HTML content to set.
      string html

  # Set the behavior when downloading a file.
  experimental deprecated command setDownloadBehavior
    parameters
      # Whether to allow all or deny all download requests, or use default Chrome behavior if
      # available (otherwise deny).
      enum behavior
        deny
        allow
        default
      # The default path to save downloaded files to. This is required if behavior is set to 'allow'
      optional string downloadPath

  # Overrides the Geolocation Position or Error. Omitting any of the parameters emulates position
  # unavailable.
  deprecated command setGeolocationOverride
    # Use 'Emulation.setGeolocationOverride' instead
    redirect Emulation
    parameters
      # Mock latitude
      optional number latitude
      # Mock longitude
      optional number longitude
      # Mock accuracy
      optional number accuracy

  # Controls whether page will emit lifecycle events.
  experimental command setLifecycleEventsEnabled
    parameters
      # If true, starts emitting lifecycle events.
      boolean enabled

  # Toggles mouse event-based touch event emulation.
  experimental deprecated command setTouchEmulationEnabled
    # Use 'Emulation.setTouchEmulationEnabled' instead
    redirect Emulation
    parameters
      # Whether the touch event emulation should be enabled.
      boolean enabled
      # Touch/gesture events configuration. Default: current platform.
      optional enum configuration
        mobile
        desktop

  # Starts sending each frame using the `screencastFrame` event.
  experimental command startScreencast
    parameters
      # Image compression format.
      optional enum format
        jpeg
        png
      # Compression quality from range [0..100].
      optional integer quality
      # Maximum screenshot width.
      optional integer maxWidth
      # Maximum screenshot height.
      optional integer maxHeight
      # Send every n-th frame.
      optional integer everyNthFrame

  # Force the page stop all navigations and pending resource fetches.
  command stopLoading

  # Crashes renderer on the IO thread, generates minidumps.
  experimental command crash

  # Tries to close page, running its beforeunload hooks, if any.
  experimental command close

  # Tries to update the web lifecycle state of the page.
  # It will transition the page to the given state according to:
  # https://github.com/WICG/web-lifecycle/
  experimental command setWebLifecycleState
    parameters
      # Target lifecycle state
      enum state
        frozen
        active

  # Stops sending each frame in the `screencastFrame`.
  experimental command stopScreencast

  # Requests backend to produce compilation cache for the specified scripts.
  # `scripts` are appeneded to the list of scripts for which the cache
  # would be produced. The list may be reset during page navigation.
  # When script with a matching URL is encountered, the cache is optionally
  # produced upon backend discretion, based on internal heuristics.
  # See also: `Page.compilationCacheProduced`.
  experimental command produceCompilationCache
    parameters
      array of CompilationCacheParams scripts

  # Seeds compilation cache for given url. Compilation cache does not survive
  # cross-process navigation.
  experimental command addCompilationCache
    parameters
      string url
      # Base64-encoded data
      binary data

  # Clears seeded compilation cache.
  experimental command clearCompilationCache

  # Enum of possible auto-reponse for permisison / prompt dialogs.
  experimental type AutoResponseMode extends string
    enum
      none
      autoAccept
      autoReject
      autoOptOut

# Sets the Secure Payment Confirmation transaction mode.
  # https://w3c.github.io/secure-payment-confirmation/#sctn-automation-set-spc-transaction-mode
  experimental command setSPCTransactionMode
    parameters
      AutoResponseMode mode

  # Extensions for Custom Handlers API:
  # https://html.spec.whatwg.org/multipage/system-state.html#rph-automation
  experimental command setRPHRegistrationMode
    parameters
      AutoResponseMode mode

  # Generates a report for testing.
  experimental command generateTestReport
    parameters
      # Message to be displayed in the report.
      string message
      # Specifies the endpoint group to deliver the report to.
      optional string group

  # Pauses page execution. Can be resumed using generic Runtime.runIfWaitingForDebugger.
  experimental command waitForDebugger

  # Intercept file chooser requests and transfer control to protocol clients.
  # When file chooser interception is enabled, native file chooser dialog is not shown.
  # Instead, a protocol event `Page.fileChooserOpened` is emitted.
  experimental command setInterceptFileChooserDialog
    parameters
      boolean enabled

  event domContentEventFired
    parameters
      Network.MonotonicTime timestamp

  # Emitted only when `page.interceptFileChooser` is enabled.
  event fileChooserOpened
    parameters
      # Id of the frame containing input node.
      experimental FrameId frameId
      # Input mode.
      enum mode
        selectSingle
        selectMultiple
      # Input node id. Only present for file choosers opened via an `<input type="file">` element.
      experimental optional DOM.BackendNodeId backendNodeId

  # Fired when frame has been attached to its parent.
  event frameAttached
    parameters
      # Id of the frame that has been attached.
      FrameId frameId
      # Parent frame identifier.
      FrameId parentFrameId
      # JavaScript stack trace of when frame was attached, only set if frame initiated from script.
      optional Runtime.StackTrace stack

  # Fired when frame no longer has a scheduled navigation.
  deprecated event frameClearedScheduledNavigation
    parameters
      # Id of the frame that has cleared its scheduled navigation.
      FrameId frameId

  # Fired when frame has been detached from its parent.
  event frameDetached
    parameters
      # Id of the frame that has been detached.
      FrameId frameId
      experimental enum reason
        # The frame is removed from the DOM.
        remove
        # The frame is being swapped out in favor of an out-of-process iframe.
        # A new frame target will be created (see Target.attachedToTarget).
        swap

  # The type of a frameNavigated event.
  experimental type NavigationType extends string
    enum
      Navigation
      BackForwardCacheRestore

  # Fired once navigation of the frame has completed. Frame is now associated with the new loader.
  event frameNavigated
    parameters
      # Frame object.
      Frame frame
      experimental NavigationType type

  # Fired when opening document to write to.
  experimental event documentOpened
    parameters
      # Frame object.
      Frame frame

  experimental event frameResized

  # Fired when a renderer-initiated navigation is requested.
  # Navigation may still be cancelled after the event is issued.
  experimental event frameRequestedNavigation
    parameters
      # Id of the frame that is being navigated.
      FrameId frameId
      # The reason for the navigation.
      ClientNavigationReason reason
      # The destination URL for the requested navigation.
      string url
      # The disposition for the navigation.
      ClientNavigationDisposition disposition

  # Fired when frame schedules a potential navigation.
  deprecated event frameScheduledNavigation
    parameters
      # Id of the frame that has scheduled a navigation.
      FrameId frameId
      # Delay (in seconds) until the navigation is scheduled to begin. The navigation is not
      # guaranteed to start.
      number delay
      # The reason for the navigation.
      ClientNavigationReason reason
      # The destination URL for the scheduled navigation.
      string url

  # Fired when frame has started loading.
  experimental event frameStartedLoading
    parameters
      # Id of the frame that has started loading.
      FrameId frameId

  # Fired when frame has stopped loading.
  experimental event frameStoppedLoading
    parameters
      # Id of the frame that has stopped loading.
      FrameId frameId

  # Fired when page is about to start a download.
  # Deprecated. Use Browser.downloadWillBegin instead.
  experimental deprecated event downloadWillBegin
    parameters
      # Id of the frame that caused download to begin.
      FrameId frameId
      # Global unique identifier of the download.
      string guid
      # URL of the resource being downloaded.
      string url
      # Suggested file name of the resource (the actual name of the file saved on disk may differ).
      string suggestedFilename

  # Fired when download makes progress. Last call has |done| == true.
  # Deprecated. Use Browser.downloadProgress instead.
  experimental deprecated event downloadProgress
    parameters
      # Global unique identifier of the download.
      string guid
      # Total expected bytes to download.
      number totalBytes
      # Total bytes received.
      number receivedBytes
      # Download status.
      enum state
        inProgress
        completed
        canceled

  # Fired when interstitial page was hidden
  event interstitialHidden

  # Fired when interstitial page was shown
  event interstitialShown

  # Fired when a JavaScript initiated dialog (alert, confirm, prompt, or onbeforeunload) has been
  # closed.
  event javascriptDialogClosed
    parameters
      # Whether dialog was confirmed.
      boolean result
      # User input in case of prompt.
      string userInput

  # Fired when a JavaScript initiated dialog (alert, confirm, prompt, or onbeforeunload) is about to
  # open.
  event javascriptDialogOpening
    parameters
      # Frame url.
      string url
      # Message that will be displayed by the dialog.
      string message
      # Dialog type.
      DialogType type
      # True iff browser is capable showing or acting on the given dialog. When browser has no
      # dialog handler for given target, calling alert while Page domain is engaged will stall
      # the page execution. Execution can be resumed via calling Page.handleJavaScriptDialog.
      boolean hasBrowserHandler
      # Default dialog prompt.
      optional string defaultPrompt

  # Fired for top level page lifecycle events such as navigation, load, paint, etc.
  event lifecycleEvent
    parameters
      # Id of the frame.
      FrameId frameId
      # Loader identifier. Empty string if the request is fetched from worker.
      Network.LoaderId loaderId
      string name
      Network.MonotonicTime timestamp

  # List of not restored reasons for back-forward cache.
  experimental type BackForwardCacheNotRestoredReason extends string
    enum
      NotPrimaryMainFrame
      BackForwardCacheDisabled
      RelatedActiveContentsExist
      HTTPStatusNotOK
      SchemeNotHTTPOrHTTPS
      Loading
      WasGrantedMediaAccess
      DisableForRenderFrameHostCalled
      DomainNotAllowed
      HTTPMethodNotGET
      SubframeIsNavigating
      Timeout
      CacheLimit
      JavaScriptExecution
      RendererProcessKilled
      RendererProcessCrashed
      SchedulerTrackedFeatureUsed
      ConflictingBrowsingInstance
      CacheFlushed
      ServiceWorkerVersionActivation
      SessionRestored
      ServiceWorkerPostMessage
      EnteredBackForwardCacheBeforeServiceWorkerHostAdded
      RenderFrameHostReused_SameSite
      RenderFrameHostReused_CrossSite
      ServiceWorkerClaim
      IgnoreEventAndEvict
      HaveInnerContents
      TimeoutPuttingInCache
      BackForwardCacheDisabledByLowMemory
      BackForwardCacheDisabledByCommandLine
      NetworkRequestDatapipeDrainedAsBytesConsumer
      NetworkRequestRedirected
      NetworkRequestTimeout
      NetworkExceedsBufferLimit
      NavigationCancelledWhileRestoring
      NotMostRecentNavigationEntry
      BackForwardCacheDisabledForPrerender
      UserAgentOverrideDiffers
      ForegroundCacheLimit
      BrowsingInstanceNotSwapped
      BackForwardCacheDisabledForDelegate
      UnloadHandlerExistsInMainFrame
      UnloadHandlerExistsInSubFrame
      ServiceWorkerUnregistration
      CacheControlNoStore
      CacheControlNoStoreCookieModified
      CacheControlNoStoreHTTPOnlyCookieModified
      NoResponseHead
      Unknown
      ActivationNavigationsDisallowedForBug1234857
      ErrorDocument
      FencedFramesEmbedder
      CookieDisabled
      HTTPAuthRequired
      CookieFlushed
      #Blocklisted features
      WebSocket
      WebTransport
      WebRTC
      MainResourceHasCacheControlNoStore
      MainResourceHasCacheControlNoCache
      SubresourceHasCacheControlNoStore
      SubresourceHasCacheControlNoCache
      ContainsPlugins
      DocumentLoaded
      DedicatedWorkerOrWorklet
      OutstandingNetworkRequestOthers
      RequestedMIDIPermission
      RequestedAudioCapturePermission
      RequestedVideoCapturePermission
      RequestedBackForwardCacheBlockedSensors
      RequestedBackgroundWorkPermission
      BroadcastChannel
      WebXR
      SharedWorker
      WebLocks
      WebHID
      WebShare
      RequestedStorageAccessGrant
      WebNfc
      OutstandingNetworkRequestFetch
      OutstandingNetworkRequestXHR
      AppBanner
      Printing
      WebDatabase
      PictureInPicture
      Portal
      SpeechRecognizer
      IdleManager
      PaymentManager
      SpeechSynthesis
      KeyboardLock
      WebOTPService
      OutstandingNetworkRequestDirectSocket
      InjectedJavascript
      InjectedStyleSheet
      KeepaliveRequest
      IndexedDBEvent
      Dummy
      JsNetworkRequestReceivedCacheControlNoStoreResource
      WebRTCSticky
      WebTransportSticky
      WebSocketSticky
      # Disabled for RenderFrameHost reasons
      # See content/browser/renderer_host/back_forward_cache_disable.h for explanations.
      ContentSecurityHandler
      ContentWebAuthenticationAPI
      ContentFileChooser
      ContentSerial
      ContentFileSystemAccess
      ContentMediaDevicesDispatcherHost
      ContentWebBluetooth
      ContentWebUSB
      ContentMediaSessionService
      ContentScreenReader

      # See components/back_forward_cache/back_forward_cache_disable.h for explanations.
      EmbedderPopupBlockerTabHelper
      EmbedderSafeBrowsingTriggeredPopupBlocker
      EmbedderSafeBrowsingThreatDetails
      EmbedderAppBannerManager
      EmbedderDomDistillerViewerSource
      EmbedderDomDistillerSelfDeletingRequestDelegate
      EmbedderOomInterventionTabHelper
      EmbedderOfflinePage
      EmbedderChromePasswordManagerClientBindCredentialManager
      EmbedderPermissionRequestManager
      EmbedderModalDialog
      EmbedderExtensions
      EmbedderExtensionMessaging
      EmbedderExtensionMessagingForOpenPort
      EmbedderExtensionSentMessageToCachedFrame

  # Types of not restored reasons for back-forward cache.
  experimental type BackForwardCacheNotRestoredReasonType extends string
    enum
      SupportPending
      PageSupportNeeded
      Circumstantial

  experimental type BackForwardCacheBlockingDetails extends object
    properties
      # Url of the file where blockage happened. Optional because of tests.
      optional string url
      # Function name where blockage happened. Optional because of anonymous functions and tests.
      optional string function
      # Line number in the script (0-based).
      integer lineNumber
      # Column number in the script (0-based).
      integer columnNumber

  experimental type BackForwardCacheNotRestoredExplanation extends object
    properties
      # Type of the reason
      BackForwardCacheNotRestoredReasonType type
      # Not restored reason
      BackForwardCacheNotRestoredReason reason
      # Context associated with the reason. The meaning of this context is
      # dependent on the reason:
      # - EmbedderExtensionSentMessageToCachedFrame: the extension ID.
      #
      optional string context
      optional array of BackForwardCacheBlockingDetails details

  experimental type BackForwardCacheNotRestoredExplanationTree extends object
    properties
      # URL of each frame
      string url
      # Not restored reasons of each frame
      array of BackForwardCacheNotRestoredExplanation explanations
      # Array of children frame
      array of BackForwardCacheNotRestoredExplanationTree children

  # Fired for failed bfcache history navigations if BackForwardCache feature is enabled. Do
  # not assume any ordering with the Page.frameNavigated event. This event is fired only for
  # main-frame history navigation where the document changes (non-same-document navigations),
  # when bfcache navigation fails.
  experimental event backForwardCacheNotUsed
    parameters
      # The loader id for the associated navgation.
      Network.LoaderId loaderId
      # The frame id of the associated frame.
      FrameId frameId
      # Array of reasons why the page could not be cached. This must not be empty.
      array of BackForwardCacheNotRestoredExplanation notRestoredExplanations
      # Tree structure of reasons why the page could not be cached for each frame.
      optional BackForwardCacheNotRestoredExplanationTree notRestoredExplanationsTree

  event loadEventFired
    parameters
      Network.MonotonicTime timestamp

  # Fired when same-document navigation happens, e.g. due to history API usage or anchor navigation.
  experimental event navigatedWithinDocument
    parameters
      # Id of the frame.
      FrameId frameId
      # Frame's new url.
      string url

  # Compressed image data requested by the `startScreencast`.
  experimental event screencastFrame
    parameters
      # Base64-encoded compressed image.
      binary data
      # Screencast frame metadata.
      ScreencastFrameMetadata metadata
      # Frame number.
      integer sessionId

  # Fired when the page with currently enabled screencast was shown or hidden `.
  experimental event screencastVisibilityChanged
    parameters
      # True if the page is visible.
      boolean visible

  # Fired when a new window is going to be opened, via window.open(), link click, form submission,
  # etc.
  event windowOpen
    parameters
      # The URL for the new window.
      string url
      # Window name.
      string windowName
      # An array of enabled window features.
      array of string windowFeatures
      # Whether or not it was triggered by user gesture.
      boolean userGesture

  # Issued for every compilation cache generated. Is only available
  # if Page.setGenerateCompilationCache is enabled.
  experimental event compilationCacheProduced
    parameters
      string url
      # Base64-encoded data
      binary data

  # Enable/disable prerendering manually.
  #
  # This command is a short-term solution for https://crbug.com/1440085.
  # See https://docs.google.com/document/d/12HVmFxYj5Jc-eJr5OmWsa2bqTJsbgGLKI6ZIyx0_wpA
  # for more details.
  #
  # TODO(https://crbug.com/1440085): Remove this once Puppeteer supports tab targets.
  experimental command setPrerenderingAllowed
    parameters
      boolean isAllowed

domain Performance

  # Run-time execution metric.
  type Metric extends object
    properties
      # Metric name.
      string name
      # Metric value.
      number value

  # Disable collecting and reporting metrics.
  command disable

  # Enable collecting and reporting metrics.
  command enable
    parameters
      # Time domain to use for collecting and reporting duration metrics.
      optional enum timeDomain
        # Use monotonically increasing abstract time (default).
        timeTicks
        # Use thread running time.
        threadTicks

  # Sets time domain to use for collecting and reporting duration metrics.
  # Note that this must be called before enabling metrics collection. Calling
  # this method while metrics collection is enabled returns an error.
  experimental deprecated command setTimeDomain
    parameters
      # Time domain
      enum timeDomain
        # Use monotonically increasing abstract time (default).
        timeTicks
        # Use thread running time.
        threadTicks

  # Retrieve current values of run-time metrics.
  command getMetrics
    returns
      # Current values for run-time metrics.
      array of Metric metrics

  # Current values of the metrics.
  event metrics
    parameters
      # Current values of the metrics.
      array of Metric metrics
      # Timestamp title.
      string title

# Reporting of performance timeline events, as specified in
# https://w3c.github.io/performance-timeline/#dom-performanceobserver.
experimental domain PerformanceTimeline
  depends on DOM
  depends on Network

  # See https://github.com/WICG/LargestContentfulPaint and largest_contentful_paint.idl
  type LargestContentfulPaint extends object
    properties
      Network.TimeSinceEpoch renderTime
      Network.TimeSinceEpoch loadTime
      # The number of pixels being painted.
      number size
      # The id attribute of the element, if available.
      optional string elementId
      # The URL of the image (may be trimmed).
      optional string url
      optional DOM.BackendNodeId nodeId

  type LayoutShiftAttribution extends object
    properties
      DOM.Rect previousRect
      DOM.Rect currentRect
      optional DOM.BackendNodeId nodeId

  # See https://wicg.github.io/layout-instability/#sec-layout-shift and layout_shift.idl
  type LayoutShift extends object
    properties
      # Score increment produced by this event.
      number value
      boolean hadRecentInput
      Network.TimeSinceEpoch lastInputTime
      array of LayoutShiftAttribution sources

  type TimelineEvent extends object
    properties
      # Identifies the frame that this event is related to. Empty for non-frame targets.
      Page.FrameId frameId
      # The event type, as specified in https://w3c.github.io/performance-timeline/#dom-performanceentry-entrytype
      # This determines which of the optional "details" fiedls is present.
      string type
      # Name may be empty depending on the type.
      string name
      # Time in seconds since Epoch, monotonically increasing within document lifetime.
      Network.TimeSinceEpoch time
      # Event duration, if applicable.
      optional number duration
      optional LargestContentfulPaint lcpDetails
      optional LayoutShift layoutShiftDetails

  # Previously buffered events would be reported before method returns.
  # See also: timelineEventAdded
  command enable
    parameters
      # The types of event to report, as specified in
      # https://w3c.github.io/performance-timeline/#dom-performanceentry-entrytype
      # The specified filter overrides any previous filters, passing empty
      # filter disables recording.
      # Note that not all types exposed to the web platform are currently supported.
      array of string eventTypes

  # Sent when a performance timeline event is added. See reportPerformanceTimeline method.
  event timelineEventAdded
    parameters
      TimelineEvent event

# Security
domain Security

  # An internal certificate ID value.
  type CertificateId extends integer

  # A description of mixed content (HTTP resources on HTTPS pages), as defined by
  # https://www.w3.org/TR/mixed-content/#categories
  type MixedContentType extends string
    enum
      blockable
      optionally-blockable
      none

  # The security level of a page or resource.
  type SecurityState extends string
    enum
      unknown
      neutral
      insecure
      secure
      info
      insecure-broken

  # Details about the security state of the page certificate.
  experimental type CertificateSecurityState extends object
    properties
      # Protocol name (e.g. "TLS 1.2" or "QUIC").
      string protocol
      # Key Exchange used by the connection, or the empty string if not applicable.
      string keyExchange
      # (EC)DH group used by the connection, if applicable.
      optional string keyExchangeGroup
      # Cipher name.
      string cipher
      # TLS MAC. Note that AEAD ciphers do not have separate MACs.
      optional string mac
      # Page certificate.
      array of string certificate
      # Certificate subject name.
      string subjectName
      # Name of the issuing CA.
      string issuer
      # Certificate valid from date.
      Network.TimeSinceEpoch validFrom
      # Certificate valid to (expiration) date
      Network.TimeSinceEpoch validTo
      # The highest priority network error code, if the certificate has an error.
      optional string certificateNetworkError
      # True if the certificate uses a weak signature aglorithm.
      boolean certificateHasWeakSignature
      # True if the certificate has a SHA1 signature in the chain.
      boolean certificateHasSha1Signature
      # True if modern SSL
      boolean modernSSL
      # True if the connection is using an obsolete SSL protocol.
      boolean obsoleteSslProtocol
      # True if the connection is using an obsolete SSL key exchange.
      boolean obsoleteSslKeyExchange
      # True if the connection is using an obsolete SSL cipher.
      boolean obsoleteSslCipher
      # True if the connection is using an obsolete SSL signature.
      boolean obsoleteSslSignature

  experimental type SafetyTipStatus extends string
    enum
      badReputation
      lookalike

  experimental type SafetyTipInfo extends object
    properties
      # Describes whether the page triggers any safety tips or reputation warnings. Default is unknown.
      SafetyTipStatus safetyTipStatus
      # The URL the safety tip suggested ("Did you mean?"). Only filled in for lookalike matches.
      optional string safeUrl

  # Security state information about the page.
  experimental type VisibleSecurityState extends object
    properties
      # The security level of the page.
      SecurityState securityState
      # Security state details about the page certificate.
      optional CertificateSecurityState certificateSecurityState
      # The type of Safety Tip triggered on the page. Note that this field will be set even if the Safety Tip UI was not actually shown.
      optional SafetyTipInfo safetyTipInfo
      # Array of security state issues ids.
      array of string securityStateIssueIds

  # An explanation of an factor contributing to the security state.
  type SecurityStateExplanation extends object
    properties
      # Security state representing the severity of the factor being explained.
      SecurityState securityState
      # Title describing the type of factor.
      string title
      # Short phrase describing the type of factor.
      string summary
      # Full text explanation of the factor.
      string description
      # The type of mixed content described by the explanation.
      MixedContentType mixedContentType
      # Page certificate.
      array of string certificate
      # Recommendations to fix any issues.
      optional array of string recommendations

  # Information about insecure content on the page.
  deprecated type InsecureContentStatus extends object
    properties
      # Always false.
      boolean ranMixedContent
      # Always false.
      boolean displayedMixedContent
      # Always false.
      boolean containedMixedForm
      # Always false.
      boolean ranContentWithCertErrors
      # Always false.
      boolean displayedContentWithCertErrors
      # Always set to unknown.
      SecurityState ranInsecureContentStyle
      # Always set to unknown.
      SecurityState displayedInsecureContentStyle

  # The action to take when a certificate error occurs. continue will continue processing the
  # request and cancel will cancel the request.
  type CertificateErrorAction extends string
    enum
      continue
      cancel

  # Disables tracking security state changes.
  command disable

  # Enables tracking security state changes.
  command enable

  # Enable/disable whether all certificate errors should be ignored.
  experimental command setIgnoreCertificateErrors
    parameters
      # If true, all certificate errors will be ignored.
      boolean ignore

  # Handles a certificate error that fired a certificateError event.
  deprecated command handleCertificateError
    parameters
      # The ID of the event.
      integer eventId
      # The action to take on the certificate error.
      CertificateErrorAction action

  # Enable/disable overriding certificate errors. If enabled, all certificate error events need to
  # be handled by the DevTools client and should be answered with `handleCertificateError` commands.
  deprecated command setOverrideCertificateErrors
    parameters
      # If true, certificate errors will be overridden.
      boolean override

  # There is a certificate error. If overriding certificate errors is enabled, then it should be
  # handled with the `handleCertificateError` command. Note: this event does not fire if the
  # certificate error has been allowed internally. Only one client per target should override
  # certificate errors at the same time.
  deprecated event certificateError
    parameters
      # The ID of the event.
      integer eventId
      # The type of the error.
      string errorType
      # The url that was requested.
      string requestURL

  # The security state of the page changed.
  experimental event visibleSecurityStateChanged
    parameters
      # Security state information about the page.
      VisibleSecurityState visibleSecurityState

  # The security state of the page changed. No longer being sent.
  deprecated event securityStateChanged
    parameters
      # Security state.
      SecurityState securityState
      # True if the page was loaded over cryptographic transport such as HTTPS.
      deprecated boolean schemeIsCryptographic
      # Previously a list of explanations for the security state. Now always
      # empty.
      deprecated array of SecurityStateExplanation explanations
      # Information about insecure content on the page.
      deprecated InsecureContentStatus insecureContentStatus
      # Overrides user-visible description of the state. Always omitted.
      deprecated optional string summary

experimental domain ServiceWorker
  depends on Target

  type RegistrationID extends string

  # ServiceWorker registration.
  type ServiceWorkerRegistration extends object
    properties
      RegistrationID registrationId
      string scopeURL
      boolean isDeleted

  type ServiceWorkerVersionRunningStatus extends string
    enum
      stopped
      starting
      running
      stopping

  type ServiceWorkerVersionStatus extends string
    enum
      new
      installing
      installed
      activating
      activated
      redundant

  # ServiceWorker version.
  type ServiceWorkerVersion extends object
    properties
      string versionId
      RegistrationID registrationId
      string scriptURL
      ServiceWorkerVersionRunningStatus runningStatus
      ServiceWorkerVersionStatus status
      # The Last-Modified header value of the main script.
      optional number scriptLastModified
      # The time at which the response headers of the main script were received from the server.
      # For cached script it is the last time the cache entry was validated.
      optional number scriptResponseTime
      optional array of Target.TargetID controlledClients
      optional Target.TargetID targetId
      optional string routerRules

  # ServiceWorker error message.
  type ServiceWorkerErrorMessage extends object
    properties
      string errorMessage
      RegistrationID registrationId
      string versionId
      string sourceURL
      integer lineNumber
      integer columnNumber

  command deliverPushMessage
    parameters
      string origin
      RegistrationID registrationId
      string data

  command disable

  command dispatchSyncEvent
    parameters
      string origin
      RegistrationID registrationId
      string tag
      boolean lastChance

  command dispatchPeriodicSyncEvent
    parameters
      string origin
      RegistrationID registrationId
      string tag

  command enable

  command inspectWorker
    parameters
      string versionId

  command setForceUpdateOnPageLoad
    parameters
      boolean forceUpdateOnPageLoad

  command skipWaiting
    parameters
      string scopeURL

  command startWorker
    parameters
      string scopeURL

  command stopAllWorkers

  command stopWorker
    parameters
      string versionId

  command unregister
    parameters
      string scopeURL

  command updateRegistration
    parameters
      string scopeURL

  event workerErrorReported
    parameters
      ServiceWorkerErrorMessage errorMessage

  event workerRegistrationUpdated
    parameters
      array of ServiceWorkerRegistration registrations

  event workerVersionUpdated
    parameters
      array of ServiceWorkerVersion versions

experimental domain Storage
  depends on Browser
  depends on Network

  type SerializedStorageKey extends string

  # Enum of possible storage types.
  type StorageType extends string
    enum
      appcache
      cookies
      file_systems
      indexeddb
      local_storage
      shader_cache
      websql
      service_workers
      cache_storage
      interest_groups
      shared_storage
      storage_buckets
      all
      other

  # Usage for a storage type.
  type UsageForType extends object
    properties
      # Name of storage type.
      StorageType storageType
      # Storage usage (bytes).
      number usage

  # Pair of issuer origin and number of available (signed, but not used) Trust
  # Tokens from that issuer.
  experimental type TrustTokens extends object
    properties
      string issuerOrigin
      number count

  # Enum of interest group access types.
  type InterestGroupAccessType extends string
    enum
      join
      leave
      update
      loaded
      bid
      win
      additionalBid
      additionalBidWin
      clear

  # Ad advertising element inside an interest group.
  type InterestGroupAd extends object
    properties
      string renderURL
      optional string metadata

  # The full details of an interest group.
  type InterestGroupDetails extends object
    properties
      string ownerOrigin
      string name
      Network.TimeSinceEpoch expirationTime
      string joiningOrigin
      optional string biddingLogicURL
      optional string biddingWasmHelperURL
      optional string updateURL
      optional string trustedBiddingSignalsURL
      array of string trustedBiddingSignalsKeys
      optional string userBiddingSignals
      array of InterestGroupAd ads
      array of InterestGroupAd adComponents

  # Enum of shared storage access types.
  type SharedStorageAccessType extends string
    enum
      documentAddModule
      documentSelectURL
      documentRun
      documentSet
      documentAppend
      documentDelete
      documentClear
      workletSet
      workletAppend
      workletDelete
      workletClear
      workletGet
      workletKeys
      workletEntries
      workletLength
      workletRemainingBudget

  # Struct for a single key-value pair in an origin's shared storage.
  type SharedStorageEntry extends object
    properties
      string key
      string value

  # Details for an origin's shared storage.
  type SharedStorageMetadata extends object
    properties
      Network.TimeSinceEpoch creationTime
      integer length
      number remainingBudget

  # Pair of reporting metadata details for a candidate URL for `selectURL()`.
  type SharedStorageReportingMetadata extends object
    properties
      string eventType
      string reportingUrl

  # Bundles a candidate URL with its reporting metadata.
  type SharedStorageUrlWithMetadata extends object
    properties
      # Spec of candidate URL.
      string url
      # Any associated reporting metadata.
      array of SharedStorageReportingMetadata reportingMetadata

  # Bundles the parameters for shared storage access events whose
  # presence/absence can vary according to SharedStorageAccessType.
  type SharedStorageAccessParams extends object
    properties
      # Spec of the module script URL.
      # Present only for SharedStorageAccessType.documentAddModule.
      optional string scriptSourceUrl
      # Name of the registered operation to be run.
      # Present only for SharedStorageAccessType.documentRun and
      # SharedStorageAccessType.documentSelectURL.
      optional string operationName
      # The operation's serialized data in bytes (converted to a string).
      # Present only for SharedStorageAccessType.documentRun and
      # SharedStorageAccessType.documentSelectURL.
      optional string serializedData
      # Array of candidate URLs' specs, along with any associated metadata.
      # Present only for SharedStorageAccessType.documentSelectURL.
      optional array of SharedStorageUrlWithMetadata urlsWithMetadata
      # Key for a specific entry in an origin's shared storage.
      # Present only for SharedStorageAccessType.documentSet,
      # SharedStorageAccessType.documentAppend,
      # SharedStorageAccessType.documentDelete,
      # SharedStorageAccessType.workletSet,
      # SharedStorageAccessType.workletAppend,
      # SharedStorageAccessType.workletDelete, and
      # SharedStorageAccessType.workletGet.
      optional string key
      # Value for a specific entry in an origin's shared storage.
      # Present only for SharedStorageAccessType.documentSet,
      # SharedStorageAccessType.documentAppend,
      # SharedStorageAccessType.workletSet, and
      # SharedStorageAccessType.workletAppend.
      optional string value
      # Whether or not to set an entry for a key if that key is already present.
      # Present only for SharedStorageAccessType.documentSet and
      # SharedStorageAccessType.workletSet.
      optional boolean ignoreIfPresent

  type StorageBucketsDurability extends string
    enum
      relaxed
      strict

  type StorageBucket extends object
    properties
      SerializedStorageKey storageKey
      # If not specified, it is the default bucket of the storageKey.
      optional string name

  type StorageBucketInfo extends object
    properties
      StorageBucket bucket
      string id
      Network.TimeSinceEpoch expiration
      # Storage quota (bytes).
      number quota
      boolean persistent
      StorageBucketsDurability durability

  # Returns a storage key given a frame id.
  command getStorageKeyForFrame
    parameters
      Page.FrameId frameId
    returns
      SerializedStorageKey storageKey

  # Clears storage for origin.
  command clearDataForOrigin
    parameters
      # Security origin.
      string origin
      # Comma separated list of StorageType to clear.
      string storageTypes

  # Clears storage for storage key.
  command clearDataForStorageKey
    parameters
      # Storage key.
      string storageKey
      # Comma separated list of StorageType to clear.
      string storageTypes

  # Returns all browser cookies.
  command getCookies
    parameters
      # Browser context to use when called on the browser endpoint.
      optional Browser.BrowserContextID browserContextId
    returns
      # Array of cookie objects.
      array of Network.Cookie cookies

  # Sets given cookies.
  command setCookies
    parameters
      # Cookies to be set.
      array of Network.CookieParam cookies
      # Browser context to use when called on the browser endpoint.
      optional Browser.BrowserContextID browserContextId

  # Clears cookies.
  command clearCookies
    parameters
      # Browser context to use when called on the browser endpoint.
      optional Browser.BrowserContextID browserContextId

  # Returns usage and quota in bytes.
  command getUsageAndQuota
    parameters
      # Security origin.
      string origin
    returns
      # Storage usage (bytes).
      number usage
      # Storage quota (bytes).
      number quota
      # Whether or not the origin has an active storage quota override
      boolean overrideActive
      # Storage usage per type (bytes).
      array of UsageForType usageBreakdown

  # Override quota for the specified origin
  experimental command overrideQuotaForOrigin
    parameters
      # Security origin.
      string origin
      # The quota size (in bytes) to override the original quota with.
      # If this is called multiple times, the overridden quota will be equal to
      # the quotaSize provided in the final call. If this is called without
      # specifying a quotaSize, the quota will be reset to the default value for
      # the specified origin. If this is called multiple times with different
      # origins, the override will be maintained for each origin until it is
      # disabled (called without a quotaSize).
      optional number quotaSize

  # Registers origin to be notified when an update occurs to its cache storage list.
  command trackCacheStorageForOrigin
    parameters
      # Security origin.
      string origin

  # Registers storage key to be notified when an update occurs to its cache storage list.
  command trackCacheStorageForStorageKey
    parameters
      # Storage key.
      string storageKey

  # Registers origin to be notified when an update occurs to its IndexedDB.
  command trackIndexedDBForOrigin
    parameters
      # Security origin.
      string origin

  # Registers storage key to be notified when an update occurs to its IndexedDB.
  command trackIndexedDBForStorageKey
    parameters
      # Storage key.
      string storageKey

  # Unregisters origin from receiving notifications for cache storage.
  command untrackCacheStorageForOrigin
    parameters
      # Security origin.
      string origin

  # Unregisters storage key from receiving notifications for cache storage.
  command untrackCacheStorageForStorageKey
    parameters
      # Storage key.
      string storageKey

  # Unregisters origin from receiving notifications for IndexedDB.
  command untrackIndexedDBForOrigin
    parameters
      # Security origin.
      string origin

  # Unregisters storage key from receiving notifications for IndexedDB.
  command untrackIndexedDBForStorageKey
    parameters
      # Storage key.
      string storageKey

  # Returns the number of stored Trust Tokens per issuer for the
  # current browsing context.
  experimental command getTrustTokens
    returns
      array of TrustTokens tokens

  # Removes all Trust Tokens issued by the provided issuerOrigin.
  # Leaves other stored data, including the issuer's Redemption Records, intact.
  experimental command clearTrustTokens
    parameters
      string issuerOrigin
    returns
      # True if any tokens were deleted, false otherwise.
      boolean didDeleteTokens

  # Gets details for a named interest group.
  experimental command getInterestGroupDetails
    parameters
      string ownerOrigin
      string name
    returns
      InterestGroupDetails details

  # Enables/Disables issuing of interestGroupAccessed events.
  experimental command setInterestGroupTracking
    parameters
      boolean enable

  # Gets metadata for an origin's shared storage.
  experimental command getSharedStorageMetadata
    parameters
      string ownerOrigin
    returns
      SharedStorageMetadata metadata

  # Gets the entries in an given origin's shared storage.
  experimental command getSharedStorageEntries
    parameters
      string ownerOrigin
    returns
      array of SharedStorageEntry entries

  # Sets entry with `key` and `value` for a given origin's shared storage.
  experimental command setSharedStorageEntry
    parameters
      string ownerOrigin
      string key
      string value
      # If `ignoreIfPresent` is included and true, then only sets the entry if
      # `key` doesn't already exist.
      optional boolean ignoreIfPresent

  # Deletes entry for `key` (if it exists) for a given origin's shared storage.
  experimental command deleteSharedStorageEntry
    parameters
      string ownerOrigin
      string key

  # Clears all entries for a given origin's shared storage.
  experimental command clearSharedStorageEntries
    parameters
      string ownerOrigin

  # Resets the budget for `ownerOrigin` by clearing all budget withdrawals.
  experimental command resetSharedStorageBudget
    parameters
      string ownerOrigin

  # Enables/disables issuing of sharedStorageAccessed events.
  experimental command setSharedStorageTracking
    parameters
      boolean enable

  # Set tracking for a storage key's buckets.
  experimental command setStorageBucketTracking
    parameters
      string storageKey
      boolean enable

  # Deletes the Storage Bucket with the given storage key and bucket name.
  experimental command deleteStorageBucket
    parameters
      StorageBucket bucket

  # Deletes state for sites identified as potential bounce trackers, immediately.
  experimental command runBounceTrackingMitigations
    returns
      array of string deletedSites

  # A cache's contents have been modified.
  event cacheStorageContentUpdated
    parameters
      # Origin to update.
      string origin
      # Storage key to update.
      string storageKey
      # Storage bucket to update.
      string bucketId
      # Name of cache in origin.
      string cacheName

  # A cache has been added/deleted.
  event cacheStorageListUpdated
    parameters
      # Origin to update.
      string origin
      # Storage key to update.
      string storageKey
      # Storage bucket to update.
      string bucketId

  # The origin's IndexedDB object store has been modified.
  event indexedDBContentUpdated
    parameters
      # Origin to update.
      string origin
      # Storage key to update.
      string storageKey
      # Storage bucket to update.
      string bucketId
      # Database to update.
      string databaseName
      # ObjectStore to update.
      string objectStoreName

  # The origin's IndexedDB database list has been modified.
  event indexedDBListUpdated
    parameters
      # Origin to update.
      string origin
      # Storage key to update.
      string storageKey
      # Storage bucket to update.
      string bucketId

  # One of the interest groups was accessed by the associated page.
  event interestGroupAccessed
    parameters
      Network.TimeSinceEpoch accessTime
      InterestGroupAccessType type
      string ownerOrigin
      string name

  # Shared storage was accessed by the associated page.
  # The following parameters are included in all events.
  event sharedStorageAccessed
    parameters
      # Time of the access.
      Network.TimeSinceEpoch accessTime
      # Enum value indicating the Shared Storage API method invoked.
      SharedStorageAccessType type
      # DevTools Frame Token for the primary frame tree's root.
      Page.FrameId mainFrameId
      # Serialized origin for the context that invoked the Shared Storage API.
      string ownerOrigin
      # The sub-parameters warapped by `params` are all optional and their
      # presence/absence depends on `type`.
      SharedStorageAccessParams params

  event storageBucketCreatedOrUpdated
    parameters
      StorageBucketInfo bucketInfo

  event storageBucketDeleted
    parameters
      string bucketId

  # https://wicg.github.io/attribution-reporting-api/
  experimental command setAttributionReportingLocalTestingMode
    parameters
      # If enabled, noise is suppressed and reports are sent immediately.
      boolean enabled

  # Enables/disables issuing of Attribution Reporting events.
  experimental command setAttributionReportingTracking
    parameters
      boolean enable

  experimental type AttributionReportingSourceType extends string
    enum
      navigation
      event

  experimental type UnsignedInt64AsBase10 extends string
  experimental type UnsignedInt128AsBase16 extends string
  experimental type SignedInt64AsBase10 extends string

  experimental type AttributionReportingFilterDataEntry extends object
    properties
      string key
      array of string values

  experimental type AttributionReportingAggregationKeysEntry extends object
    properties
      string key
      UnsignedInt128AsBase16 value

  experimental type AttributionReportingEventReportWindows extends object
    properties
      # duration in seconds
      integer start
      # duration in seconds
      array of integer ends

  experimental type AttributionReportingTriggerSpec extends object
    properties
      # number instead of integer because not all uint32 can be represented by
      # int
      array of number triggerData
      AttributionReportingEventReportWindows eventReportWindows

  experimental type AttributionReportingTriggerDataMatching extends string
    enum
      exact
      modulus

  experimental type AttributionReportingSourceRegistration extends object
    properties
      Network.TimeSinceEpoch time
      # duration in seconds
      integer expiry
      array of AttributionReportingTriggerSpec triggerSpecs
      # duration in seconds
      integer aggregatableReportWindow
      AttributionReportingSourceType type
      string sourceOrigin
      string reportingOrigin
      array of string destinationSites
      UnsignedInt64AsBase10 eventId
      SignedInt64AsBase10 priority
      array of AttributionReportingFilterDataEntry filterData
      array of AttributionReportingAggregationKeysEntry aggregationKeys
      optional UnsignedInt64AsBase10 debugKey
      AttributionReportingTriggerDataMatching triggerDataMatching

  experimental type AttributionReportingSourceRegistrationResult extends string
    enum
      success
      internalError
      insufficientSourceCapacity
      insufficientUniqueDestinationCapacity
      excessiveReportingOrigins
      prohibitedByBrowserPolicy
      successNoised
      destinationReportingLimitReached
      destinationGlobalLimitReached
      destinationBothLimitsReached
      reportingOriginsPerSiteLimitReached
      exceedsMaxChannelCapacity

  # TODO(crbug.com/1458532): Add other Attribution Reporting events, e.g.
  # trigger registration.
  experimental event attributionReportingSourceRegistered
    parameters
      AttributionReportingSourceRegistration registration
      AttributionReportingSourceRegistrationResult result

# The SystemInfo domain defines methods and events for querying low-level system information.
experimental domain SystemInfo

  # Describes a single graphics processor (GPU).
  type GPUDevice extends object
    properties
      # PCI ID of the GPU vendor, if available; 0 otherwise.
      number vendorId
      # PCI ID of the GPU device, if available; 0 otherwise.
      number deviceId
      # Sub sys ID of the GPU, only available on Windows.
      optional number subSysId
      # Revision of the GPU, only available on Windows.
      optional number revision
      # String description of the GPU vendor, if the PCI ID is not available.
      string vendorString
      # String description of the GPU device, if the PCI ID is not available.
      string deviceString
      # String description of the GPU driver vendor.
      string driverVendor
      # String description of the GPU driver version.
      string driverVersion

  # Describes the width and height dimensions of an entity.
  type Size extends object
    properties
      # Width in pixels.
      integer width
      # Height in pixels.
      integer height

  # Describes a supported video decoding profile with its associated minimum and
  # maximum resolutions.
  type VideoDecodeAcceleratorCapability extends object
    properties
      # Video codec profile that is supported, e.g. VP9 Profile 2.
      string profile
      # Maximum video dimensions in pixels supported for this |profile|.
      Size maxResolution
      # Minimum video dimensions in pixels supported for this |profile|.
      Size minResolution

  # Describes a supported video encoding profile with its associated maximum
  # resolution and maximum framerate.
  type VideoEncodeAcceleratorCapability extends object
    properties
      # Video codec profile that is supported, e.g H264 Main.
      string profile
      # Maximum video dimensions in pixels supported for this |profile|.
      Size maxResolution
      # Maximum encoding framerate in frames per second supported for this
      # |profile|, as fraction's numerator and denominator, e.g. 24/1 fps,
      # 24000/1001 fps, etc.
      integer maxFramerateNumerator
      integer maxFramerateDenominator

  # YUV subsampling type of the pixels of a given image.
  type SubsamplingFormat extends string
    enum
      yuv420
      yuv422
      yuv444

  # Image format of a given image.
  type ImageType extends string
    enum
      jpeg
      webp
      unknown

  # Describes a supported image decoding profile with its associated minimum and
  # maximum resolutions and subsampling.
  type ImageDecodeAcceleratorCapability extends object
    properties
      # Image coded, e.g. Jpeg.
      ImageType imageType
      # Maximum supported dimensions of the image in pixels.
      Size maxDimensions
      # Minimum supported dimensions of the image in pixels.
      Size minDimensions
      # Optional array of supported subsampling formats, e.g. 4:2:0, if known.
      array of SubsamplingFormat subsamplings

  # Provides information about the GPU(s) on the system.
  type GPUInfo extends object
    properties
      # The graphics devices on the system. Element 0 is the primary GPU.
      array of GPUDevice devices
      # An optional dictionary of additional GPU related attributes.
      optional object auxAttributes
      # An optional dictionary of graphics features and their status.
      optional object featureStatus
      # An optional array of GPU driver bug workarounds.
      array of string driverBugWorkarounds
      # Supported accelerated video decoding capabilities.
      array of VideoDecodeAcceleratorCapability videoDecoding
      # Supported accelerated video encoding capabilities.
      array of VideoEncodeAcceleratorCapability videoEncoding
      # Supported accelerated image decoding capabilities.
      array of ImageDecodeAcceleratorCapability imageDecoding

  # Represents process info.
  type ProcessInfo extends object
    properties
      # Specifies process type.
      string type
      # Specifies process id.
      integer id
      # Specifies cumulative CPU usage in seconds across all threads of the
      # process since the process start.
      number cpuTime

  # Returns information about the system.
  command getInfo
    returns
      # Information about the GPUs on the system.
      GPUInfo gpu
      # A platform-dependent description of the model of the machine. On Mac OS, this is, for
      # example, 'MacBookPro'. Will be the empty string if not supported.
      string modelName
      # A platform-dependent description of the version of the machine. On Mac OS, this is, for
      # example, '10.1'. Will be the empty string if not supported.
      string modelVersion
      # The command line string used to launch the browser. Will be the empty string if not
      # supported.
      string commandLine

  # Returns information about the feature state.
  command getFeatureState
    parameters
      string featureState
    returns
      boolean featureEnabled

  # Returns information about all running processes.
  command getProcessInfo
    returns
      # An array of process info blocks.
      array of ProcessInfo processInfo

# Supports additional targets discovery and allows to attach to them.
domain Target

  type TargetID extends string

  # Unique identifier of attached debugging session.
  type SessionID extends string

  type TargetInfo extends object
    properties
      TargetID targetId
      string type
      string title
      string url
      # Whether the target has an attached client.
      boolean attached
      # Opener target Id
      optional TargetID openerId
      # Whether the target has access to the originating window.
      experimental boolean canAccessOpener
      # Frame id of originating window (is only set if target has an opener).
      experimental optional Page.FrameId openerFrameId
      experimental optional Browser.BrowserContextID browserContextId
      # Provides additional details for specific target types. For example, for
      # the type of "page", this may be set to "portal" or "prerender".
      experimental optional string subtype

  # A filter used by target query/discovery/auto-attach operations.
  experimental type FilterEntry extends object
    properties
      # If set, causes exclusion of mathcing targets from the list.
      optional boolean exclude
      # If not present, matches any type.
      optional string type

  # The entries in TargetFilter are matched sequentially against targets and
  # the first entry that matches determines if the target is included or not,
  # depending on the value of `exclude` field in the entry.
  # If filter is not specified, the one assumed is
  # [{type: "browser", exclude: true}, {type: "tab", exclude: true}, {}]
  # (i.e. include everything but `browser` and `tab`).
  experimental type TargetFilter extends array of FilterEntry

  experimental type RemoteLocation extends object
    properties
      string host
      integer port

  # Activates (focuses) the target.
  command activateTarget
    parameters
      TargetID targetId

  # Attaches to the target with given id.
  command attachToTarget
    parameters
      TargetID targetId
      # Enables "flat" access to the session via specifying sessionId attribute in the commands.
      # We plan to make this the default, deprecate non-flattened mode,
      # and eventually retire it. See crbug.com/991325.
      optional boolean flatten
    returns
      # Id assigned to the session.
      SessionID sessionId

  # Attaches to the browser target, only uses flat sessionId mode.
  experimental command attachToBrowserTarget
    returns
      # Id assigned to the session.
      SessionID sessionId

  # Closes the target. If the target is a page that gets closed too.
  command closeTarget
    parameters
      TargetID targetId
    returns
      # Always set to true. If an error occurs, the response indicates protocol error.
      deprecated boolean success

  # Inject object to the target's main frame that provides a communication
  # channel with browser target.
  #
  # Injected object will be available as `window[bindingName]`.
  #
  # The object has the follwing API:
  # - `binding.send(json)` - a method to send messages over the remote debugging protocol
  # - `binding.onmessage = json => handleMessage(json)` - a callback that will be called for the protocol notifications and command responses.
  experimental command exposeDevToolsProtocol
    parameters
      TargetID targetId
      # Binding name, 'cdp' if not specified.
      optional string bindingName

  # Creates a new empty BrowserContext. Similar to an incognito profile but you can have more than
  # one.
  experimental command createBrowserContext
    parameters
      # If specified, disposes this context when debugging session disconnects.
      optional boolean disposeOnDetach
      # Proxy server, similar to the one passed to --proxy-server
      optional string proxyServer
      # Proxy bypass list, similar to the one passed to --proxy-bypass-list
      optional string proxyBypassList
      # An optional list of origins to grant unlimited cross-origin access to.
      # Parts of the URL other than those constituting origin are ignored.
      optional array of string originsWithUniversalNetworkAccess
    returns
      # The id of the context created.
      Browser.BrowserContextID browserContextId

  # Returns all browser contexts created with `Target.createBrowserContext` method.
  experimental command getBrowserContexts
    returns
      # An array of browser context ids.
      array of Browser.BrowserContextID browserContextIds

  # Creates a new page.
  command createTarget
    parameters
      # The initial URL the page will be navigated to. An empty string indicates about:blank.
      string url
      # Frame width in DIP (headless chrome only).
      optional integer width
      # Frame height in DIP (headless chrome only).
      optional integer height
      # The browser context to create the page in.
      experimental optional Browser.BrowserContextID browserContextId
      # Whether BeginFrames for this target will be controlled via DevTools (headless chrome only,
      # not supported on MacOS yet, false by default).
      experimental optional boolean enableBeginFrameControl
      # Whether to create a new Window or Tab (chrome-only, false by default).
      optional boolean newWindow
      # Whether to create the target in background or foreground (chrome-only,
      # false by default).
      optional boolean background
      # Whether to create the target of type "tab".
      experimental optional boolean forTab
    returns
      # The id of the page opened.
      TargetID targetId

  # Detaches session with given id.
  command detachFromTarget
    parameters
      # Session to detach.
      optional SessionID sessionId
      # Deprecated.
      deprecated optional TargetID targetId

  # Deletes a BrowserContext. All the belonging pages will be closed without calling their
  # beforeunload hooks.
  experimental command disposeBrowserContext
    parameters
      Browser.BrowserContextID browserContextId

  # Returns information about a target.
  experimental command getTargetInfo
    parameters
      optional TargetID targetId
    returns
      TargetInfo targetInfo

  # Retrieves a list of available targets.
  command getTargets
    parameters
      # Only targets matching filter will be reported. If filter is not specified
      # and target discovery is currently enabled, a filter used for target discovery
      # is used for consistency.
      experimental optional TargetFilter filter
    returns
      # The list of targets.
      array of TargetInfo targetInfos

  # Sends protocol message over session with given id.
  # Consider using flat mode instead; see commands attachToTarget, setAutoAttach,
  # and crbug.com/991325.
  deprecated command sendMessageToTarget
    parameters
      string message
      # Identifier of the session.
      optional SessionID sessionId
      # Deprecated.
      deprecated optional TargetID targetId

  # Controls whether to automatically attach to new targets which are considered to be related to
  # this one. When turned on, attaches to all existing related targets as well. When turned off,
  # automatically detaches from all currently attached targets.
  # This also clears all targets added by `autoAttachRelated` from the list of targets to watch
  # for creation of related targets.
  experimental command setAutoAttach
    parameters
      # Whether to auto-attach to related targets.
      boolean autoAttach
      # Whether to pause new targets when attaching to them. Use `Runtime.runIfWaitingForDebugger`
      # to run paused targets.
      boolean waitForDebuggerOnStart
      # Enables "flat" access to the session via specifying sessionId attribute in the commands.
      # We plan to make this the default, deprecate non-flattened mode,
      # and eventually retire it. See crbug.com/991325.
      optional boolean flatten
      # Only targets matching filter will be attached.
      experimental optional TargetFilter filter

  # Adds the specified target to the list of targets that will be monitored for any related target
  # creation (such as child frames, child workers and new versions of service worker) and reported
  # through `attachedToTarget`. The specified target is also auto-attached.
  # This cancels the effect of any previous `setAutoAttach` and is also cancelled by subsequent
  # `setAutoAttach`. Only available at the Browser target.
  experimental command autoAttachRelated
    parameters
      TargetID targetId
      # Whether to pause new targets when attaching to them. Use `Runtime.runIfWaitingForDebugger`
      # to run paused targets.
      boolean waitForDebuggerOnStart
      # Only targets matching filter will be attached.
      experimental optional TargetFilter filter

  # Controls whether to discover available targets and notify via
  # `targetCreated/targetInfoChanged/targetDestroyed` events.
  command setDiscoverTargets
    parameters
      # Whether to discover available targets.
      boolean discover
      # Only targets matching filter will be attached. If `discover` is false,
      # `filter` must be omitted or empty.
      experimental optional TargetFilter filter

  # Enables target discovery for the specified locations, when `setDiscoverTargets` was set to
  # `true`.
  experimental command setRemoteLocations
    parameters
      # List of remote locations.
      array of RemoteLocation locations

  # Issued when attached to target because of auto-attach or `attachToTarget` command.
  experimental event attachedToTarget
    parameters
      # Identifier assigned to the session used to send/receive messages.
      SessionID sessionId
      TargetInfo targetInfo
      boolean waitingForDebugger

  # Issued when detached from target for any reason (including `detachFromTarget` command). Can be
  # issued multiple times per target if multiple sessions have been attached to it.
  experimental event detachedFromTarget
    parameters
      # Detached session identifier.
      SessionID sessionId
      # Deprecated.
      deprecated optional TargetID targetId

  # Notifies about a new protocol message received from the session (as reported in
  # `attachedToTarget` event).
  event receivedMessageFromTarget
    parameters
      # Identifier of a session which sends a message.
      SessionID sessionId
      string message
      # Deprecated.
      deprecated optional TargetID targetId

  # Issued when a possible inspection target is created.
  event targetCreated
    parameters
      TargetInfo targetInfo

  # Issued when a target is destroyed.
  event targetDestroyed
    parameters
      TargetID targetId

  # Issued when a target has crashed.
  event targetCrashed
    parameters
      TargetID targetId
      # Termination status type.
      string status
      # Termination error code.
      integer errorCode

  # Issued when some information about a target has changed. This only happens between
  # `targetCreated` and `targetDestroyed`.
  event targetInfoChanged
    parameters
      TargetInfo targetInfo

# The Tethering domain defines methods and events for browser port binding.
experimental domain Tethering

  # Request browser port binding.
  command bind
    parameters
      # Port number to bind.
      integer port

  # Request browser port unbinding.
  command unbind
    parameters
      # Port number to unbind.
      integer port

  # Informs that port was successfully bound and got a specified connection id.
  event accepted
    parameters
      # Port number that was successfully bound.
      integer port
      # Connection id to be used.
      string connectionId

experimental domain Tracing
  depends on IO

  # Configuration for memory dump. Used only when "memory-infra" category is enabled.
  type MemoryDumpConfig extends object

  type TraceConfig extends object
    properties
      # Controls how the trace buffer stores data.
      optional enum recordMode
        recordUntilFull
        recordContinuously
        recordAsMuchAsPossible
        echoToConsole
      # Size of the trace buffer in kilobytes. If not specified or zero is passed, a default value
      # of 200 MB would be used.
      optional number traceBufferSizeInKb
      # Turns on JavaScript stack sampling.
      optional boolean enableSampling
      # Turns on system tracing.
      optional boolean enableSystrace
      # Turns on argument filter.
      optional boolean enableArgumentFilter
      # Included category filters.
      optional array of string includedCategories
      # Excluded category filters.
      optional array of string excludedCategories
      # Configuration to synthesize the delays in tracing.
      optional array of string syntheticDelays
      # Configuration for memory dump triggers. Used only when "memory-infra" category is enabled.
      optional MemoryDumpConfig memoryDumpConfig

  # Data format of a trace. Can be either the legacy JSON format or the
  # protocol buffer format. Note that the JSON format will be deprecated soon.
  type StreamFormat extends string
    enum
      json
      proto

  # Compression type to use for traces returned via streams.
  type StreamCompression extends string
    enum
      none
      gzip

  # Details exposed when memory request explicitly declared.
  # Keep consistent with memory_dump_request_args.h and
  # memory_instrumentation.mojom
  type MemoryDumpLevelOfDetail extends string
    enum
      background
      light
      detailed

  # Backend type to use for tracing. `chrome` uses the Chrome-integrated
  # tracing service and is supported on all platforms. `system` is only
  # supported on Chrome OS and uses the Perfetto system tracing service.
  # `auto` chooses `system` when the perfettoConfig provided to Tracing.start
  # specifies at least one non-Chrome data source; otherwise uses `chrome`.
  type TracingBackend extends string
    enum
      auto
      chrome
      system

  # Stop trace events collection.
  command end

  # Gets supported tracing categories.
  command getCategories
    returns
      # A list of supported tracing categories.
      array of string categories

  # Record a clock sync marker in the trace.
  command recordClockSyncMarker
    parameters
      # The ID of this clock sync marker
      string syncId

  # Request a global memory dump.
  command requestMemoryDump
    parameters
      # Enables more deterministic results by forcing garbage collection
      optional boolean deterministic
      # Specifies level of details in memory dump. Defaults to "detailed".
      optional MemoryDumpLevelOfDetail levelOfDetail
    returns
      # GUID of the resulting global memory dump.
      string dumpGuid
      # True iff the global memory dump succeeded.
      boolean success

  # Start trace events collection.
  command start
    parameters
      # Category/tag filter
      deprecated optional string categories
      # Tracing options
      deprecated optional string options
      # If set, the agent will issue bufferUsage events at this interval, specified in milliseconds
      optional number bufferUsageReportingInterval
      # Whether to report trace events as series of dataCollected events or to save trace to a
      # stream (defaults to `ReportEvents`).
      optional enum transferMode
        ReportEvents
        ReturnAsStream
      # Trace data format to use. This only applies when using `ReturnAsStream`
      # transfer mode (defaults to `json`).
      optional StreamFormat streamFormat
      # Compression format to use. This only applies when using `ReturnAsStream`
      # transfer mode (defaults to `none`)
      optional StreamCompression streamCompression
      optional TraceConfig traceConfig
      # Base64-encoded serialized perfetto.protos.TraceConfig protobuf message
      # When specified, the parameters `categories`, `options`, `traceConfig`
      # are ignored.
      optional binary perfettoConfig
      # Backend type (defaults to `auto`)
      optional TracingBackend tracingBackend

  event bufferUsage
    parameters
      # A number in range [0..1] that indicates the used size of event buffer as a fraction of its
      # total size.
      optional number percentFull
      # An approximate number of events in the trace log.
      optional number eventCount
      # A number in range [0..1] that indicates the used size of event buffer as a fraction of its
      # total size.
      optional number value

  # Contains a bucket of collected trace events. When tracing is stopped collected events will be
  # sent as a sequence of dataCollected events followed by tracingComplete event.
  event dataCollected
    parameters
      array of object value

  # Signals that tracing is stopped and there is no trace buffers pending flush, all data were
  # delivered via dataCollected events.
  event tracingComplete
    parameters
      # Indicates whether some trace data is known to have been lost, e.g. because the trace ring
      # buffer wrapped around.
      boolean dataLossOccurred
      # A handle of the stream that holds resulting trace data.
      optional IO.StreamHandle stream
      # Trace data format of returned stream.
      optional StreamFormat traceFormat
      # Compression format of returned stream.
      optional StreamCompression streamCompression

# A domain for letting clients substitute browser's network layer with client code.
domain Fetch
  depends on Network
  depends on IO
  depends on Page

  # Unique request identifier.
  type RequestId extends string

  # Stages of the request to handle. Request will intercept before the request is
  # sent. Response will intercept after the response is received (but before response
  # body is received).
  type RequestStage extends string
    enum
      Request
      Response

  type RequestPattern extends object
    properties
      # Wildcards (`'*'` -> zero or more, `'?'` -> exactly one) are allowed. Escape character is
      # backslash. Omitting is equivalent to `"*"`.
      optional string urlPattern
      # If set, only requests for matching resource types will be intercepted.
      optional Network.ResourceType resourceType
      # Stage at which to begin intercepting requests. Default is Request.
      optional RequestStage requestStage

  # Response HTTP header entry
  type HeaderEntry extends object
    properties
      string name
      string value

  # Authorization challenge for HTTP status code 401 or 407.
  type AuthChallenge extends object
    properties
      # Source of the authentication challenge.
      optional enum source
        Server
        Proxy
      # Origin of the challenger.
      string origin
      # The authentication scheme used, such as basic or digest
      string scheme
      # The realm of the challenge. May be empty.
      string realm

  # Response to an AuthChallenge.
  type AuthChallengeResponse extends object
    properties
      # The decision on what to do in response to the authorization challenge.  Default means
      # deferring to the default behavior of the net stack, which will likely either the Cancel
      # authentication or display a popup dialog box.
      enum response
        Default
        CancelAuth
        ProvideCredentials
      # The username to provide, possibly empty. Should only be set if response is
      # ProvideCredentials.
      optional string username
      # The password to provide, possibly empty. Should only be set if response is
      # ProvideCredentials.
      optional string password

  # Disables the fetch domain.
  command disable

  # Enables issuing of requestPaused events. A request will be paused until client
  # calls one of failRequest, fulfillRequest or continueRequest/continueWithAuth.
  command enable
    parameters
      # If specified, only requests matching any of these patterns will produce
      # fetchRequested event and will be paused until clients response. If not set,
      # all requests will be affected.
      optional array of RequestPattern patterns
      # If true, authRequired events will be issued and requests will be paused
      # expecting a call to continueWithAuth.
      optional boolean handleAuthRequests

  # Causes the request to fail with specified reason.
  command failRequest
    parameters
      # An id the client received in requestPaused event.
      RequestId requestId
      # Causes the request to fail with the given reason.
      Network.ErrorReason errorReason

  # Provides response to the request.
  command fulfillRequest
    parameters
      # An id the client received in requestPaused event.
      RequestId requestId
      # An HTTP response code.
      integer responseCode
      # Response headers.
      optional array of HeaderEntry responseHeaders
      # Alternative way of specifying response headers as a \0-separated
      # series of name: value pairs. Prefer the above method unless you
      # need to represent some non-UTF8 values that can't be transmitted
      # over the protocol as text.
      optional binary binaryResponseHeaders
      # A response body. If absent, original response body will be used if
      # the request is intercepted at the response stage and empty body
      # will be used if the request is intercepted at the request stage.
      optional binary body
      # A textual representation of responseCode.
      # If absent, a standard phrase matching responseCode is used.
      optional string responsePhrase

  # Continues the request, optionally modifying some of its parameters.
  command continueRequest
    parameters
      # An id the client received in requestPaused event.
      RequestId requestId
      # If set, the request url will be modified in a way that's not observable by page.
      optional string url
      # If set, the request method is overridden.
      optional string method
      # If set, overrides the post data in the request.
      optional binary postData
      # If set, overrides the request headers. Note that the overrides do not
      # extend to subsequent redirect hops, if a redirect happens. Another override
      # may be applied to a different request produced by a redirect.
      optional array of HeaderEntry headers
      # If set, overrides response interception behavior for this request.
      experimental optional boolean interceptResponse

  # Continues a request supplying authChallengeResponse following authRequired event.
  command continueWithAuth
    parameters
      # An id the client received in authRequired event.
      RequestId requestId
      # Response to  with an authChallenge.
      AuthChallengeResponse authChallengeResponse

  # Continues loading of the paused response, optionally modifying the
  # response headers. If either responseCode or headers are modified, all of them
  # must be present.
  experimental command continueResponse
    parameters
      # An id the client received in requestPaused event.
      RequestId requestId
      # An HTTP response code. If absent, original response code will be used.
      optional integer responseCode
      # A textual representation of responseCode.
      # If absent, a standard phrase matching responseCode is used.
      optional string responsePhrase
      # Response headers. If absent, original response headers will be used.
      optional array of HeaderEntry responseHeaders
      # Alternative way of specifying response headers as a \0-separated
      # series of name: value pairs. Prefer the above method unless you
      # need to represent some non-UTF8 values that can't be transmitted
      # over the protocol as text.
      optional binary binaryResponseHeaders

  # Causes the body of the response to be received from the server and
  # returned as a single string. May only be issued for a request that
  # is paused in the Response stage and is mutually exclusive with
  # takeResponseBodyForInterceptionAsStream. Calling other methods that
  # affect the request or disabling fetch domain before body is received
  # results in an undefined behavior.
  # Note that the response body is not available for redirects. Requests
  # paused in the _redirect received_ state may be differentiated by
  # `responseCode` and presence of `location` response header, see
  # comments to `requestPaused` for details.
  command getResponseBody
    parameters
      # Identifier for the intercepted request to get body for.
      RequestId requestId
    returns
      # Response body.
      string body
      # True, if content was sent as base64.
      boolean base64Encoded

  # Returns a handle to the stream representing the response body.
  # The request must be paused in the HeadersReceived stage.
  # Note that after this command the request can't be continued
  # as is -- client either needs to cancel it or to provide the
  # response body.
  # The stream only supports sequential read, IO.read will fail if the position
  # is specified.
  # This method is mutually exclusive with getResponseBody.
  # Calling other methods that affect the request or disabling fetch
  # domain before body is received results in an undefined behavior.
  command takeResponseBodyAsStream
    parameters
      RequestId requestId
    returns
      IO.StreamHandle stream

  # Issued when the domain is enabled and the request URL matches the
  # specified filter. The request is paused until the client responds
  # with one of continueRequest, failRequest or fulfillRequest.
  # The stage of the request can be determined by presence of responseErrorReason
  # and responseStatusCode -- the request is at the response stage if either
  # of these fields is present and in the request stage otherwise.
  # Redirect responses and subsequent requests are reported similarly to regular
  # responses and requests. Redirect responses may be distinguished by the value
  # of `responseStatusCode` (which is one of 301, 302, 303, 307, 308) along with
  # presence of the `location` header. Requests resulting from a redirect will
  # have `redirectedRequestId` field set.
  event requestPaused
    parameters
      # Each request the page makes will have a unique id.
      RequestId requestId
      # The details of the request.
      Network.Request request
      # The id of the frame that initiated the request.
      Page.FrameId frameId
      # How the requested resource will be used.
      Network.ResourceType resourceType
      # Response error if intercepted at response stage.
      optional Network.ErrorReason responseErrorReason
      # Response code if intercepted at response stage.
      optional integer responseStatusCode
      # Response status text if intercepted at response stage.
      optional string responseStatusText
      # Response headers if intercepted at the response stage.
      optional array of HeaderEntry responseHeaders
      # If the intercepted request had a corresponding Network.requestWillBeSent event fired for it,
      # then this networkId will be the same as the requestId present in the requestWillBeSent event.
      optional Network.RequestId networkId
      # If the request is due to a redirect response from the server, the id of the request that
      # has caused the redirect.
      experimental optional RequestId redirectedRequestId

  # Issued when the domain is enabled with handleAuthRequests set to true.
  # The request is paused until client responds with continueWithAuth.
  event authRequired
    parameters
      # Each request the page makes will have a unique id.
      RequestId requestId
      # The details of the request.
      Network.Request request
      # The id of the frame that initiated the request.
      Page.FrameId frameId
      # How the requested resource will be used.
      Network.ResourceType resourceType
      # Details of the Authorization Challenge encountered.
      # If this is set, client should respond with continueRequest that
      # contains AuthChallengeResponse.
      AuthChallenge authChallenge

# This domain allows inspection of Web Audio API.
# https://webaudio.github.io/web-audio-api/
experimental domain WebAudio

  # An unique ID for a graph object (AudioContext, AudioNode, AudioParam) in Web Audio API
  type GraphObjectId extends string

  # Enum of BaseAudioContext types
  type ContextType extends string
    enum
      realtime
      offline

  # Enum of AudioContextState from the spec
  type ContextState extends string
    enum
      suspended
      running
      closed

  # Enum of AudioNode types
  type NodeType extends string

  # Enum of AudioNode::ChannelCountMode from the spec
  type ChannelCountMode extends string
    enum
      clamped-max
      explicit
      max

  # Enum of AudioNode::ChannelInterpretation from the spec
  type ChannelInterpretation extends string
    enum
      discrete
      speakers

  # Enum of AudioParam types
  type ParamType extends string

  # Enum of AudioParam::AutomationRate from the spec
  type AutomationRate extends string
    enum
      a-rate
      k-rate

  # Fields in AudioContext that change in real-time.
  type ContextRealtimeData extends object
    properties
      # The current context time in second in BaseAudioContext.
      number currentTime
      # The time spent on rendering graph divided by render quantum duration,
      # and multiplied by 100. 100 means the audio renderer reached the full
      # capacity and glitch may occur.
      number renderCapacity
      # A running mean of callback interval.
      number callbackIntervalMean
      # A running variance of callback interval.
      number callbackIntervalVariance

  # Protocol object for BaseAudioContext
  type BaseAudioContext extends object
    properties
      GraphObjectId contextId
      ContextType contextType
      ContextState contextState
      optional ContextRealtimeData realtimeData
      # Platform-dependent callback buffer size.
      number callbackBufferSize
      # Number of output channels supported by audio hardware in use.
      number maxOutputChannelCount
      # Context sample rate.
      number sampleRate

# Protocol object for AudioListener
  type AudioListener extends object
    properties
      GraphObjectId listenerId
      GraphObjectId contextId

  # Protocol object for AudioNode
  type AudioNode extends object
    properties
      GraphObjectId nodeId
      GraphObjectId contextId
      NodeType nodeType
      number numberOfInputs
      number numberOfOutputs
      number channelCount
      ChannelCountMode channelCountMode
      ChannelInterpretation channelInterpretation

  # Protocol object for AudioParam
  type AudioParam extends object
    properties
      GraphObjectId paramId
      GraphObjectId nodeId
      GraphObjectId contextId
      ParamType paramType
      AutomationRate rate
      number defaultValue
      number minValue
      number maxValue

  # Enables the WebAudio domain and starts sending context lifetime events.
  command enable

  # Disables the WebAudio domain.
  command disable

  # Fetch the realtime data from the registered contexts.
  command getRealtimeData
    parameters
      GraphObjectId contextId
    returns
      ContextRealtimeData realtimeData

  # Notifies that a new BaseAudioContext has been created.
  event contextCreated
    parameters
      BaseAudioContext context

  # Notifies that an existing BaseAudioContext will be destroyed.
  event contextWillBeDestroyed
    parameters
      GraphObjectId contextId

  # Notifies that existing BaseAudioContext has changed some properties (id stays the same)..
  event contextChanged
    parameters
      BaseAudioContext context

# Notifies that the construction of an AudioListener has finished.
  event audioListenerCreated
    parameters
      AudioListener listener

  # Notifies that a new AudioListener has been created.
  event audioListenerWillBeDestroyed
    parameters
      GraphObjectId contextId
      GraphObjectId listenerId

  # Notifies that a new AudioNode has been created.
  event audioNodeCreated
    parameters
      AudioNode node

  # Notifies that an existing AudioNode has been destroyed.
  event audioNodeWillBeDestroyed
    parameters
      GraphObjectId contextId
      GraphObjectId nodeId

  # Notifies that a new AudioParam has been created.
  event audioParamCreated
    parameters
      AudioParam param

  # Notifies that an existing AudioParam has been destroyed.
  event audioParamWillBeDestroyed
    parameters
      GraphObjectId contextId
      GraphObjectId nodeId
      GraphObjectId paramId

  # Notifies that two AudioNodes are connected.
  event nodesConnected
    parameters
      GraphObjectId contextId
      GraphObjectId sourceId
      GraphObjectId destinationId
      optional number sourceOutputIndex
      optional number destinationInputIndex

  # Notifies that AudioNodes are disconnected. The destination can be null, and it means all the outgoing connections from the source are disconnected.
  event nodesDisconnected
    parameters
      GraphObjectId contextId
      GraphObjectId sourceId
      GraphObjectId destinationId
      optional number sourceOutputIndex
      optional number destinationInputIndex

  # Notifies that an AudioNode is connected to an AudioParam.
  event nodeParamConnected
    parameters
      GraphObjectId contextId
      GraphObjectId sourceId
      GraphObjectId destinationId
      optional number sourceOutputIndex

  # Notifies that an AudioNode is disconnected to an AudioParam.
  event nodeParamDisconnected
    parameters
      GraphObjectId contextId
      GraphObjectId sourceId
      GraphObjectId destinationId
      optional number sourceOutputIndex

# This domain allows configuring virtual authenticators to test the WebAuthn
# API.
experimental domain WebAuthn
  type AuthenticatorId extends string

  type AuthenticatorProtocol extends string
    enum
      # Universal 2nd Factor.
      u2f
      # Client To Authenticator Protocol 2.
      ctap2

  type Ctap2Version extends string
    enum
      ctap2_0
      ctap2_1

  type AuthenticatorTransport extends string
    enum
      # Cross-Platform authenticator attachments:
      usb
      nfc
      ble
      cable
      # Platform authenticator attachment:
      internal

  type VirtualAuthenticatorOptions extends object
    properties
      AuthenticatorProtocol protocol
      # Defaults to ctap2_0. Ignored if |protocol| == u2f.
      optional Ctap2Version ctap2Version
      AuthenticatorTransport transport
      # Defaults to false.
      optional boolean hasResidentKey
      # Defaults to false.
      optional boolean hasUserVerification
      # If set to true, the authenticator will support the largeBlob extension.
      # https://w3c.github.io/webauthn#largeBlob
      # Defaults to false.
      optional boolean hasLargeBlob
      # If set to true, the authenticator will support the credBlob extension.
      # https://fidoalliance.org/specs/fido-v2.1-rd-20201208/fido-client-to-authenticator-protocol-v2.1-rd-20201208.html#sctn-credBlob-extension
      # Defaults to false.
      optional boolean hasCredBlob
      # If set to true, the authenticator will support the minPinLength extension.
      # https://fidoalliance.org/specs/fido-v2.1-ps-20210615/fido-client-to-authenticator-protocol-v2.1-ps-20210615.html#sctn-minpinlength-extension
      # Defaults to false.
      optional boolean hasMinPinLength
      # If set to true, the authenticator will support the prf extension.
      # https://w3c.github.io/webauthn/#prf-extension
      # Defaults to false.
      optional boolean hasPrf
      # If set to true, tests of user presence will succeed immediately.
      # Otherwise, they will not be resolved. Defaults to true.
      optional boolean automaticPresenceSimulation
      # Sets whether User Verification succeeds or fails for an authenticator.
      # Defaults to false.
      optional boolean isUserVerified
      # Credentials created by this authenticator will have the backup
      # eligibility (BE) flag set to this value. Defaults to false.
      # https://w3c.github.io/webauthn/#sctn-credential-backup
      optional boolean defaultBackupEligibility
      # Credentials created by this authenticator will have the backup state
      # (BS) flag set to this value. Defaults to false.
      # https://w3c.github.io/webauthn/#sctn-credential-backup
      optional boolean defaultBackupState

  type Credential extends object
    properties
      binary credentialId
      boolean isResidentCredential
      # Relying Party ID the credential is scoped to. Must be set when adding a
      # credential.
      optional string rpId
      # The ECDSA P-256 private key in PKCS#8 format.
      binary privateKey
      # An opaque byte sequence with a maximum size of 64 bytes mapping the
      # credential to a specific user.
      optional binary userHandle
      # Signature counter. This is incremented by one for each successful
      # assertion.
      # See https://w3c.github.io/webauthn/#signature-counter
      integer signCount
      # The large blob associated with the credential.
      # See https://w3c.github.io/webauthn/#sctn-large-blob-extension
      optional binary largeBlob

  # Enable the WebAuthn domain and start intercepting credential storage and
  # retrieval with a virtual authenticator.
  command enable
    parameters
      # Whether to enable the WebAuthn user interface. Enabling the UI is
      # recommended for debugging and demo purposes, as it is closer to the real
      # experience. Disabling the UI is recommended for automated testing.
      # Supported at the embedder's discretion if UI is available.
      # Defaults to false.
      optional boolean enableUI

  # Disable the WebAuthn domain.
  command disable

  # Creates and adds a virtual authenticator.
  command addVirtualAuthenticator
    parameters
      VirtualAuthenticatorOptions options
    returns
      AuthenticatorId authenticatorId

  # Resets parameters isBogusSignature, isBadUV, isBadUP to false if they are not present.
  command setResponseOverrideBits
    parameters
      AuthenticatorId authenticatorId
      # If isBogusSignature is set, overrides the signature in the authenticator response to be zero.
      # Defaults to false.
      optional boolean isBogusSignature
      # If isBadUV is set, overrides the UV bit in the flags in the authenticator response to
      # be zero. Defaults to false.
      optional boolean isBadUV
      # If isBadUP is set, overrides the UP bit in the flags in the authenticator response to
      # be zero. Defaults to false.
      optional boolean isBadUP

  # Removes the given authenticator.
  command removeVirtualAuthenticator
    parameters
      AuthenticatorId authenticatorId

  # Adds the credential to the specified authenticator.
  command addCredential
    parameters
      AuthenticatorId authenticatorId
      Credential credential

  # Returns a single credential stored in the given virtual authenticator that
  # matches the credential ID.
  command getCredential
    parameters
      AuthenticatorId authenticatorId
      binary credentialId
    returns
      Credential credential

  # Returns all the credentials stored in the given virtual authenticator.
  command getCredentials
    parameters
      AuthenticatorId authenticatorId
    returns
      array of Credential credentials

  # Removes a credential from the authenticator.
  command removeCredential
    parameters
      AuthenticatorId authenticatorId
      binary credentialId

  # Clears all the credentials from the specified device.
  command clearCredentials
    parameters
      AuthenticatorId authenticatorId

  # Sets whether User Verification succeeds or fails for an authenticator.
  # The default is true.
  command setUserVerified
    parameters
      AuthenticatorId authenticatorId
      boolean isUserVerified

  # Sets whether tests of user presence will succeed immediately (if true) or fail to resolve (if false) for an authenticator.
  # The default is true.
  command setAutomaticPresenceSimulation
    parameters
      AuthenticatorId authenticatorId
      boolean enabled

  # Triggered when a credential is added to an authenticator.
  event credentialAdded
    parameters
      AuthenticatorId authenticatorId
      Credential credential

  # Triggered when a credential is used in a webauthn assertion.
  event credentialAsserted
    parameters
      AuthenticatorId authenticatorId
      Credential credential

# This domain allows detailed inspection of media elements
experimental domain Media

  # Players will get an ID that is unique within the agent context.
  type PlayerId extends string

  type Timestamp extends number

  # Have one type per entry in MediaLogRecord::Type
  # Corresponds to kMessage
  type PlayerMessage extends object
    properties
      # Keep in sync with MediaLogMessageLevel
      # We are currently keeping the message level 'error' separate from the
      # PlayerError type because right now they represent different things,
      # this one being a DVLOG(ERROR) style log message that gets printed
      # based on what log level is selected in the UI, and the other is a
      # representation of a media::PipelineStatus object. Soon however we're
      # going to be moving away from using PipelineStatus for errors and
      # introducing a new error type which should hopefully let us integrate
      # the error log level into the PlayerError type.
      enum level
        error
        warning
        info
        debug
      string message

  # Corresponds to kMediaPropertyChange
  type PlayerProperty extends object
    properties
      string name
      string value

  # Corresponds to kMediaEventTriggered
  type PlayerEvent extends object
    properties
      Timestamp timestamp
      string value

  # Represents logged source line numbers reported in an error.
  # NOTE: file and line are from chromium c++ implementation code, not js.
  type PlayerErrorSourceLocation extends object
    properties
      string file
      integer line

  # Corresponds to kMediaError
  type PlayerError extends object
    properties
      string errorType
      # Code is the numeric enum entry for a specific set of error codes, such
      # as PipelineStatusCodes in media/base/pipeline_status.h
      integer code
      # A trace of where this error was caused / where it passed through.
      array of PlayerErrorSourceLocation stack
      # Errors potentially have a root cause error, ie, a DecoderError might be
      # caused by an WindowsError
      array of PlayerError cause
      # Extra data attached to an error, such as an HRESULT, Video Codec, etc.
      object data

  # This can be called multiple times, and can be used to set / override /
  # remove player properties. A null propValue indicates removal.
  event playerPropertiesChanged
    parameters
      PlayerId playerId
      array of PlayerProperty properties

  # Send events as a list, allowing them to be batched on the browser for less
  # congestion. If batched, events must ALWAYS be in chronological order.
  event playerEventsAdded
    parameters
      PlayerId playerId
      array of PlayerEvent events

  # Send a list of any messages that need to be delivered.
  event playerMessagesLogged
    parameters
      PlayerId playerId
      array of PlayerMessage messages

  # Send a list of any errors that need to be delivered.
  event playerErrorsRaised
    parameters
      PlayerId playerId
      array of PlayerError errors

  # Called whenever a player is created, or when a new agent joins and receives
  # a list of active players. If an agent is restored, it will receive the full
  # list of player ids and all events again.
  event playersCreated
    parameters
      array of PlayerId players

  # Enables the Media domain
  command enable

  # Disables the Media domain.
  command disable

experimental domain DeviceAccess
  # Device request id.
  type RequestId extends string

  # A device id.
  type DeviceId extends string

  # Device information displayed in a user prompt to select a device.
  type PromptDevice extends object
    properties
      DeviceId id
      # Display name as it appears in a device request user prompt.
      string name

  # Enable events in this domain.
  command enable

  # Disable events in this domain.
  command disable

  # Select a device in response to a DeviceAccess.deviceRequestPrompted event.
  command selectPrompt
    parameters
      RequestId id
      DeviceId deviceId

  # Cancel a prompt in response to a DeviceAccess.deviceRequestPrompted event.
  command cancelPrompt
    parameters
      RequestId id

  # A device request opened a user prompt to select a device. Respond with the
  # selectPrompt or cancelPrompt command.
  event deviceRequestPrompted
    parameters
      RequestId id
      array of PromptDevice devices

experimental domain Preload
  # Unique id
  type RuleSetId extends string

  # Corresponds to SpeculationRuleSet
  type RuleSet extends object
    properties
      RuleSetId id
      # Identifies a document which the rule set is associated with.
      Network.LoaderId loaderId
      # Source text of JSON representing the rule set. If it comes from
      # `<script>` tag, it is the textContent of the node. Note that it is
      # a JSON for valid case.
      #
      # See also:
      # - https://wicg.github.io/nav-speculation/speculation-rules.html
      # - https://github.com/WICG/nav-speculation/blob/main/triggers.md
      string sourceText
      # A speculation rule set is either added through an inline
      # `<script>` tag or through an external resource via the
      # 'Speculation-Rules' HTTP header. For the first case, we include
      # the BackendNodeId of the relevant `<script>` tag. For the second
      # case, we include the external URL where the rule set was loaded
      # from, and also RequestId if Network domain is enabled.
      #
      # See also:
      # - https://wicg.github.io/nav-speculation/speculation-rules.html#speculation-rules-script
      # - https://wicg.github.io/nav-speculation/speculation-rules.html#speculation-rules-header
      optional DOM.BackendNodeId backendNodeId
      optional string url
      optional Network.RequestId requestId
      # Error information
      # `errorMessage` is null iff `errorType` is null.
      optional RuleSetErrorType errorType
      # TODO(https://crbug.com/1425354): Replace this property with structured error.
      deprecated optional string errorMessage

  type RuleSetErrorType extends string
    enum
      SourceIsNotJsonObject
      InvalidRulesSkipped

  # The type of preloading attempted. It corresponds to
  # mojom::SpeculationAction (although PrefetchWithSubresources is omitted as it
  # isn't being used by clients).
  type SpeculationAction extends string
    enum
      Prefetch
      Prerender

  # Corresponds to mojom::SpeculationTargetHint.
  # See https://github.com/WICG/nav-speculation/blob/main/triggers.md#window-name-targeting-hints
  type SpeculationTargetHint extends string
    enum
      Blank
      Self

  # A key that identifies a preloading attempt.
  #
  # The url used is the url specified by the trigger (i.e. the initial URL), and
  # not the final url that is navigated to. For example, prerendering allows
  # same-origin main frame navigations during the attempt, but the attempt is
  # still keyed with the initial URL.
  type PreloadingAttemptKey extends object
    properties
      Network.LoaderId loaderId
      SpeculationAction action
      string url
      optional SpeculationTargetHint targetHint

  # Lists sources for a preloading attempt, specifically the ids of rule sets
  # that had a speculation rule that triggered the attempt, and the
  # BackendNodeIds of <a href> or <area href> elements that triggered the
  # attempt (in the case of attempts triggered by a document rule). It is
  # possible for mulitple rule sets and links to trigger a single attempt.
  type PreloadingAttemptSource extends object
    properties
      PreloadingAttemptKey key
      array of RuleSetId ruleSetIds
      array of DOM.BackendNodeId nodeIds

  command enable

  command disable

  # Upsert. Currently, it is only emitted when a rule set added.
  event ruleSetUpdated
    parameters
      RuleSet ruleSet

  event ruleSetRemoved
    parameters
      RuleSetId id

  # List of FinalStatus reasons for Prerender2.
  type PrerenderFinalStatus extends string
    enum
      Activated
      Destroyed
      LowEndDevice
      InvalidSchemeRedirect
      InvalidSchemeNavigation
      NavigationRequestBlockedByCsp
      MainFrameNavigation
      MojoBinderPolicy
      RendererProcessCrashed
      RendererProcessKilled
      Download
      TriggerDestroyed
      NavigationNotCommitted
      NavigationBadHttpStatus
      ClientCertRequested
      NavigationRequestNetworkError
      CancelAllHostsForTesting
      DidFailLoad
      Stop
      SslCertificateError
      LoginAuthRequested
      UaChangeRequiresReload
      BlockedByClient
      AudioOutputDeviceRequested
      MixedContent
      TriggerBackgrounded
      MemoryLimitExceeded
      DataSaverEnabled
      TriggerUrlHasEffectiveUrl
      ActivatedBeforeStarted
      InactivePageRestriction
      StartFailed
      TimeoutBackgrounded
      CrossSiteRedirectInInitialNavigation
      CrossSiteNavigationInInitialNavigation
      SameSiteCrossOriginRedirectNotOptInInInitialNavigation
      SameSiteCrossOriginNavigationNotOptInInInitialNavigation
      ActivationNavigationParameterMismatch
      ActivatedInBackground
      EmbedderHostDisallowed
      ActivationNavigationDestroyedBeforeSuccess
      TabClosedByUserGesture
      TabClosedWithoutUserGesture
      PrimaryMainFrameRendererProcessCrashed
      PrimaryMainFrameRendererProcessKilled
      ActivationFramePolicyNotCompatible
      PreloadingDisabled
      BatterySaverEnabled
      ActivatedDuringMainFrameNavigation
      PreloadingUnsupportedByWebContents
      CrossSiteRedirectInMainFrameNavigation
      CrossSiteNavigationInMainFrameNavigation
      SameSiteCrossOriginRedirectNotOptInInMainFrameNavigation
      SameSiteCrossOriginNavigationNotOptInInMainFrameNavigation
      MemoryPressureOnTrigger
      MemoryPressureAfterTriggered
      PrerenderingDisabledByDevTools
      SpeculationRuleRemoved
      ActivatedWithAuxiliaryBrowsingContexts
      MaxNumOfRunningEagerPrerendersExceeded
      MaxNumOfRunningNonEagerPrerendersExceeded
      MaxNumOfRunningEmbedderPrerendersExceeded
      PrerenderingUrlHasEffectiveUrl
      RedirectedPrerenderingUrlHasEffectiveUrl
      ActivationUrlHasEffectiveUrl

  # Fired when a preload enabled state is updated.
  event preloadEnabledStateUpdated
    parameters
      boolean disabledByPreference
      boolean disabledByDataSaver
      boolean disabledByBatterySaver
      boolean disabledByHoldbackPrefetchSpeculationRules
      boolean disabledByHoldbackPrerenderSpeculationRules

  # Preloading status values, see also PreloadingTriggeringOutcome. This
  # status is shared by prefetchStatusUpdated and prerenderStatusUpdated.
  type PreloadingStatus extends string
    enum
      Pending
      Running
      Ready
      Success
      Failure
      # PreloadingTriggeringOutcome which not used by prefetch nor prerender.
      NotSupported

  # TODO(https://crbug.com/1384419): revisit the list of PrefetchStatus and
  # filter out the ones that aren't necessary to the developers.
  type PrefetchStatus extends string
    enum
      # Prefetch is not disabled by PrefetchHeldback.
      PrefetchAllowed
      PrefetchFailedIneligibleRedirect
      PrefetchFailedInvalidRedirect
      PrefetchFailedMIMENotSupported
      PrefetchFailedNetError
      PrefetchFailedNon2XX
      PrefetchFailedPerPageLimitExceeded
      PrefetchEvicted
      PrefetchHeldback
      # A previous prefetch to the origin got a HTTP 503 response with an
      # Retry-After header that has no elapsed yet.
      PrefetchIneligibleRetryAfter
      PrefetchIsPrivacyDecoy
      PrefetchIsStale
      PrefetchNotEligibleBrowserContextOffTheRecord
      PrefetchNotEligibleDataSaverEnabled
      PrefetchNotEligibleExistingProxy
      PrefetchNotEligibleHostIsNonUnique
      PrefetchNotEligibleNonDefaultStoragePartition
      PrefetchNotEligibleSameSiteCrossOriginPrefetchRequiredProxy
      PrefetchNotEligibleSchemeIsNotHttps
      PrefetchNotEligibleUserHasCookies
      PrefetchNotEligibleUserHasServiceWorker
      PrefetchNotEligibleBatterySaverEnabled
      PrefetchNotEligiblePreloadingDisabled
      PrefetchNotFinishedInTime
      PrefetchNotStarted
      PrefetchNotUsedCookiesChanged
      PrefetchProxyNotAvailable
      # The response of the prefetch is used for the next navigation. This is
      # the final successful state.
      PrefetchResponseUsed
      # The prefetch finished successfully but was never used.
      PrefetchSuccessfulButNotUsed
      PrefetchNotUsedProbeFailed

  # Fired when a prefetch attempt is updated.
  event prefetchStatusUpdated
    parameters
      PreloadingAttemptKey key
      # The frame id of the frame initiating prefetch.
      Page.FrameId initiatingFrameId
      string prefetchUrl
      PreloadingStatus status
      PrefetchStatus prefetchStatus
      Network.RequestId requestId

  # Information of headers to be displayed when the header mismatch occurred.
  type PrerenderMismatchedHeaders extends object
    properties
      string headerName
      optional string initialValue
      optional string activationValue

  # Fired when a prerender attempt is updated.
  event prerenderStatusUpdated
    parameters
      PreloadingAttemptKey key
      PreloadingStatus status
      optional PrerenderFinalStatus prerenderStatus
      # This is used to give users more information about the name of Mojo interface
      # that is incompatible with prerender and has caused the cancellation of the attempt.
      optional string disallowedMojoInterface
      optional array of PrerenderMismatchedHeaders mismatchedHeaders

  # Send a list of sources for all preloading attempts in a document.
  event preloadingAttemptSourcesUpdated
    parameters
      Network.LoaderId loaderId
      array of PreloadingAttemptSource preloadingAttemptSources

# This domain allows interacting with the FedCM dialog.
experimental domain FedCm
  # Whether this is a sign-up or sign-in action for this account, i.e.
  # whether this account has ever been used to sign in to this RP before.
  type LoginState extends string
    enum
      SignIn
      SignUp

  # The types of FedCM dialogs.
  type DialogType extends string
    enum
      AccountChooser
      AutoReauthn
      ConfirmIdpLogin
      Error

  # The buttons on the FedCM dialog.
  type DialogButton extends string
    enum
      ConfirmIdpLoginContinue
      ErrorGotIt
      ErrorMoreDetails

  # Corresponds to IdentityRequestAccount
  type Account extends object
    properties
      string accountId
      string email
      string name
      string givenName
      string pictureUrl
      string idpConfigUrl
      string idpLoginUrl
      LoginState loginState
      # These two are only set if the loginState is signUp
      optional string termsOfServiceUrl
      optional string privacyPolicyUrl

  event dialogShown
    parameters
      string dialogId
      DialogType dialogType
      array of Account accounts
      # These exist primarily so that the caller can verify the
      # RP context was used appropriately.
      string title
      optional string subtitle

  # Triggered when a dialog is closed, either by user action, JS abort,
  # or a command below.
  event dialogClosed
    parameters
      string dialogId

  command enable
    parameters
      # Allows callers to disable the promise rejection delay that would
      # normally happen, if this is unimportant to what's being tested.
      # (step 4 of https://fedidcg.github.io/FedCM/#browser-api-rp-sign-in)
      optional boolean disableRejectionDelay

  command disable

  command selectAccount
    parameters
      string dialogId
      integer accountIndex

  command clickDialogButton
    parameters
      string dialogId
      DialogButton dialogButton

  command dismissDialog
    parameters
      string dialogId
      optional boolean triggerCooldown

  # Resets the cooldown time, if any, to allow the next FedCM call to show
  # a dialog even if one was recently dismissed by the user.
  command resetCooldown
