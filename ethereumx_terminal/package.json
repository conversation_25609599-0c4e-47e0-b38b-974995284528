{"name": "ethereumx-proxy-server", "version": "1.0.0", "description": "Proxy server to bypass CORS and X-Frame-Options for pump.fun", "main": "proxy-server.js", "scripts": {"start": "node proxy-server.js", "dev": "nodemon proxy-server.js"}, "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "http-proxy-middleware": "^2.0.6", "puppeteer": "^21.11.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["proxy", "cors", "iframe", "pump.fun"], "author": "EthereumX", "license": "MIT"}