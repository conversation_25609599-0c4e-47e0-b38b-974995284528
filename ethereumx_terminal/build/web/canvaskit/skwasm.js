
var skwasm = (() => {
  var _scriptName = typeof document != 'undefined' ? document.currentScript?.src : undefined;
  
  return (
function(moduleArg = {}) {
  var moduleRtn;

function aa(){e.buffer!=h.buffer&&k();return h}function n(){e.buffer!=h.buffer&&k();return ba}function q(){e.buffer!=h.buffer&&k();return ca}function t(){e.buffer!=h.buffer&&k();return da}function u(){e.buffer!=h.buffer&&k();return ea}function fa(){e.buffer!=h.buffer&&k();return ha}var v=moduleArg,ia,ja,ka=new Promise((a,b)=>{ia=a;ja=b}),la="object"==typeof window,ma="function"==typeof importScripts,w=ma&&self.name?.startsWith("em-pthread"),na=Object.assign({},v),x="",oa,pa;
if(la||ma)ma?x=self.location.href:"undefined"!=typeof document&&document.currentScript&&(x=document.currentScript.src),_scriptName&&(x=_scriptName),x.startsWith("blob:")?x="":x=x.substr(0,x.replace(/[?#].*/,"").lastIndexOf("/")+1),ma&&(pa=a=>{var b=new XMLHttpRequest;b.open("GET",a,!1);b.responseType="arraybuffer";b.send(null);return new Uint8Array(b.response)}),oa=a=>fetch(a,{credentials:"same-origin"}).then(b=>b.ok?b.arrayBuffer():Promise.reject(Error(b.status+" : "+b.url)));
var qa=console.log.bind(console),y=console.error.bind(console);Object.assign(v,na);na=null;var e,ra,sa=!1,ta,h,ba,ua,va,ca,da,ea,ha;function k(){var a=e.buffer;h=new Int8Array(a);ua=new Int16Array(a);ba=new Uint8Array(a);va=new Uint16Array(a);ca=new Int32Array(a);da=new Uint32Array(a);ea=new Float32Array(a);ha=new Float64Array(a)}
if(w){var wa,xa=!1;y=function(...b){console.error(b.join(" "))};self.alert=function(...b){postMessage({g:"alert",text:b.join(" "),na:ya()})};v.instantiateWasm=(b,c)=>new Promise(d=>{wa=f=>{f=new WebAssembly.Instance(f,za());c(f);d()}});self.onunhandledrejection=b=>{throw b.reason||b;};function a(b){try{var c=b.data,d=c.g;if("load"===d){let f=[];self.onmessage=g=>f.push(g);self.startWorker=()=>{postMessage({g:"loaded"});for(let g of f)a(g);self.onmessage=a};for(const g of c.ca)if(!v[g]||v[g].proxy)v[g]=
(...l)=>{postMessage({g:"callHandler",ba:g,X:l})},"print"==g&&(qa=v[g]),"printErr"==g&&(y=v[g]);e=c.pa;k();wa(c.qa)}else if("run"===d){Aa(c.l);Ba(c.l,0,0,1,0,0);Ca(c);Da();Ea(c.l);xa||=!0;try{Fa(c.la,c.J)}catch(f){if("unwind"!=f)throw f;}}else"setimmediate"!==c.target&&("checkMailbox"===d?xa&&Ga():d&&(y(`worker: received unknown command ${d}`),y(c)))}catch(f){throw Ha(),f;}}self.onmessage=a}w||(e=new WebAssembly.Memory({initial:256,maximum:32768,shared:!0}),k());
var Ia=[],Ja=[],Ka=[],z=0,La=null,Ma=null;function Na(){z--;if(0==z&&(null!==La&&(clearInterval(La),La=null),Ma)){var a=Ma;Ma=null;a()}}function Oa(a){a="Aborted("+a+")";y(a);sa=!0;a=new WebAssembly.RuntimeError(a+". Build with -sASSERTIONS for more info.");ja(a);throw a;}var Pa=a=>a.startsWith("data:application/octet-stream;base64,"),Qa;function Ra(a){return oa(a).then(b=>new Uint8Array(b),()=>{if(pa)var b=pa(a);else throw"both async and sync fetching of the wasm failed";return b})}
function Sa(a,b,c){return Ra(a).then(d=>WebAssembly.instantiate(d,b)).then(c,d=>{y(`failed to asynchronously prepare wasm: ${d}`);Oa(d)})}function Ta(a,b){var c=Qa;return"function"!=typeof WebAssembly.instantiateStreaming||Pa(c)||"function"!=typeof fetch?Sa(c,a,b):fetch(c,{credentials:"same-origin"}).then(d=>WebAssembly.instantiateStreaming(d,a).then(b,function(f){y(`wasm streaming compile failed: ${f}`);y("falling back to ArrayBuffer instantiation");return Sa(c,a,b)}))}
function za(){Ua={__cxa_throw:Va,__pthread_create_js:Wa,__syscall_fcntl64:Xa,__syscall_fstat64:Ya,__syscall_ioctl:Za,__syscall_openat:$a,_abort_js:ab,_emscripten_get_now_is_monotonic:bb,_emscripten_init_main_thread_js:cb,_emscripten_notify_mailbox_postmessage:db,_emscripten_receive_on_main_thread_js:eb,_emscripten_runtime_keepalive_clear:fb,_emscripten_thread_cleanup:gb,_emscripten_thread_mailbox_await:Ea,_emscripten_thread_set_strongref:hb,_emscripten_throw_longjmp:ib,_mmap_js:jb,_munmap_js:kb,_setitimer_js:lb,
_tzset_js:mb,emscripten_check_blocking_allowed:nb,emscripten_exit_with_live_runtime:ob,emscripten_get_now:pb,emscripten_glActiveTexture:qb,emscripten_glAttachShader:rb,emscripten_glBeginQuery:sb,emscripten_glBeginQueryEXT:tb,emscripten_glBindAttribLocation:ub,emscripten_glBindBuffer:vb,emscripten_glBindFramebuffer:wb,emscripten_glBindRenderbuffer:xb,emscripten_glBindSampler:yb,emscripten_glBindTexture:zb,emscripten_glBindVertexArray:Ab,emscripten_glBindVertexArrayOES:Ab,emscripten_glBlendColor:Bb,
emscripten_glBlendEquation:Cb,emscripten_glBlendFunc:Db,emscripten_glBlitFramebuffer:Eb,emscripten_glBufferData:Fb,emscripten_glBufferSubData:Gb,emscripten_glCheckFramebufferStatus:Hb,emscripten_glClear:Ib,emscripten_glClearColor:Jb,emscripten_glClearStencil:Kb,emscripten_glClientWaitSync:Lb,emscripten_glColorMask:Mb,emscripten_glCompileShader:Nb,emscripten_glCompressedTexImage2D:Ob,emscripten_glCompressedTexSubImage2D:Pb,emscripten_glCopyBufferSubData:Qb,emscripten_glCopyTexSubImage2D:Rb,emscripten_glCreateProgram:Sb,
emscripten_glCreateShader:Tb,emscripten_glCullFace:Ub,emscripten_glDeleteBuffers:Vb,emscripten_glDeleteFramebuffers:Wb,emscripten_glDeleteProgram:Xb,emscripten_glDeleteQueries:Yb,emscripten_glDeleteQueriesEXT:Zb,emscripten_glDeleteRenderbuffers:$b,emscripten_glDeleteSamplers:ac,emscripten_glDeleteShader:bc,emscripten_glDeleteSync:cc,emscripten_glDeleteTextures:dc,emscripten_glDeleteVertexArrays:ec,emscripten_glDeleteVertexArraysOES:ec,emscripten_glDepthMask:fc,emscripten_glDisable:gc,emscripten_glDisableVertexAttribArray:hc,
emscripten_glDrawArrays:ic,emscripten_glDrawArraysInstanced:jc,emscripten_glDrawArraysInstancedBaseInstanceWEBGL:kc,emscripten_glDrawBuffers:lc,emscripten_glDrawElements:mc,emscripten_glDrawElementsInstanced:nc,emscripten_glDrawElementsInstancedBaseVertexBaseInstanceWEBGL:oc,emscripten_glDrawRangeElements:pc,emscripten_glEnable:qc,emscripten_glEnableVertexAttribArray:rc,emscripten_glEndQuery:sc,emscripten_glEndQueryEXT:tc,emscripten_glFenceSync:uc,emscripten_glFinish:vc,emscripten_glFlush:wc,emscripten_glFramebufferRenderbuffer:xc,
emscripten_glFramebufferTexture2D:yc,emscripten_glFrontFace:zc,emscripten_glGenBuffers:Ac,emscripten_glGenFramebuffers:Bc,emscripten_glGenQueries:Cc,emscripten_glGenQueriesEXT:Dc,emscripten_glGenRenderbuffers:Ec,emscripten_glGenSamplers:Fc,emscripten_glGenTextures:Gc,emscripten_glGenVertexArrays:Hc,emscripten_glGenVertexArraysOES:Hc,emscripten_glGenerateMipmap:Ic,emscripten_glGetBufferParameteriv:Jc,emscripten_glGetError:Kc,emscripten_glGetFloatv:Lc,emscripten_glGetFramebufferAttachmentParameteriv:Mc,
emscripten_glGetIntegerv:Nc,emscripten_glGetProgramInfoLog:Oc,emscripten_glGetProgramiv:Pc,emscripten_glGetQueryObjecti64vEXT:Qc,emscripten_glGetQueryObjectui64vEXT:Qc,emscripten_glGetQueryObjectuiv:Rc,emscripten_glGetQueryObjectuivEXT:Sc,emscripten_glGetQueryiv:Tc,emscripten_glGetQueryivEXT:Uc,emscripten_glGetRenderbufferParameteriv:Vc,emscripten_glGetShaderInfoLog:Wc,emscripten_glGetShaderPrecisionFormat:Xc,emscripten_glGetShaderiv:Yc,emscripten_glGetString:Zc,emscripten_glGetStringi:$c,emscripten_glGetUniformLocation:ad,
emscripten_glInvalidateFramebuffer:bd,emscripten_glInvalidateSubFramebuffer:cd,emscripten_glIsSync:dd,emscripten_glIsTexture:ed,emscripten_glLineWidth:fd,emscripten_glLinkProgram:gd,emscripten_glMultiDrawArraysInstancedBaseInstanceWEBGL:hd,emscripten_glMultiDrawElementsInstancedBaseVertexBaseInstanceWEBGL:jd,emscripten_glPixelStorei:kd,emscripten_glQueryCounterEXT:ld,emscripten_glReadBuffer:md,emscripten_glReadPixels:nd,emscripten_glRenderbufferStorage:od,emscripten_glRenderbufferStorageMultisample:pd,
emscripten_glSamplerParameterf:qd,emscripten_glSamplerParameteri:rd,emscripten_glSamplerParameteriv:sd,emscripten_glScissor:td,emscripten_glShaderSource:ud,emscripten_glStencilFunc:vd,emscripten_glStencilFuncSeparate:wd,emscripten_glStencilMask:xd,emscripten_glStencilMaskSeparate:yd,emscripten_glStencilOp:zd,emscripten_glStencilOpSeparate:Ad,emscripten_glTexImage2D:Bd,emscripten_glTexParameterf:Cd,emscripten_glTexParameterfv:Dd,emscripten_glTexParameteri:Ed,emscripten_glTexParameteriv:Fd,emscripten_glTexStorage2D:Gd,
emscripten_glTexSubImage2D:Hd,emscripten_glUniform1f:Id,emscripten_glUniform1fv:Jd,emscripten_glUniform1i:Kd,emscripten_glUniform1iv:Ld,emscripten_glUniform2f:Md,emscripten_glUniform2fv:Nd,emscripten_glUniform2i:Od,emscripten_glUniform2iv:Pd,emscripten_glUniform3f:Qd,emscripten_glUniform3fv:Rd,emscripten_glUniform3i:Sd,emscripten_glUniform3iv:Td,emscripten_glUniform4f:Ud,emscripten_glUniform4fv:Vd,emscripten_glUniform4i:Wd,emscripten_glUniform4iv:Xd,emscripten_glUniformMatrix2fv:Yd,emscripten_glUniformMatrix3fv:Zd,
emscripten_glUniformMatrix4fv:$d,emscripten_glUseProgram:ae,emscripten_glVertexAttrib1f:be,emscripten_glVertexAttrib2fv:ce,emscripten_glVertexAttrib3fv:de,emscripten_glVertexAttrib4fv:ee,emscripten_glVertexAttribDivisor:fe,emscripten_glVertexAttribIPointer:ge,emscripten_glVertexAttribPointer:he,emscripten_glViewport:ie,emscripten_glWaitSync:je,emscripten_resize_heap:ke,emscripten_webgl_enable_extension:le,emscripten_webgl_get_current_context:me,emscripten_webgl_make_context_current:ne,environ_get:oe,
environ_sizes_get:pe,exit:qe,fd_close:re,fd_pread:se,fd_read:te,fd_seek:ue,fd_write:ve,glDeleteTextures:dc,glGetIntegerv:Nc,glGetString:Zc,glGetStringi:$c,invoke_ii:we,invoke_iii:xe,invoke_iiii:ye,invoke_iiiii:ze,invoke_iiiiiii:Ae,invoke_vi:Be,invoke_vii:Ce,invoke_viii:De,invoke_viiii:Ee,invoke_viiiiiii:Fe,memory:e,proc_exit:Ge,skwasm_captureImageBitmap:He,skwasm_connectThread:Ie,skwasm_createGlTextureFromTextureSource:Je,skwasm_createOffscreenCanvas:Ke,skwasm_dispatchDisposeSurface:Le,skwasm_dispatchRasterizeImage:Me,
skwasm_dispatchRenderPictures:Ne,skwasm_disposeAssociatedObjectOnThread:Oe,skwasm_getAssociatedObject:Pe,skwasm_postRasterizeResult:Qe,skwasm_resizeCanvas:Re,skwasm_resolveAndPostImages:Se,skwasm_setAssociatedObjectOnThread:Te};return{env:Ua,wasi_snapshot_preview1:Ua}}function Ue(a){this.name="ExitStatus";this.message=`Program terminated with exit(${a})`;this.status=a}
var Ve=a=>{a.terminate();a.onmessage=()=>{}},Ze=a=>{0==A.length&&(We(),Xe(A[0]));var b=A.pop();if(!b)return 6;Ye.push(b);B[a.l]=b;b.l=a.l;var c={g:"run",la:a.ka,J:a.J,l:a.l};c.B=a.B;c.L=a.L;b.postMessage(c,a.U);return 0},D=0,af=a=>$e(a);v.stackAlloc=af;var H=(a,b,...c)=>{for(var d=c.length,f=E(),g=$e(8*d),l=g>>3,m=0;m<c.length;m++){var p=c[m];fa()[l+m]=p}a=bf(a,0,d,g,b);G(f);return a};
function Ge(a){if(w)return H(0,1,a);ta=a;if(!(cf||0<D)){for(var b of Ye)Ve(b);for(b of A)Ve(b);A=[];Ye=[];B=[];sa=!0}throw new Ue(a);}var df=a=>{if(!(a instanceof Ue||"unwind"==a))throw a;};function ef(a){if(w)return H(1,0,a);qe(a)}var qe=a=>{ta=a;if(w)throw ef(a),"unwind";Ge(a)},A=[],Ye=[],ff=[],B={};function gf(){for(var a=1;a--;)We();Ia.unshift(()=>{z++;hf(()=>Na())})}var kf=a=>{var b=a.l;delete B[b];A.push(a);Ye.splice(Ye.indexOf(a),1);a.l=0;jf(b)};
function Ca(a){"undefined"!=typeof lf&&(Object.assign(mf,a.L),!v.canvas&&a.B&&mf[a.B]&&(v.canvas=mf[a.B].T,v.canvas.id=a.B))}function Da(){ff.forEach(a=>a())}
var Xe=a=>new Promise(b=>{a.onmessage=g=>{g=g.data;var l=g.g;if(g.I&&g.I!=ya()){var m=B[g.I];m?m.postMessage(g,g.U):y(`Internal error! Worker sent a message "${l}" to target pthread ${g.I}, but that thread no longer exists!`)}else if("checkMailbox"===l)Ga();else if("spawnThread"===l)Ze(g);else if("cleanupThread"===l)kf(B[g.ma]);else if("loaded"===l)a.loaded=!0,b(a);else if("alert"===l)alert(`Thread ${g.na}: ${g.text}`);else if("setimmediate"===g.target)a.postMessage(g);else if("callHandler"===l)v[g.ba](...g.X);
else l&&y(`worker sent an unknown command ${l}`)};a.onerror=g=>{y(`${"worker sent an error!"} ${g.filename}:${g.lineno}: ${g.message}`);throw g;};var c=[],d=[],f;for(f of d)v.propertyIsEnumerable(f)&&c.push(f);a.postMessage({g:"load",ca:c,pa:e,qa:ra})});function hf(a){w?a():Promise.all(A.map(Xe)).then(a)}function We(){var a=_scriptName;v.mainScriptUrlOrBlob&&(a=v.mainScriptUrlOrBlob,"string"!=typeof a&&(a=URL.createObjectURL(a)));a=new Worker(a,{type:"module",name:"em-pthread"});A.push(a)}
var nf=a=>{a.forEach(b=>b(v))},Aa=a=>{k();var b=t()[a+52>>2];a=t()[a+56>>2];of(b,b-a);G(b)},I,Fa=(a,b)=>{cf=D=0;a=I.get(a)(b);cf||0<D?ta=a:pf(a)},cf=v.noExitRuntime||!0;class qf{constructor(a){this.A=a-24}}var rf=0,sf=0,Va=(a,b,c)=>{var d=new qf(a);t()[d.A+16>>2]=0;t()[d.A+4>>2]=b;t()[d.A+8>>2]=c;rf=a;sf++;throw rf;};function tf(a,b,c,d){return w?H(2,1,a,b,c,d):Wa(a,b,c,d)}
var uf="undefined"!=typeof TextDecoder?new TextDecoder:void 0,vf=(a,b=0,c=NaN)=>{var d=b+c;for(c=b;a[c]&&!(c>=d);)++c;if(16<c-b&&a.buffer&&uf)return uf.decode(a.slice(b,c));for(d="";b<c;){var f=a[b++];if(f&128){var g=a[b++]&63;if(192==(f&224))d+=String.fromCharCode((f&31)<<6|g);else{var l=a[b++]&63;f=224==(f&240)?(f&15)<<12|g<<6|l:(f&7)<<18|g<<12|l<<6|a[b++]&63;65536>f?d+=String.fromCharCode(f):(f-=65536,d+=String.fromCharCode(55296|f>>10,56320|f&1023))}}else d+=String.fromCharCode(f)}return d},wf=
(a,b)=>a?vf(n(),a,b):"",Wa=(a,b,c,d)=>{if("undefined"==typeof SharedArrayBuffer)return 6;var f=[],g=0,l=b?t()[b+40>>2]:0;l=(l=4294967295==l?"#canvas":wf(l).trim())?l.split(","):[];var m={},p=v.canvas?.id||"";for(r of l){var r=r.trim();try{if("#canvas"==r){if(!v.canvas){y(`pthread_create: could not find canvas with ID "${r}" to transfer to thread!`);g=28;break}r=v.canvas.id}if(mf[r]){var F=mf[r];mf[r]=null;v.canvas instanceof OffscreenCanvas&&r===v.canvas.id&&(v.canvas=null)}else if(!w){var C=v.canvas&&
v.canvas.id===r?v.canvas:document.querySelector(r);if(!C){y(`pthread_create: could not find canvas with ID "${r}" to transfer to thread!`);g=28;break}if(C.Y){y(`pthread_create: cannot transfer canvas with ID "${r}" to thread, since the current thread does not have control over it!`);g=63;break}if(C.transferControlToOffscreen)C.u||(C.u=xf(12),q()[C.u>>2]=C.width,q()[C.u+4>>2]=C.height,t()[C.u+8>>2]=0),F={T:C.transferControlToOffscreen(),u:C.u,id:C.id},C.Y=!0;else return y(`pthread_create: cannot transfer control of canvas "${r}" to pthread, because current browser does not support OffscreenCanvas!`),
y("pthread_create: Build with -sOFFSCREEN_FRAMEBUFFER to enable fallback proxying of GL commands from pthread to main thread."),52}F&&(f.push(F.T),m[F.id]=F)}catch(rg){return y(`pthread_create: failed to transfer control of canvas "${r}" to OffscreenCanvas! Error: ${rg}`),28}}if(w&&(0===f.length||g))return tf(a,b,c,d);if(g)return g;for(C of Object.values(m))t()[C.u+8>>2]=a;a={ka:c,l:a,J:d,B:p,L:m,U:f};return w?(a.g="spawnThread",postMessage(a,f),0):Ze(a)};
function Xa(a,b,c){return w?H(3,1,a,b,c):0}function Ya(a,b){if(w)return H(4,1,a,b)}function Za(a,b,c){return w?H(5,1,a,b,c):0}function $a(a,b,c,d){if(w)return H(6,1,a,b,c,d)}
var ab=()=>{Oa("")},bb=()=>1,cb=a=>{Ba(a,!ma,1,!la,65536,!1);Da()},yf=a=>{if(!sa)try{if(a(),!(cf||0<D))try{w?pf(ta):qe(ta)}catch(b){df(b)}}catch(b){df(b)}},Ea=a=>{"function"===typeof Atomics.oa&&(Atomics.oa(q(),a>>2,a).value.then(Ga),a+=128,Atomics.store(q(),a>>2,1))},Ga=()=>{var a=ya();a&&(Ea(a),yf(zf))},db=(a,b)=>{a==b?setTimeout(Ga):w?postMessage({I:a,g:"checkMailbox"}):(a=B[a])&&a.postMessage({g:"checkMailbox"})},Af=[],eb=(a,b,c,d,f)=>{Af.length=d;b=f>>3;for(c=0;c<d;c++)Af[c]=fa()[b+c];return(0,Bf[a])(...Af)},
fb=()=>{cf=!1;D=0},gb=a=>{w?postMessage({g:"cleanupThread",ma:a}):kf(B[a])},hb=()=>{},ib=()=>{throw Infinity;};function jb(a,b,c,d,f,g,l,m){return w?H(7,1,a,b,c,d,f,g,l,m):-52}function kb(a,b,c,d,f,g,l){if(w)return H(8,1,a,b,c,d,f,g,l)}var Cf={},pb=()=>performance.timeOrigin+performance.now();
function lb(a,b){if(w)return H(9,1,a,b);Cf[a]&&(clearTimeout(Cf[a].id),delete Cf[a]);if(!b)return 0;var c=setTimeout(()=>{delete Cf[a];yf(()=>Df(a,performance.timeOrigin+performance.now()))},b);Cf[a]={id:c,ua:b};return 0}
var J=(a,b,c)=>{var d=n();if(0<c){var f=b;c=b+c-1;for(var g=0;g<a.length;++g){var l=a.charCodeAt(g);if(55296<=l&&57343>=l){var m=a.charCodeAt(++g);l=65536+((l&1023)<<10)|m&1023}if(127>=l){if(b>=c)break;d[b++]=l}else{if(2047>=l){if(b+1>=c)break;d[b++]=192|l>>6}else{if(65535>=l){if(b+2>=c)break;d[b++]=224|l>>12}else{if(b+3>=c)break;d[b++]=240|l>>18;d[b++]=128|l>>12&63}d[b++]=128|l>>6&63}d[b++]=128|l&63}}d[b]=0;a=b-f}else a=0;return a},mb=(a,b,c,d)=>{var f=(new Date).getFullYear(),g=(new Date(f,0,1)).getTimezoneOffset();
f=(new Date(f,6,1)).getTimezoneOffset();var l=Math.max(g,f);t()[a>>2]=60*l;q()[b>>2]=Number(g!=f);b=m=>{var p=Math.abs(m);return`UTC${0<=m?"-":"+"}${String(Math.floor(p/60)).padStart(2,"0")}${String(p%60).padStart(2,"0")}`};a=b(g);b=b(f);f<g?(J(a,c,17),J(b,d,17)):(J(a,d,17),J(b,c,17))},nb=()=>{},ob=()=>{D+=1;throw"unwind";},K,Ef=a=>{var b=a.getExtension("ANGLE_instanced_arrays");b&&(a.vertexAttribDivisor=(c,d)=>b.vertexAttribDivisorANGLE(c,d),a.drawArraysInstanced=(c,d,f,g)=>b.drawArraysInstancedANGLE(c,
d,f,g),a.drawElementsInstanced=(c,d,f,g,l)=>b.drawElementsInstancedANGLE(c,d,f,g,l))},Ff=a=>{var b=a.getExtension("OES_vertex_array_object");b&&(a.createVertexArray=()=>b.createVertexArrayOES(),a.deleteVertexArray=c=>b.deleteVertexArrayOES(c),a.bindVertexArray=c=>b.bindVertexArrayOES(c),a.isVertexArray=c=>b.isVertexArrayOES(c))},Gf=a=>{var b=a.getExtension("WEBGL_draw_buffers");b&&(a.drawBuffers=(c,d)=>b.drawBuffersWEBGL(c,d))},Hf=a=>{a.O=a.getExtension("WEBGL_draw_instanced_base_vertex_base_instance")},
If=a=>{a.S=a.getExtension("WEBGL_multi_draw_instanced_base_vertex_base_instance")},Jf=a=>{var b="ANGLE_instanced_arrays EXT_blend_minmax EXT_disjoint_timer_query EXT_frag_depth EXT_shader_texture_lod EXT_sRGB OES_element_index_uint OES_fbo_render_mipmap OES_standard_derivatives OES_texture_float OES_texture_half_float OES_texture_half_float_linear OES_vertex_array_object WEBGL_color_buffer_float WEBGL_depth_texture WEBGL_draw_buffers EXT_color_buffer_float EXT_conservative_depth EXT_disjoint_timer_query_webgl2 EXT_texture_norm16 NV_shader_noperspective_interpolation WEBGL_clip_cull_distance EXT_clip_control EXT_color_buffer_half_float EXT_depth_clamp EXT_float_blend EXT_polygon_offset_clamp EXT_texture_compression_bptc EXT_texture_compression_rgtc EXT_texture_filter_anisotropic KHR_parallel_shader_compile OES_texture_float_linear WEBGL_blend_func_extended WEBGL_compressed_texture_astc WEBGL_compressed_texture_etc WEBGL_compressed_texture_etc1 WEBGL_compressed_texture_s3tc WEBGL_compressed_texture_s3tc_srgb WEBGL_debug_renderer_info WEBGL_debug_shaders WEBGL_lose_context WEBGL_multi_draw WEBGL_polygon_mode".split(" ");
return(a.getSupportedExtensions()||[]).filter(c=>b.includes(c))},Kf=1,Lf=[],L=[],Mf=[],Nf=[],M=[],N=[],Of=[],Pf={},mf={},O=[],P=[],Q=[],Qf={},Rf={},Sf=4,Tf=0,Uf=a=>{for(var b=Kf++,c=a.length;c<b;c++)a[c]=null;return b},S=(a,b,c,d)=>{for(var f=0;f<a;f++){var g=K[c](),l=g&&Uf(d);g?(g.name=l,d[l]=g):R||=1282;q()[b+4*f>>2]=l}},Wf=a=>{var b={R:2,alpha:!0,depth:!0,stencil:!0,antialias:!1,premultipliedAlpha:!0,preserveDrawingBuffer:!1,powerPreference:"default",failIfMajorPerformanceCaveat:!1,P:!0};a.A||
(a.A=a.getContext,a.getContext=function(d,f){f=a.A(d,f);return"webgl"==d==f instanceof WebGLRenderingContext?f:null});var c=1<b.R?a.getContext("webgl2",b):a.getContext("webgl",b);return c?Vf(c,b):0},Vf=(a,b)=>{var c=xf(8);t()[c+4>>2]=ya();var d={handle:c,attributes:b,version:b.R,D:a};a.canvas&&(a.canvas.sa=d);Pf[c]=d;("undefined"==typeof b.P||b.P)&&Xf(d);return c},Xf=a=>{a||=T;if(!a.ea){a.ea=!0;var b=a.D;b.fa=b.getExtension("WEBGL_multi_draw");b.aa=b.getExtension("EXT_polygon_offset_clamp");b.$=b.getExtension("EXT_clip_control");
b.ra=b.getExtension("WEBGL_polygon_mode");Ef(b);Ff(b);Gf(b);Hf(b);If(b);2<=a.version&&(b.h=b.getExtension("EXT_disjoint_timer_query_webgl2"));if(2>a.version||!b.h)b.h=b.getExtension("EXT_disjoint_timer_query");Jf(b).forEach(c=>{c.includes("lose_context")||c.includes("debug")||b.getExtension(c)})}},lf={},R,T,qb=a=>K.activeTexture(a),rb=(a,b)=>{K.attachShader(L[a],N[b])},sb=(a,b)=>{K.beginQuery(a,O[b])},tb=(a,b)=>{K.h.beginQueryEXT(a,O[b])},ub=(a,b,c)=>{K.bindAttribLocation(L[a],b,wf(c))},vb=(a,b)=>
{35051==a?K.K=b:35052==a&&(K.v=b);K.bindBuffer(a,Lf[b])},wb=(a,b)=>{K.bindFramebuffer(a,Mf[b])},xb=(a,b)=>{K.bindRenderbuffer(a,Nf[b])},yb=(a,b)=>{K.bindSampler(a,P[b])},zb=(a,b)=>{K.bindTexture(a,M[b])},Ab=a=>{K.bindVertexArray(Of[a])},Bb=(a,b,c,d)=>K.blendColor(a,b,c,d),Cb=a=>K.blendEquation(a),Db=(a,b)=>K.blendFunc(a,b),Eb=(a,b,c,d,f,g,l,m,p,r)=>K.blitFramebuffer(a,b,c,d,f,g,l,m,p,r),Fb=(a,b,c,d)=>{2<=T.version?c&&b?K.bufferData(a,n(),d,c,b):K.bufferData(a,b,d):K.bufferData(a,c?n().subarray(c,
c+b):b,d)},Gb=(a,b,c,d)=>{2<=T.version?c&&K.bufferSubData(a,b,n(),d,c):K.bufferSubData(a,b,n().subarray(d,d+c))},Hb=a=>K.checkFramebufferStatus(a),Ib=a=>K.clear(a),Jb=(a,b,c,d)=>K.clearColor(a,b,c,d),Kb=a=>K.clearStencil(a),Lb=(a,b,c,d)=>K.clientWaitSync(Q[a],b,(c>>>0)+4294967296*d),Mb=(a,b,c,d)=>{K.colorMask(!!a,!!b,!!c,!!d)},Nb=a=>{K.compileShader(N[a])},Ob=(a,b,c,d,f,g,l,m)=>{2<=T.version?K.v||!l?K.compressedTexImage2D(a,b,c,d,f,g,l,m):K.compressedTexImage2D(a,b,c,d,f,g,n(),m,l):K.compressedTexImage2D(a,
b,c,d,f,g,n().subarray(m,m+l))},Pb=(a,b,c,d,f,g,l,m,p)=>{2<=T.version?K.v||!m?K.compressedTexSubImage2D(a,b,c,d,f,g,l,m,p):K.compressedTexSubImage2D(a,b,c,d,f,g,l,n(),p,m):K.compressedTexSubImage2D(a,b,c,d,f,g,l,n().subarray(p,p+m))},Qb=(a,b,c,d,f)=>K.copyBufferSubData(a,b,c,d,f),Rb=(a,b,c,d,f,g,l,m)=>K.copyTexSubImage2D(a,b,c,d,f,g,l,m),Sb=()=>{var a=Uf(L),b=K.createProgram();b.name=a;b.H=b.F=b.G=0;b.N=1;L[a]=b;return a},Tb=a=>{var b=Uf(N);N[b]=K.createShader(a);return b},Ub=a=>K.cullFace(a),Vb=
(a,b)=>{for(var c=0;c<a;c++){var d=q()[b+4*c>>2],f=Lf[d];f&&(K.deleteBuffer(f),f.name=0,Lf[d]=null,d==K.K&&(K.K=0),d==K.v&&(K.v=0))}},Wb=(a,b)=>{for(var c=0;c<a;++c){var d=q()[b+4*c>>2],f=Mf[d];f&&(K.deleteFramebuffer(f),f.name=0,Mf[d]=null)}},Xb=a=>{if(a){var b=L[a];b?(K.deleteProgram(b),b.name=0,L[a]=null):R||=1281}},Yb=(a,b)=>{for(var c=0;c<a;c++){var d=q()[b+4*c>>2],f=O[d];f&&(K.deleteQuery(f),O[d]=null)}},Zb=(a,b)=>{for(var c=0;c<a;c++){var d=q()[b+4*c>>2],f=O[d];f&&(K.h.deleteQueryEXT(f),O[d]=
null)}},$b=(a,b)=>{for(var c=0;c<a;c++){var d=q()[b+4*c>>2],f=Nf[d];f&&(K.deleteRenderbuffer(f),f.name=0,Nf[d]=null)}},ac=(a,b)=>{for(var c=0;c<a;c++){var d=q()[b+4*c>>2],f=P[d];f&&(K.deleteSampler(f),f.name=0,P[d]=null)}},bc=a=>{if(a){var b=N[a];b?(K.deleteShader(b),N[a]=null):R||=1281}},cc=a=>{if(a){var b=Q[a];b?(K.deleteSync(b),b.name=0,Q[a]=null):R||=1281}},dc=(a,b)=>{for(var c=0;c<a;c++){var d=q()[b+4*c>>2],f=M[d];f&&(K.deleteTexture(f),f.name=0,M[d]=null)}},ec=(a,b)=>{for(var c=0;c<a;c++){var d=
q()[b+4*c>>2];K.deleteVertexArray(Of[d]);Of[d]=null}},fc=a=>{K.depthMask(!!a)},gc=a=>K.disable(a),hc=a=>{K.disableVertexAttribArray(a)},ic=(a,b,c)=>{K.drawArrays(a,b,c)},jc=(a,b,c,d)=>{K.drawArraysInstanced(a,b,c,d)},kc=(a,b,c,d,f)=>{K.O.drawArraysInstancedBaseInstanceWEBGL(a,b,c,d,f)},Yf=[],lc=(a,b)=>{for(var c=Yf[a],d=0;d<a;d++)c[d]=q()[b+4*d>>2];K.drawBuffers(c)},mc=(a,b,c,d)=>{K.drawElements(a,b,c,d)},nc=(a,b,c,d,f)=>{K.drawElementsInstanced(a,b,c,d,f)},oc=(a,b,c,d,f,g,l)=>{K.O.drawElementsInstancedBaseVertexBaseInstanceWEBGL(a,
b,c,d,f,g,l)},pc=(a,b,c,d,f,g)=>{K.drawElements(a,d,f,g)},qc=a=>K.enable(a),rc=a=>{K.enableVertexAttribArray(a)},sc=a=>K.endQuery(a),tc=a=>{K.h.endQueryEXT(a)},uc=(a,b)=>(a=K.fenceSync(a,b))?(b=Uf(Q),a.name=b,Q[b]=a,b):0,vc=()=>K.finish(),wc=()=>K.flush(),xc=(a,b,c,d)=>{K.framebufferRenderbuffer(a,b,c,Nf[d])},yc=(a,b,c,d,f)=>{K.framebufferTexture2D(a,b,c,M[d],f)},zc=a=>K.frontFace(a),Ac=(a,b)=>{S(a,b,"createBuffer",Lf)},Bc=(a,b)=>{S(a,b,"createFramebuffer",Mf)},Cc=(a,b)=>{S(a,b,"createQuery",O)},
Dc=(a,b)=>{for(var c=0;c<a;c++){var d=K.h.createQueryEXT();if(!d){for(R||=1282;c<a;)q()[b+4*c++>>2]=0;break}var f=Uf(O);d.name=f;O[f]=d;q()[b+4*c>>2]=f}},Ec=(a,b)=>{S(a,b,"createRenderbuffer",Nf)},Fc=(a,b)=>{S(a,b,"createSampler",P)},Gc=(a,b)=>{S(a,b,"createTexture",M)},Hc=(a,b)=>{S(a,b,"createVertexArray",Of)},Ic=a=>K.generateMipmap(a),Jc=(a,b,c)=>{c?q()[c>>2]=K.getBufferParameter(a,b):R||=1281},Kc=()=>{var a=K.getError()||R;R=0;return a},Zf=(a,b)=>{t()[a>>2]=b;var c=t()[a>>2];t()[a+4>>2]=(b-c)/
4294967296};function $f(){var a=Jf(K);return a=a.concat(a.map(b=>"GL_"+b))}
var ag=(a,b,c)=>{if(b){var d=void 0;switch(a){case 36346:d=1;break;case 36344:0!=c&&1!=c&&(R||=1280);return;case 34814:case 36345:d=0;break;case 34466:var f=K.getParameter(34467);d=f?f.length:0;break;case 33309:if(2>T.version){R||=1282;return}d=$f().length;break;case 33307:case 33308:if(2>T.version){R||=1280;return}d=33307==a?3:0}if(void 0===d)switch(f=K.getParameter(a),typeof f){case "number":d=f;break;case "boolean":d=f?1:0;break;case "string":R||=1280;return;case "object":if(null===f)switch(a){case 34964:case 35725:case 34965:case 36006:case 36007:case 32873:case 34229:case 36662:case 36663:case 35053:case 35055:case 36010:case 35097:case 35869:case 32874:case 36389:case 35983:case 35368:case 34068:d=
0;break;default:R||=1280;return}else{if(f instanceof Float32Array||f instanceof Uint32Array||f instanceof Int32Array||f instanceof Array){for(a=0;a<f.length;++a)switch(c){case 0:q()[b+4*a>>2]=f[a];break;case 2:u()[b+4*a>>2]=f[a];break;case 4:aa()[b+a]=f[a]?1:0}return}try{d=f.name|0}catch(g){R||=1280;y(`GL_INVALID_ENUM in glGet${c}v: Unknown object returned from WebGL getParameter(${a})! (error: ${g})`);return}}break;default:R||=1280;y(`GL_INVALID_ENUM in glGet${c}v: Native code calling glGet${c}v(${a}) and it returns ${f} of type ${typeof f}!`);
return}switch(c){case 1:Zf(b,d);break;case 0:q()[b>>2]=d;break;case 2:u()[b>>2]=d;break;case 4:aa()[b]=d?1:0}}else R||=1281},Lc=(a,b)=>ag(a,b,2),Mc=(a,b,c,d)=>{a=K.getFramebufferAttachmentParameter(a,b,c);if(a instanceof WebGLRenderbuffer||a instanceof WebGLTexture)a=a.name|0;q()[d>>2]=a},Nc=(a,b)=>ag(a,b,0),Oc=(a,b,c,d)=>{a=K.getProgramInfoLog(L[a]);null===a&&(a="(unknown error)");b=0<b&&d?J(a,d,b):0;c&&(q()[c>>2]=b)},Pc=(a,b,c)=>{if(c)if(a>=Kf)R||=1281;else if(a=L[a],35716==b)a=K.getProgramInfoLog(a),
null===a&&(a="(unknown error)"),q()[c>>2]=a.length+1;else if(35719==b){if(!a.H){var d=K.getProgramParameter(a,35718);for(b=0;b<d;++b)a.H=Math.max(a.H,K.getActiveUniform(a,b).name.length+1)}q()[c>>2]=a.H}else if(35722==b){if(!a.F)for(d=K.getProgramParameter(a,35721),b=0;b<d;++b)a.F=Math.max(a.F,K.getActiveAttrib(a,b).name.length+1);q()[c>>2]=a.F}else if(35381==b){if(!a.G)for(d=K.getProgramParameter(a,35382),b=0;b<d;++b)a.G=Math.max(a.G,K.getActiveUniformBlockName(a,b).length+1);q()[c>>2]=a.G}else q()[c>>
2]=K.getProgramParameter(a,b);else R||=1281},Qc=(a,b,c)=>{if(c){a=O[a];b=2>T.version?K.h.getQueryObjectEXT(a,b):K.getQueryParameter(a,b);var d;"boolean"==typeof b?d=b?1:0:d=b;Zf(c,d)}else R||=1281},Rc=(a,b,c)=>{if(c){a=K.getQueryParameter(O[a],b);var d;"boolean"==typeof a?d=a?1:0:d=a;q()[c>>2]=d}else R||=1281},Sc=(a,b,c)=>{if(c){a=K.h.getQueryObjectEXT(O[a],b);var d;"boolean"==typeof a?d=a?1:0:d=a;q()[c>>2]=d}else R||=1281},Tc=(a,b,c)=>{c?q()[c>>2]=K.getQuery(a,b):R||=1281},Uc=(a,b,c)=>{c?q()[c>>
2]=K.h.getQueryEXT(a,b):R||=1281},Vc=(a,b,c)=>{c?q()[c>>2]=K.getRenderbufferParameter(a,b):R||=1281},Wc=(a,b,c,d)=>{a=K.getShaderInfoLog(N[a]);null===a&&(a="(unknown error)");b=0<b&&d?J(a,d,b):0;c&&(q()[c>>2]=b)},Xc=(a,b,c,d)=>{a=K.getShaderPrecisionFormat(a,b);q()[c>>2]=a.rangeMin;q()[c+4>>2]=a.rangeMax;q()[d>>2]=a.precision},Yc=(a,b,c)=>{c?35716==b?(a=K.getShaderInfoLog(N[a]),null===a&&(a="(unknown error)"),a=a?a.length+1:0,q()[c>>2]=a):35720==b?(a=(a=K.getShaderSource(N[a]))?a.length+1:0,q()[c>>
2]=a):q()[c>>2]=K.getShaderParameter(N[a],b):R||=1281},bg=a=>{for(var b=0,c=0;c<a.length;++c){var d=a.charCodeAt(c);127>=d?b++:2047>=d?b+=2:55296<=d&&57343>=d?(b+=4,++c):b+=3}b+=1;(c=xf(b))&&J(a,c,b);return c},Zc=a=>{var b=Qf[a];if(!b){switch(a){case 7939:b=bg($f().join(" "));break;case 7936:case 7937:case 37445:case 37446:(b=K.getParameter(a))||(R||=1280);b=b?bg(b):0;break;case 7938:b=K.getParameter(7938);var c=`OpenGL ES 2.0 (${b})`;2<=T.version&&(c=`OpenGL ES 3.0 (${b})`);b=bg(c);break;case 35724:b=
K.getParameter(35724);c=b.match(/^WebGL GLSL ES ([0-9]\.[0-9][0-9]?)(?:$| .*)/);null!==c&&(3==c[1].length&&(c[1]+="0"),b=`OpenGL ES GLSL ES ${c[1]} (${b})`);b=bg(b);break;default:R||=1280}Qf[a]=b}return b},$c=(a,b)=>{if(2>T.version)return R||=1282,0;var c=Rf[a];if(c)return 0>b||b>=c.length?(R||=1281,0):c[b];switch(a){case 7939:return c=$f().map(bg),c=Rf[a]=c,0>b||b>=c.length?(R||=1281,0):c[b];default:return R||=1280,0}},cg=a=>"]"==a.slice(-1)&&a.lastIndexOf("["),ad=(a,b)=>{b=wf(b);if(a=L[a]){var c=
a,d=c.C,f=c.W,g;if(!d){c.C=d={};c.V={};var l=K.getProgramParameter(c,35718);for(g=0;g<l;++g){var m=K.getActiveUniform(c,g);var p=m.name;m=m.size;var r=cg(p);r=0<r?p.slice(0,r):p;var F=c.N;c.N+=m;f[r]=[m,F];for(p=0;p<m;++p)d[F]=p,c.V[F++]=r}}c=a.C;d=0;f=b;g=cg(b);0<g&&(d=parseInt(b.slice(g+1))>>>0,f=b.slice(0,g));if((f=a.W[f])&&d<f[0]&&(d+=f[1],c[d]=c[d]||K.getUniformLocation(a,b)))return d}else R||=1281;return-1},bd=(a,b,c)=>{for(var d=Yf[b],f=0;f<b;f++)d[f]=q()[c+4*f>>2];K.invalidateFramebuffer(a,
d)},cd=(a,b,c,d,f,g,l)=>{for(var m=Yf[b],p=0;p<b;p++)m[p]=q()[c+4*p>>2];K.invalidateSubFramebuffer(a,m,d,f,g,l)},dd=a=>K.isSync(Q[a]),ed=a=>(a=M[a])?K.isTexture(a):0,fd=a=>K.lineWidth(a),gd=a=>{a=L[a];K.linkProgram(a);a.C=0;a.W={}},hd=(a,b,c,d,f,g)=>{K.S.multiDrawArraysInstancedBaseInstanceWEBGL(a,q(),b>>2,q(),c>>2,q(),d>>2,t(),f>>2,g)},jd=(a,b,c,d,f,g,l,m)=>{K.S.multiDrawElementsInstancedBaseVertexBaseInstanceWEBGL(a,q(),b>>2,c,q(),d>>2,q(),f>>2,q(),g>>2,t(),l>>2,m)},kd=(a,b)=>{3317==a?Sf=b:3314==
a&&(Tf=b);K.pixelStorei(a,b)},ld=(a,b)=>{K.h.queryCounterEXT(O[a],b)},md=a=>K.readBuffer(a),dg=a=>{a-=5120;0==a?a=aa():1==a?a=n():2==a?(e.buffer!=h.buffer&&k(),a=ua):4==a?a=q():6==a?a=u():5==a||28922==a||28520==a||30779==a||30782==a?a=t():(e.buffer!=h.buffer&&k(),a=va);return a},eg=(a,b,c,d,f)=>{a=dg(a);b=d*((Tf||c)*({5:3,6:4,8:2,29502:3,29504:4,26917:2,26918:2,29846:3,29847:4}[b-6402]||1)*a.BYTES_PER_ELEMENT+Sf-1&-Sf);return a.subarray(f>>>31-Math.clz32(a.BYTES_PER_ELEMENT),f+b>>>31-Math.clz32(a.BYTES_PER_ELEMENT))},
nd=(a,b,c,d,f,g,l)=>{if(2<=T.version)if(K.K)K.readPixels(a,b,c,d,f,g,l);else{var m=dg(g);l>>>=31-Math.clz32(m.BYTES_PER_ELEMENT);K.readPixels(a,b,c,d,f,g,m,l)}else(m=eg(g,f,c,d,l))?K.readPixels(a,b,c,d,f,g,m):R||=1280},od=(a,b,c,d)=>K.renderbufferStorage(a,b,c,d),pd=(a,b,c,d,f)=>K.renderbufferStorageMultisample(a,b,c,d,f),qd=(a,b,c)=>{K.samplerParameterf(P[a],b,c)},rd=(a,b,c)=>{K.samplerParameteri(P[a],b,c)},sd=(a,b,c)=>{c=q()[c>>2];K.samplerParameteri(P[a],b,c)},td=(a,b,c,d)=>K.scissor(a,b,c,d),
ud=(a,b,c,d)=>{for(var f="",g=0;g<b;++g){var l=d?t()[d+4*g>>2]:void 0;f+=wf(t()[c+4*g>>2],l)}K.shaderSource(N[a],f)},vd=(a,b,c)=>K.stencilFunc(a,b,c),wd=(a,b,c,d)=>K.stencilFuncSeparate(a,b,c,d),xd=a=>K.stencilMask(a),yd=(a,b)=>K.stencilMaskSeparate(a,b),zd=(a,b,c)=>K.stencilOp(a,b,c),Ad=(a,b,c,d)=>K.stencilOpSeparate(a,b,c,d),Bd=(a,b,c,d,f,g,l,m,p)=>{if(2<=T.version){if(K.v){K.texImage2D(a,b,c,d,f,g,l,m,p);return}if(p){var r=dg(m);p>>>=31-Math.clz32(r.BYTES_PER_ELEMENT);K.texImage2D(a,b,c,d,f,g,
l,m,r,p);return}}r=p?eg(m,l,d,f,p):null;K.texImage2D(a,b,c,d,f,g,l,m,r)},Cd=(a,b,c)=>K.texParameterf(a,b,c),Dd=(a,b,c)=>{c=u()[c>>2];K.texParameterf(a,b,c)},Ed=(a,b,c)=>K.texParameteri(a,b,c),Fd=(a,b,c)=>{c=q()[c>>2];K.texParameteri(a,b,c)},Gd=(a,b,c,d,f)=>K.texStorage2D(a,b,c,d,f),Hd=(a,b,c,d,f,g,l,m,p)=>{if(2<=T.version){if(K.v){K.texSubImage2D(a,b,c,d,f,g,l,m,p);return}if(p){var r=dg(m);K.texSubImage2D(a,b,c,d,f,g,l,m,r,p>>>31-Math.clz32(r.BYTES_PER_ELEMENT));return}}p=p?eg(m,l,f,g,p):null;K.texSubImage2D(a,
b,c,d,f,g,l,m,p)},U=a=>{var b=K.Z;if(b){var c=b.C[a];"number"==typeof c&&(b.C[a]=c=K.getUniformLocation(b,b.V[a]+(0<c?`[${c}]`:"")));return c}R||=1282},Id=(a,b)=>{K.uniform1f(U(a),b)},V=[],Jd=(a,b,c)=>{if(2<=T.version)b&&K.uniform1fv(U(a),u(),c>>2,b);else{if(288>=b)for(var d=V[b],f=0;f<b;++f)d[f]=u()[c+4*f>>2];else d=u().subarray(c>>2,c+4*b>>2);K.uniform1fv(U(a),d)}},Kd=(a,b)=>{K.uniform1i(U(a),b)},fg=[],Ld=(a,b,c)=>{if(2<=T.version)b&&K.uniform1iv(U(a),q(),c>>2,b);else{if(288>=b)for(var d=fg[b],
f=0;f<b;++f)d[f]=q()[c+4*f>>2];else d=q().subarray(c>>2,c+4*b>>2);K.uniform1iv(U(a),d)}},Md=(a,b,c)=>{K.uniform2f(U(a),b,c)},Nd=(a,b,c)=>{if(2<=T.version)b&&K.uniform2fv(U(a),u(),c>>2,2*b);else{if(144>=b){b*=2;for(var d=V[b],f=0;f<b;f+=2)d[f]=u()[c+4*f>>2],d[f+1]=u()[c+(4*f+4)>>2]}else d=u().subarray(c>>2,c+8*b>>2);K.uniform2fv(U(a),d)}},Od=(a,b,c)=>{K.uniform2i(U(a),b,c)},Pd=(a,b,c)=>{if(2<=T.version)b&&K.uniform2iv(U(a),q(),c>>2,2*b);else{if(144>=b){b*=2;for(var d=fg[b],f=0;f<b;f+=2)d[f]=q()[c+
4*f>>2],d[f+1]=q()[c+(4*f+4)>>2]}else d=q().subarray(c>>2,c+8*b>>2);K.uniform2iv(U(a),d)}},Qd=(a,b,c,d)=>{K.uniform3f(U(a),b,c,d)},Rd=(a,b,c)=>{if(2<=T.version)b&&K.uniform3fv(U(a),u(),c>>2,3*b);else{if(96>=b){b*=3;for(var d=V[b],f=0;f<b;f+=3)d[f]=u()[c+4*f>>2],d[f+1]=u()[c+(4*f+4)>>2],d[f+2]=u()[c+(4*f+8)>>2]}else d=u().subarray(c>>2,c+12*b>>2);K.uniform3fv(U(a),d)}},Sd=(a,b,c,d)=>{K.uniform3i(U(a),b,c,d)},Td=(a,b,c)=>{if(2<=T.version)b&&K.uniform3iv(U(a),q(),c>>2,3*b);else{if(96>=b){b*=3;for(var d=
fg[b],f=0;f<b;f+=3)d[f]=q()[c+4*f>>2],d[f+1]=q()[c+(4*f+4)>>2],d[f+2]=q()[c+(4*f+8)>>2]}else d=q().subarray(c>>2,c+12*b>>2);K.uniform3iv(U(a),d)}},Ud=(a,b,c,d,f)=>{K.uniform4f(U(a),b,c,d,f)},Vd=(a,b,c)=>{if(2<=T.version)b&&K.uniform4fv(U(a),u(),c>>2,4*b);else{if(72>=b){var d=V[4*b],f=u();c>>=2;b*=4;for(var g=0;g<b;g+=4){var l=c+g;d[g]=f[l];d[g+1]=f[l+1];d[g+2]=f[l+2];d[g+3]=f[l+3]}}else d=u().subarray(c>>2,c+16*b>>2);K.uniform4fv(U(a),d)}},Wd=(a,b,c,d,f)=>{K.uniform4i(U(a),b,c,d,f)},Xd=(a,b,c)=>{if(2<=
T.version)b&&K.uniform4iv(U(a),q(),c>>2,4*b);else{if(72>=b){b*=4;for(var d=fg[b],f=0;f<b;f+=4)d[f]=q()[c+4*f>>2],d[f+1]=q()[c+(4*f+4)>>2],d[f+2]=q()[c+(4*f+8)>>2],d[f+3]=q()[c+(4*f+12)>>2]}else d=q().subarray(c>>2,c+16*b>>2);K.uniform4iv(U(a),d)}},Yd=(a,b,c,d)=>{if(2<=T.version)b&&K.uniformMatrix2fv(U(a),!!c,u(),d>>2,4*b);else{if(72>=b){b*=4;for(var f=V[b],g=0;g<b;g+=4)f[g]=u()[d+4*g>>2],f[g+1]=u()[d+(4*g+4)>>2],f[g+2]=u()[d+(4*g+8)>>2],f[g+3]=u()[d+(4*g+12)>>2]}else f=u().subarray(d>>2,d+16*b>>2);
K.uniformMatrix2fv(U(a),!!c,f)}},Zd=(a,b,c,d)=>{if(2<=T.version)b&&K.uniformMatrix3fv(U(a),!!c,u(),d>>2,9*b);else{if(32>=b){b*=9;for(var f=V[b],g=0;g<b;g+=9)f[g]=u()[d+4*g>>2],f[g+1]=u()[d+(4*g+4)>>2],f[g+2]=u()[d+(4*g+8)>>2],f[g+3]=u()[d+(4*g+12)>>2],f[g+4]=u()[d+(4*g+16)>>2],f[g+5]=u()[d+(4*g+20)>>2],f[g+6]=u()[d+(4*g+24)>>2],f[g+7]=u()[d+(4*g+28)>>2],f[g+8]=u()[d+(4*g+32)>>2]}else f=u().subarray(d>>2,d+36*b>>2);K.uniformMatrix3fv(U(a),!!c,f)}},$d=(a,b,c,d)=>{if(2<=T.version)b&&K.uniformMatrix4fv(U(a),
!!c,u(),d>>2,16*b);else{if(18>=b){var f=V[16*b],g=u();d>>=2;b*=16;for(var l=0;l<b;l+=16){var m=d+l;f[l]=g[m];f[l+1]=g[m+1];f[l+2]=g[m+2];f[l+3]=g[m+3];f[l+4]=g[m+4];f[l+5]=g[m+5];f[l+6]=g[m+6];f[l+7]=g[m+7];f[l+8]=g[m+8];f[l+9]=g[m+9];f[l+10]=g[m+10];f[l+11]=g[m+11];f[l+12]=g[m+12];f[l+13]=g[m+13];f[l+14]=g[m+14];f[l+15]=g[m+15]}}else f=u().subarray(d>>2,d+64*b>>2);K.uniformMatrix4fv(U(a),!!c,f)}},ae=a=>{a=L[a];K.useProgram(a);K.Z=a},be=(a,b)=>K.vertexAttrib1f(a,b),ce=(a,b)=>{K.vertexAttrib2f(a,u()[b>>
2],u()[b+4>>2])},de=(a,b)=>{K.vertexAttrib3f(a,u()[b>>2],u()[b+4>>2],u()[b+8>>2])},ee=(a,b)=>{K.vertexAttrib4f(a,u()[b>>2],u()[b+4>>2],u()[b+8>>2],u()[b+12>>2])},fe=(a,b)=>{K.vertexAttribDivisor(a,b)},ge=(a,b,c,d,f)=>{K.vertexAttribIPointer(a,b,c,d,f)},he=(a,b,c,d,f,g)=>{K.vertexAttribPointer(a,b,c,!!d,f,g)},ie=(a,b,c,d)=>K.viewport(a,b,c,d),je=(a,b,c,d)=>{K.waitSync(Q[a],b,(c>>>0)+4294967296*d)},ke=a=>{var b=n().length;a>>>=0;if(a<=b||2147483648<a)return!1;for(var c=1;4>=c;c*=2){var d=b*(1+.2/c);
d=Math.min(d,a+100663296);a:{d=(Math.min(2147483648,65536*Math.ceil(Math.max(a,d)/65536))-e.buffer.byteLength+65535)/65536|0;try{e.grow(d);k();var f=1;break a}catch(g){}f=void 0}if(f)return!0}return!1};
function le(a,b){a=Pf[a];b=wf(b);b.startsWith("GL_")&&(b=b.substr(3));"ANGLE_instanced_arrays"==b&&Ef(K);"OES_vertex_array_object"==b&&Ff(K);"WEBGL_draw_buffers"==b&&Gf(K);"WEBGL_draw_instanced_base_vertex_base_instance"==b&&Hf(K);"WEBGL_multi_draw_instanced_base_vertex_base_instance"==b&&If(K);"WEBGL_multi_draw"==b&&(K.fa=K.getExtension("WEBGL_multi_draw"));"EXT_polygon_offset_clamp"==b&&(K.aa=K.getExtension("EXT_polygon_offset_clamp"));"EXT_clip_control"==b&&(K.$=K.getExtension("EXT_clip_control"));
"WEBGL_polygon_mode"==b&&(K.ra=K.getExtension("WEBGL_polygon_mode"));return!!a.D.getExtension(b)}
var me=()=>T?T.handle:0,ne=a=>{T=Pf[a];v.ta=K=T?.D;return!a||K?0:-5},gg={},ig=()=>{if(!hg){var a={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:"./this.program"},b;for(b in gg)void 0===gg[b]?delete a[b]:a[b]=gg[b];var c=[];for(b in a)c.push(`${b}=${a[b]}`);hg=c}return hg},hg;
function oe(a,b){if(w)return H(10,1,a,b);var c=0;ig().forEach((d,f)=>{var g=b+c;f=t()[a+4*f>>2]=g;for(g=0;g<d.length;++g)aa()[f++]=d.charCodeAt(g);aa()[f]=0;c+=d.length+1});return 0}function pe(a,b){if(w)return H(11,1,a,b);var c=ig();t()[a>>2]=c.length;var d=0;c.forEach(f=>d+=f.length+1);t()[b>>2]=d;return 0}function re(a){return w?H(12,1,a):52}function se(a,b,c,d,f,g){return w?H(13,1,a,b,c,d,f,g):52}function te(a,b,c,d){return w?H(14,1,a,b,c,d):52}
function ue(a,b,c,d,f){return w?H(15,1,a,b,c,d,f):70}var jg=[null,[],[]];function ve(a,b,c,d){if(w)return H(16,1,a,b,c,d);for(var f=0,g=0;g<c;g++){var l=t()[b>>2],m=t()[b+4>>2];b+=8;for(var p=0;p<m;p++){var r=n()[l+p],F=jg[a];0===r||10===r?((1===a?qa:y)(vf(F)),F.length=0):F.push(r)}f+=m}t()[d>>2]=f;return 0}function kg(){}function lg(){}function W(){}function He(){}function Ie(){}function Je(){}function Ke(){}function Le(){}function Me(){}function Ne(){}function Oe(){}function Pe(){}
function Qe(){}function Re(){}function Se(){}function Te(){}var mg,ng=[];w||gf();for(var X=0;32>X;++X)Yf.push(Array(X));var og=new Float32Array(288);for(X=0;288>=X;++X)V[X]=og.subarray(0,X);var pg=new Int32Array(288);for(X=0;288>=X;++X)fg[X]=pg.subarray(0,X);
(function(){const a=new Map,b=new Map;Te=function(c,d,f){W({m:"setAssociatedObject",M:d,object:f},[f],c)};Pe=function(c){return b.get(c)};Ie=function(c){kg(c,function(d){var f=d.m;if(f)switch(f){case "renderPictures":qg(d.o,d.ha,d.ga,d.s,lg());break;case "onRenderComplete":sg(d.o,d.s,{imageBitmaps:d.da,rasterStartMilliseconds:d.ja,rasterEndMilliseconds:d.ia});break;case "setAssociatedObject":b.set(d.M,d.object);break;case "disposeAssociatedObject":d=d.M;f=b.get(d);f.close&&f.close();b.delete(d);break;
case "disposeSurface":tg(d.o);break;case "rasterizeImage":ug(d.o,d.image,d.format,d.s);break;case "onRasterizeComplete":vg(d.o,d.data,d.s);break;default:console.warn(`unrecognized skwasm message: ${f}`)}})};Ne=function(c,d,f,g,l){W({m:"renderPictures",o:d,ha:f,ga:g,s:l},[],c)};Ke=function(c,d){c=new OffscreenCanvas(c,d);d=Wf(c);a.set(d,c);return d};Re=function(c,d,f){c=a.get(c);c.width=d;c.height=f};He=function(c,d,f,g){g||=[];c=a.get(c);g.push(createImageBitmap(c,0,0,d,f));return g};Se=async function(c,
d,f,g){d=d?await Promise.all(d):[];W({m:"onRenderComplete",o:c,s:g,da:d,ja:f,ia:lg()},[...d])};Je=function(c,d,f){const g=T.D,l=g.createTexture();g.bindTexture(g.TEXTURE_2D,l);g.pixelStorei(g.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!0);g.texImage2D(g.TEXTURE_2D,0,g.RGBA,d,f,0,g.RGBA,g.UNSIGNED_BYTE,c);g.pixelStorei(g.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1);g.bindTexture(g.TEXTURE_2D,null);c=Uf(M);M[c]=l;return c};Oe=function(c,d){W({m:"disposeAssociatedObject",M:d},[],c)};Le=function(c,d){W({m:"disposeSurface",
o:d},[],c)};Me=function(c,d,f,g,l){W({m:"rasterizeImage",o:d,image:f,format:g,s:l},[],c)};Qe=function(c,d,f){W({m:"onRasterizeComplete",o:c,data:d,s:f})}})();
(function(){let a=0;kg=function(b,c){function d({data:f}){const g=f.m;g&&("syncTimeOrigin"==g?a=performance.timeOrigin-f.timeOrigin:c(f))}b?(B[b].addEventListener("message",d),B[b].postMessage({m:"syncTimeOrigin",timeOrigin:performance.timeOrigin})):addEventListener("message",d)};lg=function(){return performance.now()+a};W=function(b,c,d){d?B[d].postMessage(b,c):postMessage(b,c)}})();
var Bf=[Ge,ef,tf,Xa,Ya,Za,$a,jb,kb,lb,oe,pe,re,se,te,ue,ve],Ua,Y=function(){function a(c,d){Y=c.exports;v.wasmExports=Y;ff.push(Y._emscripten_tls_init);I=Y.__indirect_function_table;Ja.unshift(Y.__wasm_call_ctors);ra=d;Na();return Y}var b=za();z++;if(v.instantiateWasm)try{return v.instantiateWasm(b,a)}catch(c){y(`Module.instantiateWasm callback failed with error: ${c}`),ja(c)}Qa??=Pa("skwasm.wasm")?"skwasm.wasm":x+"skwasm.wasm";Ta(b,function(c){a(c.instance,c.module)}).catch(ja);return{}}();
v._canvas_saveLayer=(a,b,c,d,f)=>(v._canvas_saveLayer=Y.canvas_saveLayer)(a,b,c,d,f);v._canvas_save=a=>(v._canvas_save=Y.canvas_save)(a);v._canvas_restore=a=>(v._canvas_restore=Y.canvas_restore)(a);v._canvas_restoreToCount=(a,b)=>(v._canvas_restoreToCount=Y.canvas_restoreToCount)(a,b);v._canvas_getSaveCount=a=>(v._canvas_getSaveCount=Y.canvas_getSaveCount)(a);v._canvas_translate=(a,b,c)=>(v._canvas_translate=Y.canvas_translate)(a,b,c);
v._canvas_scale=(a,b,c)=>(v._canvas_scale=Y.canvas_scale)(a,b,c);v._canvas_rotate=(a,b)=>(v._canvas_rotate=Y.canvas_rotate)(a,b);v._canvas_skew=(a,b,c)=>(v._canvas_skew=Y.canvas_skew)(a,b,c);v._canvas_transform=(a,b)=>(v._canvas_transform=Y.canvas_transform)(a,b);v._canvas_clipRect=(a,b,c,d)=>(v._canvas_clipRect=Y.canvas_clipRect)(a,b,c,d);v._canvas_clipRRect=(a,b,c)=>(v._canvas_clipRRect=Y.canvas_clipRRect)(a,b,c);v._canvas_clipPath=(a,b,c)=>(v._canvas_clipPath=Y.canvas_clipPath)(a,b,c);
v._canvas_drawColor=(a,b,c)=>(v._canvas_drawColor=Y.canvas_drawColor)(a,b,c);v._canvas_drawLine=(a,b,c,d,f,g)=>(v._canvas_drawLine=Y.canvas_drawLine)(a,b,c,d,f,g);v._canvas_drawPaint=(a,b)=>(v._canvas_drawPaint=Y.canvas_drawPaint)(a,b);v._canvas_drawRect=(a,b,c)=>(v._canvas_drawRect=Y.canvas_drawRect)(a,b,c);v._canvas_drawRRect=(a,b,c)=>(v._canvas_drawRRect=Y.canvas_drawRRect)(a,b,c);v._canvas_drawDRRect=(a,b,c,d)=>(v._canvas_drawDRRect=Y.canvas_drawDRRect)(a,b,c,d);
v._canvas_drawOval=(a,b,c)=>(v._canvas_drawOval=Y.canvas_drawOval)(a,b,c);v._canvas_drawCircle=(a,b,c,d,f)=>(v._canvas_drawCircle=Y.canvas_drawCircle)(a,b,c,d,f);v._canvas_drawArc=(a,b,c,d,f,g)=>(v._canvas_drawArc=Y.canvas_drawArc)(a,b,c,d,f,g);v._canvas_drawPath=(a,b,c)=>(v._canvas_drawPath=Y.canvas_drawPath)(a,b,c);v._canvas_drawShadow=(a,b,c,d,f,g)=>(v._canvas_drawShadow=Y.canvas_drawShadow)(a,b,c,d,f,g);
v._canvas_drawParagraph=(a,b,c,d)=>(v._canvas_drawParagraph=Y.canvas_drawParagraph)(a,b,c,d);v._canvas_drawPicture=(a,b)=>(v._canvas_drawPicture=Y.canvas_drawPicture)(a,b);v._canvas_drawImage=(a,b,c,d,f,g)=>(v._canvas_drawImage=Y.canvas_drawImage)(a,b,c,d,f,g);v._canvas_drawImageRect=(a,b,c,d,f,g)=>(v._canvas_drawImageRect=Y.canvas_drawImageRect)(a,b,c,d,f,g);v._canvas_drawImageNine=(a,b,c,d,f,g)=>(v._canvas_drawImageNine=Y.canvas_drawImageNine)(a,b,c,d,f,g);
v._canvas_drawVertices=(a,b,c,d)=>(v._canvas_drawVertices=Y.canvas_drawVertices)(a,b,c,d);v._canvas_drawPoints=(a,b,c,d,f)=>(v._canvas_drawPoints=Y.canvas_drawPoints)(a,b,c,d,f);v._canvas_drawAtlas=(a,b,c,d,f,g,l,m,p)=>(v._canvas_drawAtlas=Y.canvas_drawAtlas)(a,b,c,d,f,g,l,m,p);v._canvas_getTransform=(a,b)=>(v._canvas_getTransform=Y.canvas_getTransform)(a,b);v._canvas_getLocalClipBounds=(a,b)=>(v._canvas_getLocalClipBounds=Y.canvas_getLocalClipBounds)(a,b);
v._canvas_getDeviceClipBounds=(a,b)=>(v._canvas_getDeviceClipBounds=Y.canvas_getDeviceClipBounds)(a,b);v._contourMeasureIter_create=(a,b,c)=>(v._contourMeasureIter_create=Y.contourMeasureIter_create)(a,b,c);v._contourMeasureIter_next=a=>(v._contourMeasureIter_next=Y.contourMeasureIter_next)(a);v._contourMeasureIter_dispose=a=>(v._contourMeasureIter_dispose=Y.contourMeasureIter_dispose)(a);v._contourMeasure_dispose=a=>(v._contourMeasure_dispose=Y.contourMeasure_dispose)(a);
v._contourMeasure_length=a=>(v._contourMeasure_length=Y.contourMeasure_length)(a);v._contourMeasure_isClosed=a=>(v._contourMeasure_isClosed=Y.contourMeasure_isClosed)(a);v._contourMeasure_getPosTan=(a,b,c,d)=>(v._contourMeasure_getPosTan=Y.contourMeasure_getPosTan)(a,b,c,d);v._contourMeasure_getSegment=(a,b,c,d)=>(v._contourMeasure_getSegment=Y.contourMeasure_getSegment)(a,b,c,d);v._skData_create=a=>(v._skData_create=Y.skData_create)(a);v._skData_getPointer=a=>(v._skData_getPointer=Y.skData_getPointer)(a);
v._skData_getConstPointer=a=>(v._skData_getConstPointer=Y.skData_getConstPointer)(a);v._skData_getSize=a=>(v._skData_getSize=Y.skData_getSize)(a);v._skData_dispose=a=>(v._skData_dispose=Y.skData_dispose)(a);v._imageFilter_createBlur=(a,b,c)=>(v._imageFilter_createBlur=Y.imageFilter_createBlur)(a,b,c);v._imageFilter_createDilate=(a,b)=>(v._imageFilter_createDilate=Y.imageFilter_createDilate)(a,b);v._imageFilter_createErode=(a,b)=>(v._imageFilter_createErode=Y.imageFilter_createErode)(a,b);
v._imageFilter_createMatrix=(a,b)=>(v._imageFilter_createMatrix=Y.imageFilter_createMatrix)(a,b);v._imageFilter_createFromColorFilter=a=>(v._imageFilter_createFromColorFilter=Y.imageFilter_createFromColorFilter)(a);v._imageFilter_compose=(a,b)=>(v._imageFilter_compose=Y.imageFilter_compose)(a,b);v._imageFilter_dispose=a=>(v._imageFilter_dispose=Y.imageFilter_dispose)(a);v._imageFilter_getFilterBounds=(a,b)=>(v._imageFilter_getFilterBounds=Y.imageFilter_getFilterBounds)(a,b);
v._colorFilter_createMode=(a,b)=>(v._colorFilter_createMode=Y.colorFilter_createMode)(a,b);v._colorFilter_createMatrix=a=>(v._colorFilter_createMatrix=Y.colorFilter_createMatrix)(a);v._colorFilter_createSRGBToLinearGamma=()=>(v._colorFilter_createSRGBToLinearGamma=Y.colorFilter_createSRGBToLinearGamma)();v._colorFilter_createLinearToSRGBGamma=()=>(v._colorFilter_createLinearToSRGBGamma=Y.colorFilter_createLinearToSRGBGamma)();
v._colorFilter_compose=(a,b)=>(v._colorFilter_compose=Y.colorFilter_compose)(a,b);v._colorFilter_dispose=a=>(v._colorFilter_dispose=Y.colorFilter_dispose)(a);v._maskFilter_createBlur=(a,b)=>(v._maskFilter_createBlur=Y.maskFilter_createBlur)(a,b);v._maskFilter_dispose=a=>(v._maskFilter_dispose=Y.maskFilter_dispose)(a);v._fontCollection_create=()=>(v._fontCollection_create=Y.fontCollection_create)();v._fontCollection_dispose=a=>(v._fontCollection_dispose=Y.fontCollection_dispose)(a);
v._typeface_create=a=>(v._typeface_create=Y.typeface_create)(a);v._typeface_dispose=a=>(v._typeface_dispose=Y.typeface_dispose)(a);v._typefaces_filterCoveredCodePoints=(a,b,c,d)=>(v._typefaces_filterCoveredCodePoints=Y.typefaces_filterCoveredCodePoints)(a,b,c,d);v._fontCollection_registerTypeface=(a,b,c)=>(v._fontCollection_registerTypeface=Y.fontCollection_registerTypeface)(a,b,c);v._fontCollection_clearCaches=a=>(v._fontCollection_clearCaches=Y.fontCollection_clearCaches)(a);
v._image_createFromPicture=(a,b,c)=>(v._image_createFromPicture=Y.image_createFromPicture)(a,b,c);v._image_createFromPixels=(a,b,c,d,f)=>(v._image_createFromPixels=Y.image_createFromPixels)(a,b,c,d,f);v._image_createFromTextureSource=(a,b,c,d)=>(v._image_createFromTextureSource=Y.image_createFromTextureSource)(a,b,c,d);v._image_ref=a=>(v._image_ref=Y.image_ref)(a);v._image_dispose=a=>(v._image_dispose=Y.image_dispose)(a);v._image_getWidth=a=>(v._image_getWidth=Y.image_getWidth)(a);
v._image_getHeight=a=>(v._image_getHeight=Y.image_getHeight)(a);v._paint_create=(a,b,c,d,f,g,l,m)=>(v._paint_create=Y.paint_create)(a,b,c,d,f,g,l,m);v._paint_dispose=a=>(v._paint_dispose=Y.paint_dispose)(a);v._paint_setShader=(a,b)=>(v._paint_setShader=Y.paint_setShader)(a,b);v._paint_setImageFilter=(a,b)=>(v._paint_setImageFilter=Y.paint_setImageFilter)(a,b);v._paint_setColorFilter=(a,b)=>(v._paint_setColorFilter=Y.paint_setColorFilter)(a,b);
v._paint_setMaskFilter=(a,b)=>(v._paint_setMaskFilter=Y.paint_setMaskFilter)(a,b);v._path_create=()=>(v._path_create=Y.path_create)();v._path_dispose=a=>(v._path_dispose=Y.path_dispose)(a);v._path_copy=a=>(v._path_copy=Y.path_copy)(a);v._path_setFillType=(a,b)=>(v._path_setFillType=Y.path_setFillType)(a,b);v._path_getFillType=a=>(v._path_getFillType=Y.path_getFillType)(a);v._path_moveTo=(a,b,c)=>(v._path_moveTo=Y.path_moveTo)(a,b,c);
v._path_relativeMoveTo=(a,b,c)=>(v._path_relativeMoveTo=Y.path_relativeMoveTo)(a,b,c);v._path_lineTo=(a,b,c)=>(v._path_lineTo=Y.path_lineTo)(a,b,c);v._path_relativeLineTo=(a,b,c)=>(v._path_relativeLineTo=Y.path_relativeLineTo)(a,b,c);v._path_quadraticBezierTo=(a,b,c,d,f)=>(v._path_quadraticBezierTo=Y.path_quadraticBezierTo)(a,b,c,d,f);v._path_relativeQuadraticBezierTo=(a,b,c,d,f)=>(v._path_relativeQuadraticBezierTo=Y.path_relativeQuadraticBezierTo)(a,b,c,d,f);
v._path_cubicTo=(a,b,c,d,f,g,l)=>(v._path_cubicTo=Y.path_cubicTo)(a,b,c,d,f,g,l);v._path_relativeCubicTo=(a,b,c,d,f,g,l)=>(v._path_relativeCubicTo=Y.path_relativeCubicTo)(a,b,c,d,f,g,l);v._path_conicTo=(a,b,c,d,f,g)=>(v._path_conicTo=Y.path_conicTo)(a,b,c,d,f,g);v._path_relativeConicTo=(a,b,c,d,f,g)=>(v._path_relativeConicTo=Y.path_relativeConicTo)(a,b,c,d,f,g);v._path_arcToOval=(a,b,c,d,f)=>(v._path_arcToOval=Y.path_arcToOval)(a,b,c,d,f);
v._path_arcToRotated=(a,b,c,d,f,g,l,m)=>(v._path_arcToRotated=Y.path_arcToRotated)(a,b,c,d,f,g,l,m);v._path_relativeArcToRotated=(a,b,c,d,f,g,l,m)=>(v._path_relativeArcToRotated=Y.path_relativeArcToRotated)(a,b,c,d,f,g,l,m);v._path_addRect=(a,b)=>(v._path_addRect=Y.path_addRect)(a,b);v._path_addOval=(a,b)=>(v._path_addOval=Y.path_addOval)(a,b);v._path_addArc=(a,b,c,d)=>(v._path_addArc=Y.path_addArc)(a,b,c,d);v._path_addPolygon=(a,b,c,d)=>(v._path_addPolygon=Y.path_addPolygon)(a,b,c,d);
v._path_addRRect=(a,b)=>(v._path_addRRect=Y.path_addRRect)(a,b);v._path_addPath=(a,b,c,d)=>(v._path_addPath=Y.path_addPath)(a,b,c,d);v._path_close=a=>(v._path_close=Y.path_close)(a);v._path_reset=a=>(v._path_reset=Y.path_reset)(a);v._path_contains=(a,b,c)=>(v._path_contains=Y.path_contains)(a,b,c);v._path_transform=(a,b)=>(v._path_transform=Y.path_transform)(a,b);v._path_getBounds=(a,b)=>(v._path_getBounds=Y.path_getBounds)(a,b);v._path_combine=(a,b,c)=>(v._path_combine=Y.path_combine)(a,b,c);
v._path_getSvgString=a=>(v._path_getSvgString=Y.path_getSvgString)(a);v._pictureRecorder_create=()=>(v._pictureRecorder_create=Y.pictureRecorder_create)();v._pictureRecorder_dispose=a=>(v._pictureRecorder_dispose=Y.pictureRecorder_dispose)(a);v._pictureRecorder_beginRecording=(a,b)=>(v._pictureRecorder_beginRecording=Y.pictureRecorder_beginRecording)(a,b);v._pictureRecorder_endRecording=a=>(v._pictureRecorder_endRecording=Y.pictureRecorder_endRecording)(a);
v._picture_getCullRect=(a,b)=>(v._picture_getCullRect=Y.picture_getCullRect)(a,b);v._picture_dispose=a=>(v._picture_dispose=Y.picture_dispose)(a);v._picture_approximateBytesUsed=a=>(v._picture_approximateBytesUsed=Y.picture_approximateBytesUsed)(a);v._shader_createLinearGradient=(a,b,c,d,f,g)=>(v._shader_createLinearGradient=Y.shader_createLinearGradient)(a,b,c,d,f,g);v._shader_createRadialGradient=(a,b,c,d,f,g,l,m)=>(v._shader_createRadialGradient=Y.shader_createRadialGradient)(a,b,c,d,f,g,l,m);
v._shader_createConicalGradient=(a,b,c,d,f,g,l,m)=>(v._shader_createConicalGradient=Y.shader_createConicalGradient)(a,b,c,d,f,g,l,m);v._shader_createSweepGradient=(a,b,c,d,f,g,l,m,p)=>(v._shader_createSweepGradient=Y.shader_createSweepGradient)(a,b,c,d,f,g,l,m,p);v._shader_dispose=a=>(v._shader_dispose=Y.shader_dispose)(a);v._runtimeEffect_create=a=>(v._runtimeEffect_create=Y.runtimeEffect_create)(a);v._runtimeEffect_dispose=a=>(v._runtimeEffect_dispose=Y.runtimeEffect_dispose)(a);
v._runtimeEffect_getUniformSize=a=>(v._runtimeEffect_getUniformSize=Y.runtimeEffect_getUniformSize)(a);v._shader_createRuntimeEffectShader=(a,b,c,d)=>(v._shader_createRuntimeEffectShader=Y.shader_createRuntimeEffectShader)(a,b,c,d);v._shader_createFromImage=(a,b,c,d,f)=>(v._shader_createFromImage=Y.shader_createFromImage)(a,b,c,d,f);v._skString_allocate=a=>(v._skString_allocate=Y.skString_allocate)(a);v._skString_getData=a=>(v._skString_getData=Y.skString_getData)(a);
v._skString_getLength=a=>(v._skString_getLength=Y.skString_getLength)(a);v._skString_free=a=>(v._skString_free=Y.skString_free)(a);v._skString16_allocate=a=>(v._skString16_allocate=Y.skString16_allocate)(a);v._skString16_getData=a=>(v._skString16_getData=Y.skString16_getData)(a);v._skString16_free=a=>(v._skString16_free=Y.skString16_free)(a);v._surface_create=()=>(v._surface_create=Y.surface_create)();v._surface_getThreadId=a=>(v._surface_getThreadId=Y.surface_getThreadId)(a);
v._surface_setCallbackHandler=(a,b)=>(v._surface_setCallbackHandler=Y.surface_setCallbackHandler)(a,b);v._surface_destroy=a=>(v._surface_destroy=Y.surface_destroy)(a);var tg=v._surface_dispose=a=>(tg=v._surface_dispose=Y.surface_dispose)(a);v._surface_renderPictures=(a,b,c)=>(v._surface_renderPictures=Y.surface_renderPictures)(a,b,c);var qg=v._surface_renderPicturesOnWorker=(a,b,c,d,f)=>(qg=v._surface_renderPicturesOnWorker=Y.surface_renderPicturesOnWorker)(a,b,c,d,f);
v._surface_rasterizeImage=(a,b,c)=>(v._surface_rasterizeImage=Y.surface_rasterizeImage)(a,b,c);var ug=v._surface_rasterizeImageOnWorker=(a,b,c,d)=>(ug=v._surface_rasterizeImageOnWorker=Y.surface_rasterizeImageOnWorker)(a,b,c,d),sg=v._surface_onRenderComplete=(a,b,c)=>(sg=v._surface_onRenderComplete=Y.surface_onRenderComplete)(a,b,c),vg=v._surface_onRasterizeComplete=(a,b,c)=>(vg=v._surface_onRasterizeComplete=Y.surface_onRasterizeComplete)(a,b,c);
v._lineMetrics_create=(a,b,c,d,f,g,l,m,p)=>(v._lineMetrics_create=Y.lineMetrics_create)(a,b,c,d,f,g,l,m,p);v._lineMetrics_dispose=a=>(v._lineMetrics_dispose=Y.lineMetrics_dispose)(a);v._lineMetrics_getHardBreak=a=>(v._lineMetrics_getHardBreak=Y.lineMetrics_getHardBreak)(a);v._lineMetrics_getAscent=a=>(v._lineMetrics_getAscent=Y.lineMetrics_getAscent)(a);v._lineMetrics_getDescent=a=>(v._lineMetrics_getDescent=Y.lineMetrics_getDescent)(a);
v._lineMetrics_getUnscaledAscent=a=>(v._lineMetrics_getUnscaledAscent=Y.lineMetrics_getUnscaledAscent)(a);v._lineMetrics_getHeight=a=>(v._lineMetrics_getHeight=Y.lineMetrics_getHeight)(a);v._lineMetrics_getWidth=a=>(v._lineMetrics_getWidth=Y.lineMetrics_getWidth)(a);v._lineMetrics_getLeft=a=>(v._lineMetrics_getLeft=Y.lineMetrics_getLeft)(a);v._lineMetrics_getBaseline=a=>(v._lineMetrics_getBaseline=Y.lineMetrics_getBaseline)(a);v._lineMetrics_getLineNumber=a=>(v._lineMetrics_getLineNumber=Y.lineMetrics_getLineNumber)(a);
v._lineMetrics_getStartIndex=a=>(v._lineMetrics_getStartIndex=Y.lineMetrics_getStartIndex)(a);v._lineMetrics_getEndIndex=a=>(v._lineMetrics_getEndIndex=Y.lineMetrics_getEndIndex)(a);v._paragraph_dispose=a=>(v._paragraph_dispose=Y.paragraph_dispose)(a);v._paragraph_getWidth=a=>(v._paragraph_getWidth=Y.paragraph_getWidth)(a);v._paragraph_getHeight=a=>(v._paragraph_getHeight=Y.paragraph_getHeight)(a);v._paragraph_getLongestLine=a=>(v._paragraph_getLongestLine=Y.paragraph_getLongestLine)(a);
v._paragraph_getMinIntrinsicWidth=a=>(v._paragraph_getMinIntrinsicWidth=Y.paragraph_getMinIntrinsicWidth)(a);v._paragraph_getMaxIntrinsicWidth=a=>(v._paragraph_getMaxIntrinsicWidth=Y.paragraph_getMaxIntrinsicWidth)(a);v._paragraph_getAlphabeticBaseline=a=>(v._paragraph_getAlphabeticBaseline=Y.paragraph_getAlphabeticBaseline)(a);v._paragraph_getIdeographicBaseline=a=>(v._paragraph_getIdeographicBaseline=Y.paragraph_getIdeographicBaseline)(a);
v._paragraph_getDidExceedMaxLines=a=>(v._paragraph_getDidExceedMaxLines=Y.paragraph_getDidExceedMaxLines)(a);v._paragraph_layout=(a,b)=>(v._paragraph_layout=Y.paragraph_layout)(a,b);v._paragraph_getPositionForOffset=(a,b,c,d)=>(v._paragraph_getPositionForOffset=Y.paragraph_getPositionForOffset)(a,b,c,d);v._paragraph_getClosestGlyphInfoAtCoordinate=(a,b,c,d,f,g)=>(v._paragraph_getClosestGlyphInfoAtCoordinate=Y.paragraph_getClosestGlyphInfoAtCoordinate)(a,b,c,d,f,g);
v._paragraph_getGlyphInfoAt=(a,b,c,d,f)=>(v._paragraph_getGlyphInfoAt=Y.paragraph_getGlyphInfoAt)(a,b,c,d,f);v._paragraph_getWordBoundary=(a,b,c)=>(v._paragraph_getWordBoundary=Y.paragraph_getWordBoundary)(a,b,c);v._paragraph_getLineCount=a=>(v._paragraph_getLineCount=Y.paragraph_getLineCount)(a);v._paragraph_getLineNumberAt=(a,b)=>(v._paragraph_getLineNumberAt=Y.paragraph_getLineNumberAt)(a,b);
v._paragraph_getLineMetricsAtIndex=(a,b)=>(v._paragraph_getLineMetricsAtIndex=Y.paragraph_getLineMetricsAtIndex)(a,b);v._textBoxList_dispose=a=>(v._textBoxList_dispose=Y.textBoxList_dispose)(a);v._textBoxList_getLength=a=>(v._textBoxList_getLength=Y.textBoxList_getLength)(a);v._textBoxList_getBoxAtIndex=(a,b,c)=>(v._textBoxList_getBoxAtIndex=Y.textBoxList_getBoxAtIndex)(a,b,c);v._paragraph_getBoxesForRange=(a,b,c,d,f)=>(v._paragraph_getBoxesForRange=Y.paragraph_getBoxesForRange)(a,b,c,d,f);
v._paragraph_getBoxesForPlaceholders=a=>(v._paragraph_getBoxesForPlaceholders=Y.paragraph_getBoxesForPlaceholders)(a);v._paragraph_getUnresolvedCodePoints=(a,b,c)=>(v._paragraph_getUnresolvedCodePoints=Y.paragraph_getUnresolvedCodePoints)(a,b,c);v._paragraphBuilder_create=(a,b)=>(v._paragraphBuilder_create=Y.paragraphBuilder_create)(a,b);v._paragraphBuilder_dispose=a=>(v._paragraphBuilder_dispose=Y.paragraphBuilder_dispose)(a);
v._paragraphBuilder_addPlaceholder=(a,b,c,d,f,g)=>(v._paragraphBuilder_addPlaceholder=Y.paragraphBuilder_addPlaceholder)(a,b,c,d,f,g);v._paragraphBuilder_addText=(a,b)=>(v._paragraphBuilder_addText=Y.paragraphBuilder_addText)(a,b);v._paragraphBuilder_getUtf8Text=(a,b)=>(v._paragraphBuilder_getUtf8Text=Y.paragraphBuilder_getUtf8Text)(a,b);v._paragraphBuilder_pushStyle=(a,b)=>(v._paragraphBuilder_pushStyle=Y.paragraphBuilder_pushStyle)(a,b);v._paragraphBuilder_pop=a=>(v._paragraphBuilder_pop=Y.paragraphBuilder_pop)(a);
v._paragraphBuilder_build=a=>(v._paragraphBuilder_build=Y.paragraphBuilder_build)(a);v._unicodePositionBuffer_create=a=>(v._unicodePositionBuffer_create=Y.unicodePositionBuffer_create)(a);v._unicodePositionBuffer_getDataPointer=a=>(v._unicodePositionBuffer_getDataPointer=Y.unicodePositionBuffer_getDataPointer)(a);v._unicodePositionBuffer_free=a=>(v._unicodePositionBuffer_free=Y.unicodePositionBuffer_free)(a);v._lineBreakBuffer_create=a=>(v._lineBreakBuffer_create=Y.lineBreakBuffer_create)(a);
v._lineBreakBuffer_getDataPointer=a=>(v._lineBreakBuffer_getDataPointer=Y.lineBreakBuffer_getDataPointer)(a);v._lineBreakBuffer_free=a=>(v._lineBreakBuffer_free=Y.lineBreakBuffer_free)(a);v._paragraphBuilder_setGraphemeBreaksUtf16=(a,b)=>(v._paragraphBuilder_setGraphemeBreaksUtf16=Y.paragraphBuilder_setGraphemeBreaksUtf16)(a,b);v._paragraphBuilder_setWordBreaksUtf16=(a,b)=>(v._paragraphBuilder_setWordBreaksUtf16=Y.paragraphBuilder_setWordBreaksUtf16)(a,b);
v._paragraphBuilder_setLineBreaksUtf16=(a,b)=>(v._paragraphBuilder_setLineBreaksUtf16=Y.paragraphBuilder_setLineBreaksUtf16)(a,b);v._paragraphStyle_create=()=>(v._paragraphStyle_create=Y.paragraphStyle_create)();v._paragraphStyle_dispose=a=>(v._paragraphStyle_dispose=Y.paragraphStyle_dispose)(a);v._paragraphStyle_setTextAlign=(a,b)=>(v._paragraphStyle_setTextAlign=Y.paragraphStyle_setTextAlign)(a,b);
v._paragraphStyle_setTextDirection=(a,b)=>(v._paragraphStyle_setTextDirection=Y.paragraphStyle_setTextDirection)(a,b);v._paragraphStyle_setMaxLines=(a,b)=>(v._paragraphStyle_setMaxLines=Y.paragraphStyle_setMaxLines)(a,b);v._paragraphStyle_setHeight=(a,b)=>(v._paragraphStyle_setHeight=Y.paragraphStyle_setHeight)(a,b);v._paragraphStyle_setTextHeightBehavior=(a,b,c)=>(v._paragraphStyle_setTextHeightBehavior=Y.paragraphStyle_setTextHeightBehavior)(a,b,c);
v._paragraphStyle_setEllipsis=(a,b)=>(v._paragraphStyle_setEllipsis=Y.paragraphStyle_setEllipsis)(a,b);v._paragraphStyle_setStrutStyle=(a,b)=>(v._paragraphStyle_setStrutStyle=Y.paragraphStyle_setStrutStyle)(a,b);v._paragraphStyle_setTextStyle=(a,b)=>(v._paragraphStyle_setTextStyle=Y.paragraphStyle_setTextStyle)(a,b);v._paragraphStyle_setApplyRoundingHack=(a,b)=>(v._paragraphStyle_setApplyRoundingHack=Y.paragraphStyle_setApplyRoundingHack)(a,b);v._strutStyle_create=()=>(v._strutStyle_create=Y.strutStyle_create)();
v._strutStyle_dispose=a=>(v._strutStyle_dispose=Y.strutStyle_dispose)(a);v._strutStyle_setFontFamilies=(a,b,c)=>(v._strutStyle_setFontFamilies=Y.strutStyle_setFontFamilies)(a,b,c);v._strutStyle_setFontSize=(a,b)=>(v._strutStyle_setFontSize=Y.strutStyle_setFontSize)(a,b);v._strutStyle_setHeight=(a,b)=>(v._strutStyle_setHeight=Y.strutStyle_setHeight)(a,b);v._strutStyle_setHalfLeading=(a,b)=>(v._strutStyle_setHalfLeading=Y.strutStyle_setHalfLeading)(a,b);
v._strutStyle_setLeading=(a,b)=>(v._strutStyle_setLeading=Y.strutStyle_setLeading)(a,b);v._strutStyle_setFontStyle=(a,b,c)=>(v._strutStyle_setFontStyle=Y.strutStyle_setFontStyle)(a,b,c);v._strutStyle_setForceStrutHeight=(a,b)=>(v._strutStyle_setForceStrutHeight=Y.strutStyle_setForceStrutHeight)(a,b);v._textStyle_create=()=>(v._textStyle_create=Y.textStyle_create)();v._textStyle_copy=a=>(v._textStyle_copy=Y.textStyle_copy)(a);v._textStyle_dispose=a=>(v._textStyle_dispose=Y.textStyle_dispose)(a);
v._textStyle_setColor=(a,b)=>(v._textStyle_setColor=Y.textStyle_setColor)(a,b);v._textStyle_setDecoration=(a,b)=>(v._textStyle_setDecoration=Y.textStyle_setDecoration)(a,b);v._textStyle_setDecorationColor=(a,b)=>(v._textStyle_setDecorationColor=Y.textStyle_setDecorationColor)(a,b);v._textStyle_setDecorationStyle=(a,b)=>(v._textStyle_setDecorationStyle=Y.textStyle_setDecorationStyle)(a,b);
v._textStyle_setDecorationThickness=(a,b)=>(v._textStyle_setDecorationThickness=Y.textStyle_setDecorationThickness)(a,b);v._textStyle_setFontStyle=(a,b,c)=>(v._textStyle_setFontStyle=Y.textStyle_setFontStyle)(a,b,c);v._textStyle_setTextBaseline=(a,b)=>(v._textStyle_setTextBaseline=Y.textStyle_setTextBaseline)(a,b);v._textStyle_clearFontFamilies=a=>(v._textStyle_clearFontFamilies=Y.textStyle_clearFontFamilies)(a);
v._textStyle_addFontFamilies=(a,b,c)=>(v._textStyle_addFontFamilies=Y.textStyle_addFontFamilies)(a,b,c);v._textStyle_setFontSize=(a,b)=>(v._textStyle_setFontSize=Y.textStyle_setFontSize)(a,b);v._textStyle_setLetterSpacing=(a,b)=>(v._textStyle_setLetterSpacing=Y.textStyle_setLetterSpacing)(a,b);v._textStyle_setWordSpacing=(a,b)=>(v._textStyle_setWordSpacing=Y.textStyle_setWordSpacing)(a,b);v._textStyle_setHeight=(a,b)=>(v._textStyle_setHeight=Y.textStyle_setHeight)(a,b);
v._textStyle_setHalfLeading=(a,b)=>(v._textStyle_setHalfLeading=Y.textStyle_setHalfLeading)(a,b);v._textStyle_setLocale=(a,b)=>(v._textStyle_setLocale=Y.textStyle_setLocale)(a,b);v._textStyle_setBackground=(a,b)=>(v._textStyle_setBackground=Y.textStyle_setBackground)(a,b);v._textStyle_setForeground=(a,b)=>(v._textStyle_setForeground=Y.textStyle_setForeground)(a,b);v._textStyle_addShadow=(a,b,c,d,f)=>(v._textStyle_addShadow=Y.textStyle_addShadow)(a,b,c,d,f);
v._textStyle_addFontFeature=(a,b,c)=>(v._textStyle_addFontFeature=Y.textStyle_addFontFeature)(a,b,c);v._textStyle_setFontVariations=(a,b,c,d)=>(v._textStyle_setFontVariations=Y.textStyle_setFontVariations)(a,b,c,d);v._vertices_create=(a,b,c,d,f,g,l)=>(v._vertices_create=Y.vertices_create)(a,b,c,d,f,g,l);v._vertices_dispose=a=>(v._vertices_dispose=Y.vertices_dispose)(a);v._skwasm_isMultiThreaded=()=>(v._skwasm_isMultiThreaded=Y.skwasm_isMultiThreaded)();
var ya=()=>(ya=Y.pthread_self)(),xf=a=>(xf=Y.malloc)(a),Ba=(a,b,c,d,f,g)=>(Ba=Y._emscripten_thread_init)(a,b,c,d,f,g),Ha=()=>(Ha=Y._emscripten_thread_crashed)(),bf=(a,b,c,d,f)=>(bf=Y._emscripten_run_on_main_thread_js)(a,b,c,d,f),jf=a=>(jf=Y._emscripten_thread_free_data)(a),pf=a=>(pf=Y._emscripten_thread_exit)(a),Df=(a,b)=>(Df=Y._emscripten_timeout)(a,b),zf=()=>(zf=Y._emscripten_check_mailbox)(),Z=(a,b)=>(Z=Y.setThrew)(a,b),of=(a,b)=>(of=Y.emscripten_stack_set_limits)(a,b),G=a=>(G=Y._emscripten_stack_restore)(a),
$e=a=>($e=Y._emscripten_stack_alloc)(a),E=()=>(E=Y.emscripten_stack_get_current)();function xe(a,b,c){var d=E();try{return I.get(a)(b,c)}catch(f){G(d);if(f!==f+0)throw f;Z(1,0)}}function Ce(a,b,c){var d=E();try{I.get(a)(b,c)}catch(f){G(d);if(f!==f+0)throw f;Z(1,0)}}function we(a,b){var c=E();try{return I.get(a)(b)}catch(d){G(c);if(d!==d+0)throw d;Z(1,0)}}function De(a,b,c,d){var f=E();try{I.get(a)(b,c,d)}catch(g){G(f);if(g!==g+0)throw g;Z(1,0)}}
function ye(a,b,c,d){var f=E();try{return I.get(a)(b,c,d)}catch(g){G(f);if(g!==g+0)throw g;Z(1,0)}}function Ee(a,b,c,d,f){var g=E();try{I.get(a)(b,c,d,f)}catch(l){G(g);if(l!==l+0)throw l;Z(1,0)}}function Fe(a,b,c,d,f,g,l,m){var p=E();try{I.get(a)(b,c,d,f,g,l,m)}catch(r){G(p);if(r!==r+0)throw r;Z(1,0)}}function Be(a,b){var c=E();try{I.get(a)(b)}catch(d){G(c);if(d!==d+0)throw d;Z(1,0)}}function Ae(a,b,c,d,f,g,l){var m=E();try{return I.get(a)(b,c,d,f,g,l)}catch(p){G(m);if(p!==p+0)throw p;Z(1,0)}}
function ze(a,b,c,d,f){var g=E();try{return I.get(a)(b,c,d,f)}catch(l){G(g);if(l!==l+0)throw l;Z(1,0)}}v.wasmMemory=e;v.wasmExports=Y;v.stackAlloc=af;
v.addFunction=(a,b)=>{if(!mg){mg=new WeakMap;var c=I.length;if(mg)for(var d=0;d<0+c;d++){var f=I.get(d);f&&mg.set(f,d)}}if(c=mg.get(a)||0)return c;if(ng.length)c=ng.pop();else{try{I.grow(1)}catch(m){if(!(m instanceof RangeError))throw m;throw"Unable to grow wasm table. Set ALLOW_TABLE_GROWTH.";}c=I.length-1}try{I.set(c,a)}catch(m){if(!(m instanceof TypeError))throw m;if("function"==typeof WebAssembly.Function){d=WebAssembly.Function;f={i:"i32",j:"i64",f:"f32",d:"f64",e:"externref",p:"i32"};for(var g=
{parameters:[],results:"v"==b[0]?[]:[f[b[0]]]},l=1;l<b.length;++l)g.parameters.push(f[b[l]]);b=new d(g,a)}else{d=[1];f=b.slice(0,1);b=b.slice(1);g={i:127,p:127,j:126,f:125,d:124,e:111};d.push(96);l=b.length;128>l?d.push(l):d.push(l%128|128,l>>7);for(l=0;l<b.length;++l)d.push(g[b[l]]);"v"==f?d.push(0):d.push(1,g[f]);b=[0,97,115,109,1,0,0,0,1];f=d.length;128>f?b.push(f):b.push(f%128|128,f>>7);b.push(...d);b.push(2,7,1,1,101,1,102,0,0,7,5,1,1,102,0,0);b=new WebAssembly.Module(new Uint8Array(b));b=(new WebAssembly.Instance(b,
{e:{f:a}})).exports.f}I.set(c,b)}mg.set(a,c);return c};var wg,xg;Ma=function yg(){wg||zg();wg||(Ma=yg)};function zg(){if(!(0<z))if(w)ia(v),w||nf(Ja),startWorker(v);else{if(!xg&&(xg=1,nf(Ia),0<z))return;wg||(wg=1,v.calledRun=1,sa||(w||nf(Ja),ia(v),w||nf(Ka)))}}zg();moduleRtn=ka;


  return moduleRtn;
}
);
})();
export default skwasm;
var isPthread = globalThis.self?.name?.startsWith('em-pthread');
// When running as a pthread, construct a new instance on startup
isPthread && skwasm();
