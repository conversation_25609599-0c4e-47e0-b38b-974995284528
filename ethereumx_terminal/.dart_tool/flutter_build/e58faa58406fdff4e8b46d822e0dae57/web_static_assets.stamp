{"inputs": ["/home/<USER>/snap/flutter/common/flutter/bin/internal/engine.version"], "outputs": ["/home/<USER>/Desktop/Project/ethereumx_terminal/.dart_tool/flutter_build/e58faa58406fdff4e8b46d822e0dae57/flutter.js", "/home/<USER>/Desktop/Project/ethereumx_terminal/.dart_tool/flutter_build/e58faa58406fdff4e8b46d822e0dae57/canvaskit/skwasm_st.js", "/home/<USER>/Desktop/Project/ethereumx_terminal/.dart_tool/flutter_build/e58faa58406fdff4e8b46d822e0dae57/canvaskit/skwasm.js", "/home/<USER>/Desktop/Project/ethereumx_terminal/.dart_tool/flutter_build/e58faa58406fdff4e8b46d822e0dae57/canvaskit/skwasm.js.symbols", "/home/<USER>/Desktop/Project/ethereumx_terminal/.dart_tool/flutter_build/e58faa58406fdff4e8b46d822e0dae57/canvaskit/chromium/canvaskit.js", "/home/<USER>/Desktop/Project/ethereumx_terminal/.dart_tool/flutter_build/e58faa58406fdff4e8b46d822e0dae57/canvaskit/chromium/canvaskit.js.symbols", "/home/<USER>/Desktop/Project/ethereumx_terminal/.dart_tool/flutter_build/e58faa58406fdff4e8b46d822e0dae57/canvaskit/chromium/canvaskit.wasm", "/home/<USER>/Desktop/Project/ethereumx_terminal/.dart_tool/flutter_build/e58faa58406fdff4e8b46d822e0dae57/canvaskit/canvaskit.js", "/home/<USER>/Desktop/Project/ethereumx_terminal/.dart_tool/flutter_build/e58faa58406fdff4e8b46d822e0dae57/canvaskit/skwasm_st.wasm", "/home/<USER>/Desktop/Project/ethereumx_terminal/.dart_tool/flutter_build/e58faa58406fdff4e8b46d822e0dae57/canvaskit/skwasm.wasm", "/home/<USER>/Desktop/Project/ethereumx_terminal/.dart_tool/flutter_build/e58faa58406fdff4e8b46d822e0dae57/canvaskit/skwasm_st.js.symbols", "/home/<USER>/Desktop/Project/ethereumx_terminal/.dart_tool/flutter_build/e58faa58406fdff4e8b46d822e0dae57/canvaskit/canvaskit.js.symbols", "/home/<USER>/Desktop/Project/ethereumx_terminal/.dart_tool/flutter_build/e58faa58406fdff4e8b46d822e0dae57/canvaskit/canvaskit.wasm"]}