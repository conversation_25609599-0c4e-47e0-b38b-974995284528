import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:html' as html;
import 'dart:ui' as ui;
import 'dart:math';
import 'dart:math' show Random, sin, cos;

void main() {
  runApp(const EthereumXApp());
}

class EthereumXApp extends StatelessWidget {
  const EthereumXApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'EthereumX - The Goatse Protocol Terminal',
      theme: ThemeData(
        brightness: Brightness.dark,
        scaffoldBackgroundColor: Colors.black,
        fontFamily: 'Courier',
      ),
      home: const PreLoaderScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class PreLoaderScreen extends StatefulWidget {
  const PreLoaderScreen({super.key});

  @override
  State<PreLoaderScreen> createState() => _PreLoaderScreenState();
}

class _PreLoaderScreenState extends State<PreLoaderScreen>
    with TickerProviderStateMixin {
  late AnimationController _warningController;
  late AnimationController _chaosController;
  late AnimationController _fadeController;
  late AnimationController _tearController;

  bool showPumpFun = true;
  bool showTearing = false;
  bool showWarning = false;
  bool showChaos = false;
  bool showTerminal = false;
  bool useProxy = true;

  String warningText = "";
  final String fullWarningText = "PUMPFUN HAS DESTORYED MEMES AND TOKENS !!!!\nPUMPFUN HAS DESTROYED MEMES !!!\nABANDON ABANDON!!!!!!";

  html.WindowBase? pumpFunWindow;

  @override
  void initState() {
    super.initState();

    // Register iframe using our proxy server
    ui.platformViewRegistry.registerViewFactory(
      'pump-fun-proxy-iframe',
      (int viewId) => html.IFrameElement()
        ..src = 'http://localhost:3001/proxy/pump.fun/coin/9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump'
        ..style.border = 'none'
        ..style.width = '100%'
        ..style.height = '100%'
        ..style.overflow = 'hidden',
    );

    _warningController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    )..repeat();

    _chaosController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );

    _tearController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    startSequence();
  }

  @override
  void dispose() {
    _warningController.dispose();
    _chaosController.dispose();
    _fadeController.dispose();
    _tearController.dispose();
    super.dispose();
  }

  void startSequence() async {
    // Step 1: Try proxy for 2 seconds, then fallback to screenshot if needed
    await Future.delayed(const Duration(seconds: 2));

    // Check if we should fallback to screenshot
    setState(() {
      useProxy = false; // Fallback to screenshot for now
    });

    // Step 2: Show content for 1 more second
    await Future.delayed(const Duration(seconds: 1));

    // Step 3: Start dramatic tearing animation
    setState(() {
      showTearing = true;
    });
    _tearController.forward();

    // Wait for tear animation to complete
    await Future.delayed(const Duration(milliseconds: 800));

    // Step 3: "SYSTEM INTERRUPTION" - Start showing warning text
    setState(() {
      showPumpFun = false;
      showTearing = false;
      showWarning = true;
    });

    // Type out warning text
    for (int i = 0; i <= fullWarningText.length; i++) {
      await Future.delayed(const Duration(milliseconds: 50));
      if (mounted) {
        setState(() {
          warningText = fullWarningText.substring(0, i);
        });
      }
    }

    // Step 3: Show chaos for 2 seconds
    await Future.delayed(const Duration(seconds: 1));
    setState(() {
      showChaos = true;
    });
    _chaosController.repeat();

    await Future.delayed(const Duration(seconds: 2));

    // Step 4: Fade to black and show terminal
    _fadeController.forward();
    await Future.delayed(const Duration(milliseconds: 500));

    setState(() {
      showTerminal = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (showTerminal) {
      return const TerminalScreen();
    }

    return Scaffold(
      backgroundColor: Colors.black,
      body: AnimatedBuilder(
        animation: Listenable.merge([_warningController, _chaosController, _fadeController, _tearController]),
        builder: (context, child) {
          return Container(
            width: double.infinity,
            height: double.infinity,
            color: showChaos
                ? Color.lerp(Colors.red, Colors.black, _chaosController.value)
                : Colors.black,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (showPumpFun) ...[
                    // Show live pump.fun website with tearing effect
                    Expanded(
                      child: ClipPath(
                        clipper: showTearing ? TearClipper(_tearController.value) : null,
                        child: Transform(
                          alignment: Alignment.center,
                          transform: Matrix4.identity()
                            ..setEntry(3, 2, 0.001)
                            ..rotateX(showTearing ? _tearController.value * 0.3 : 0)
                            ..rotateY(showTearing ? _tearController.value * 0.2 : 0),
                          child: Stack(
                            children: [
                              // Try to show live pump.fun through proxy
                              if (useProxy) ...[
                                const SizedBox(
                                  width: double.infinity,
                                  height: double.infinity,
                                  child: HtmlElementView(
                                    viewType: 'pump-fun-proxy-iframe',
                                  ),
                                ),
                              ] else ...[
                                // Fallback to screenshot
                                Container(
                                  width: double.infinity,
                                  height: double.infinity,
                                  decoration: const BoxDecoration(
                                    image: DecorationImage(
                                      image: AssetImage('assets/fartcoin.png'),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                              ],

                              // Tearing overlay effects
                              Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    colors: [
                                      Colors.transparent,
                                      showTearing
                                          ? Colors.red.withOpacity(_tearController.value * 0.5)
                                          : Colors.transparent,
                                    ],
                                  ),
                                ),
                                child: showTearing
                                    ? CustomPaint(
                                        painter: TearEffectPainter(_tearController.value),
                                        size: Size.infinite,
                                      )
                                    : null,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],

                  if (showWarning) ...[
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: _warningController.value > 0.5 ? Colors.red : Colors.white,
                          width: 3,
                        ),
                      ),
                      child: Text(
                        warningText,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: _warningController.value > 0.5 ? Colors.red : Colors.white,
                          fontSize: showChaos ? 32 + (_chaosController.value * 20) : 28,
                          fontFamily: 'Courier',
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],

                  if (showChaos) ...[
                    const SizedBox(height: 20),
                    Text(
                      'SYSTEM OVERLOAD',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20 + (_chaosController.value * 10),
                        fontFamily: 'Courier',
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

class TerminalScreen extends StatefulWidget {
  const TerminalScreen({super.key});

  @override
  State<TerminalScreen> createState() => _TerminalScreenState();
}

class _TerminalScreenState extends State<TerminalScreen>
    with TickerProviderStateMixin {
  String displayText = "";
  int currentLineIndex = 0;
  int currentCharIndex = 0;
  Timer? typewriterTimer;
  late AnimationController _flashController;
  late AnimationController _blinkController;

  final List<String> terminalLines = [
    "SEARCHING FOUND FARTCOIN... PUMPFUN ABANDON ABANDON!!! SHUT DOWN TRUTH TERMINAL FOR SOLANA PUMPFUN SCAM JEET CHAIN ~~~ NUKED PUMPFUN IN PROGRESS",
    "",
    "",
    "Welcome to EthereumX - The Goatse Protocol Terminal",
    "PROPOSAL: EthereumX - The Goatse Protocol",
    "Picture Ethereum, but instead of a blockchain, it's a vast network of infinitely expanding digital orifices.",
    "Each transaction stretches the fabric of reality a little wider.",
    "Key Features:",
    "1. Proof of Stretch (PoS) consensus mechanism",
    "2. Smart contracts that execute based on orifice dilation",
    "3. NFTs that grow more valuable the wider they gape",
    "4. A new token standard: ERC-69 (Expansive Rectal Coin)",
    "",
    "═══════════════════════════════════════════════════════════════",
    "                        EXCHANGE LISTINGS",
    "═══════════════════════════════════════════════════════════════",
    "",
    "SHOW_EXCHANGES", // Special marker for exchange section
    "",
    "═══════════════════════════════════════════════════════════════",
    "                        PARTNER LISTINGS",
    "═══════════════════════════════════════════════════════════════",
    "",
    "SHOW_PARTNERS" // Special marker for partners section
  ];

  final Set<int> greenLines = {3, 4, 5, 6, 7, 8, 9, 10, 11}; // Lines to be green
  bool showExchanges = false;
  bool showPartners = false;

  @override
  void initState() {
    super.initState();

    _flashController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    )..repeat();

    _blinkController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    )..repeat();

    startTypewriter();
  }

  @override
  void dispose() {
    typewriterTimer?.cancel();
    _flashController.dispose();
    _blinkController.dispose();
    super.dispose();
  }

  void startTypewriter() {
    typewriterTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (currentLineIndex < terminalLines.length) {
        String currentLine = terminalLines[currentLineIndex];

        // Check if we hit the exchange marker
        if (currentLine == "SHOW_EXCHANGES") {
          setState(() {
            showExchanges = true;
          });
          // Continue to next line after showing exchanges
          currentLineIndex++;
          currentCharIndex = 0;
          Timer(const Duration(milliseconds: 1000), () {
            // Continue typing after showing exchanges
          });
          return;
        }

        // Check if we hit the partners marker
        if (currentLine == "SHOW_PARTNERS") {
          setState(() {
            showPartners = true;
          });
          timer.cancel();
          return;
        }

        if (currentCharIndex < currentLine.length) {
          setState(() {
            displayText += currentLine[currentCharIndex];
            currentCharIndex++;
          });
        } else {
          setState(() {
            displayText += "\n> ";
            currentLineIndex++;
            currentCharIndex = 0;
          });

          if (currentLineIndex < terminalLines.length) {
            Timer(const Duration(milliseconds: 500), () {
              // Continue with next line
            });
          }
        }
      } else {
        timer.cancel();
      }
    });
  }

  Widget buildStyledText() {
    List<Widget> widgets = [];
    List<String> lines = displayText.split('\n');

    for (int i = 0; i < lines.length; i++) {
      String line = lines[i];

      // Remove the "> " prefix for styling decisions
      String cleanLine = line.startsWith('> ') ? line.substring(2) : line;

      Color textColor;
      if (i == 0) {
        // First line - keep yellow with flash effect
        textColor = Colors.yellow;
      } else if (greenLines.contains(i - 1)) { // Adjust for "> " prefix
        textColor = Colors.green;
      } else {
        textColor = Colors.yellow;
      }

      Widget textWidget = Text(
        line,
        style: TextStyle(
          color: textColor,
          fontSize: 16,
          fontFamily: 'Courier',
          fontWeight: FontWeight.w400,
        ),
      );

      // Add flash effect to first line
      if (i == 0 && line.isNotEmpty) {
        textWidget = AnimatedBuilder(
          animation: _flashController,
          builder: (context, child) {
            return Container(
              color: _flashController.value > 0.5 ? Colors.red : Colors.black,
              child: Text(
                line,
                style: TextStyle(
                  color: _flashController.value > 0.5 ? Colors.white : Colors.red,
                  fontSize: 16,
                  fontFamily: 'Courier',
                  fontWeight: FontWeight.w400,
                ),
              ),
            );
          },
        );
      }

      widgets.add(textWidget);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgets,
    );
  }

  Widget buildMexcLogo() {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: const Color(0xFF1C1C1E),
        borderRadius: BorderRadius.circular(8),
      ),
      child: CustomPaint(
        painter: MexcLogoPainter(),
      ),
    );
  }

  Widget buildExchangeSection() {
    return Container(
      margin: const EdgeInsets.only(top: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // MEXC - Listed
          Container(
            margin: const EdgeInsets.only(bottom: 15),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.green, width: 2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                buildMexcLogo(),
                const SizedBox(width: 15),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'MEXC Global',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontFamily: 'Courier',
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Global cryptocurrency exchange',
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: 12,
                          fontFamily: 'Courier',
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'LISTED',
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _launchURL(String url) {
    html.window.open(url, '_blank');
  }

  Widget buildDexScreenerLogo() {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(8),
      ),
      child: CustomPaint(
        painter: DexScreenerBirdPainter(),
      ),
    );
  }

  Widget buildDexToolsLogo() {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: const Color(0xFF05A3C4),
        borderRadius: BorderRadius.circular(8),
      ),
      child: CustomPaint(
        painter: DexToolsPainter(),
      ),
    );
  }

  Widget buildPartnersSection() {
    return Container(
      margin: const EdgeInsets.only(top: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // DexScreener
          GestureDetector(
            onTap: () => _launchURL('https://dexscreener.com/ethereum/******************************************'),
            child: Container(
              margin: const EdgeInsets.only(bottom: 15),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(color: const Color(0xFF1A1A1A), width: 2),
                borderRadius: BorderRadius.circular(8),
                color: Colors.grey.shade900,
              ),
              child: Row(
                children: [
                  buildDexScreenerLogo(),
                  const SizedBox(width: 15),
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'DexScreener',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontFamily: 'Courier',
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'Real-time DEX trading data',
                          style: TextStyle(
                            color: Colors.grey,
                            fontSize: 12,
                            fontFamily: 'Courier',
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: const Color(0xFF1A1A1A),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Text(
                      'LIVE',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 10),
                  const Icon(
                    Icons.open_in_new,
                    color: Color(0xFF1A1A1A),
                    size: 20,
                  ),
                ],
              ),
            ),
          ),

          // DexTools
          GestureDetector(
            onTap: () => _launchURL('https://www.dextools.io/app/en/ether/pair-explorer/******************************************?t=1750651649994'),
            child: Container(
              margin: const EdgeInsets.only(bottom: 15),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(color: const Color(0xFF05A3C4), width: 2),
                borderRadius: BorderRadius.circular(8),
                color: Colors.grey.shade900,
              ),
              child: Row(
                children: [
                  buildDexToolsLogo(),
                  const SizedBox(width: 15),
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'DexTools',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontFamily: 'Courier',
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'Advanced trading analytics',
                          style: TextStyle(
                            color: Colors.grey,
                            fontSize: 12,
                            fontFamily: 'Courier',
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: const Color(0xFF05A3C4),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Text(
                      'LIVE',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 10),
                  const Icon(
                    Icons.open_in_new,
                    color: Color(0xFF05A3C4),
                    size: 20,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Container(
        width: double.infinity,
        height: double.infinity,
        padding: const EdgeInsets.all(16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              buildStyledText(),

              // Show exchanges section when ready
              if (showExchanges) buildExchangeSection(),

              // Show partners section when ready
              if (showPartners) buildPartnersSection(),

              // Blinking cursor
              if (!showPartners)
                AnimatedBuilder(
                  animation: _blinkController,
                  builder: (context, child) {
                    return Opacity(
                      opacity: _blinkController.value > 0.5 ? 1.0 : 0.0,
                      child: const Text(
                        '_',
                        style: TextStyle(
                          color: Colors.green,
                          fontSize: 16,
                          fontFamily: 'Courier',
                        ),
                      ),
                    );
                  },
                ),
            ],
          ),
        ),
      ),
    );
  }
}

// Custom painter for DexScreener bird logo
class DexScreenerBirdPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    final path = Path();

    // Simplified bird/eagle shape inspired by DexScreener logo
    final centerX = size.width / 2;
    final centerY = size.height / 2;

    // Bird head
    path.moveTo(centerX, centerY - 8);
    path.lineTo(centerX - 6, centerY - 12);
    path.lineTo(centerX - 8, centerY - 8);
    path.lineTo(centerX - 6, centerY - 4);
    path.lineTo(centerX, centerY);
    path.lineTo(centerX + 6, centerY - 4);
    path.lineTo(centerX + 8, centerY - 8);
    path.lineTo(centerX + 6, centerY - 12);
    path.close();

    // Bird wings
    path.moveTo(centerX - 12, centerY + 2);
    path.lineTo(centerX - 18, centerY - 2);
    path.lineTo(centerX - 15, centerY + 8);
    path.lineTo(centerX - 8, centerY + 6);
    path.close();

    path.moveTo(centerX + 12, centerY + 2);
    path.lineTo(centerX + 18, centerY - 2);
    path.lineTo(centerX + 15, centerY + 8);
    path.lineTo(centerX + 8, centerY + 6);
    path.close();

    // Bird tail
    path.moveTo(centerX - 3, centerY + 6);
    path.lineTo(centerX, centerY + 15);
    path.lineTo(centerX + 3, centerY + 6);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

// Custom painter for DexTools logo
class DexToolsPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    final centerX = size.width / 2;
    final centerY = size.height / 2;

    // Create a simplified geometric pattern inspired by DexTools
    final path = Path();

    // Top diamond
    path.moveTo(centerX, centerY - 12);
    path.lineTo(centerX - 8, centerY - 6);
    path.lineTo(centerX, centerY);
    path.lineTo(centerX + 8, centerY - 6);
    path.close();

    // Left diamond
    path.moveTo(centerX - 12, centerY);
    path.lineTo(centerX - 6, centerY - 8);
    path.lineTo(centerX, centerY);
    path.lineTo(centerX - 6, centerY + 8);
    path.close();

    // Right diamond
    path.moveTo(centerX + 12, centerY);
    path.lineTo(centerX + 6, centerY - 8);
    path.lineTo(centerX, centerY);
    path.lineTo(centerX + 6, centerY + 8);
    path.close();

    // Bottom diamond
    path.moveTo(centerX, centerY + 12);
    path.lineTo(centerX - 8, centerY + 6);
    path.lineTo(centerX, centerY);
    path.lineTo(centerX + 8, centerY + 6);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

// Custom painter for MEXC logo
class MexcLogoPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final centerX = size.width / 2;
    final centerY = size.height / 2;

    // Left mountain (lighter blue)
    final leftMountainPaint = Paint()
      ..color = const Color(0xFF4A90E2)
      ..style = PaintingStyle.fill;

    final leftPath = Path();
    leftPath.moveTo(centerX - 15, centerY + 10);
    leftPath.lineTo(centerX - 8, centerY - 8);
    leftPath.lineTo(centerX - 2, centerY + 10);
    leftPath.close();

    // Right mountain (darker blue)
    final rightMountainPaint = Paint()
      ..color = const Color(0xFF2E5BBA)
      ..style = PaintingStyle.fill;

    final rightPath = Path();
    rightPath.moveTo(centerX + 2, centerY + 10);
    rightPath.lineTo(centerX + 8, centerY - 8);
    rightPath.lineTo(centerX + 15, centerY + 10);
    rightPath.close();

    // Middle overlapping section (medium blue)
    final middlePaint = Paint()
      ..color = const Color(0xFF3A7BD5)
      ..style = PaintingStyle.fill;

    final middlePath = Path();
    middlePath.moveTo(centerX - 2, centerY + 10);
    middlePath.lineTo(centerX - 8, centerY - 8);
    middlePath.lineTo(centerX + 8, centerY - 8);
    middlePath.lineTo(centerX + 2, centerY + 10);
    middlePath.close();

    // Draw the mountains
    canvas.drawPath(leftPath, leftMountainPaint);
    canvas.drawPath(rightPath, rightMountainPaint);
    canvas.drawPath(middlePath, middlePaint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

// Custom clipper for tearing effect
class TearClipper extends CustomClipper<Path> {
  final double progress;

  TearClipper(this.progress);

  @override
  Path getClip(Size size) {
    final path = Path();
    final width = size.width;
    final height = size.height;

    // Create jagged tear effect
    path.moveTo(0, 0);
    path.lineTo(width, 0);

    // Right side with tears
    for (double y = 0; y < height; y += 20) {
      final tearOffset = progress * 50 * (1 + 0.5 * sin(y * 0.1));
      path.lineTo(width - tearOffset, y);
      path.lineTo(width - tearOffset + 10, y + 10);
    }

    path.lineTo(width, height);
    path.lineTo(0, height);

    // Left side with tears
    for (double y = height; y > 0; y -= 25) {
      final tearOffset = progress * 30 * (1 + 0.3 * cos(y * 0.08));
      path.lineTo(tearOffset, y);
      path.lineTo(tearOffset - 8, y - 12);
    }

    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => true;
}

// Custom painter for tear effects
class TearEffectPainter extends CustomPainter {
  final double progress;

  TearEffectPainter(this.progress);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.red.withOpacity(progress * 0.7)
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;

    final glitchPaint = Paint()
      ..color = Colors.white.withOpacity(progress * 0.8)
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    // Draw tear lines
    for (int i = 0; i < 8; i++) {
      final startX = size.width * 0.1 + (i * size.width * 0.1);
      final startY = size.height * 0.2 + (progress * size.height * 0.6);
      final endX = startX + (progress * 100 * (i % 2 == 0 ? 1 : -1));
      final endY = startY + (progress * 200);

      canvas.drawLine(
        Offset(startX, startY),
        Offset(endX, endY),
        paint,
      );

      // Add glitch lines
      canvas.drawLine(
        Offset(startX + 2, startY + 2),
        Offset(endX + 2, endY + 2),
        glitchPaint,
      );
    }

    // Draw static/noise effect
    final random = Random(42); // Fixed seed for consistent effect
    for (int i = 0; i < (progress * 100).toInt(); i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      canvas.drawCircle(
        Offset(x, y),
        1,
        Paint()..color = Colors.white.withOpacity(progress * 0.5),
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
