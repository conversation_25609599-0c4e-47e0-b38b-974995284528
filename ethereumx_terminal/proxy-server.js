const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const cors = require('cors');

const app = express();
const PORT = 3001;

// Enable CORS for all routes
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['*']
}));

// Simple proxy that forwards everything to pump.fun without modification
const pumpFunProxy = createProxyMiddleware({
  target: 'https://pump.fun',
  changeOrigin: true,
  secure: true,
  followRedirects: true,
  logLevel: 'debug',
  pathRewrite: {
    '^/proxy/pump.fun': '', // Remove /proxy/pump.fun from the path
  },
  onProxyReq: function (proxyReq, req, res) {
    console.log(`Proxying request: ${req.method} ${req.url} -> https://pump.fun${req.url.replace('/proxy/pump.fun', '')}`);

    // Set proper headers to mimic a real browser request
    proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
    proxyReq.setHeader('Accept', 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8');
    proxyReq.setHeader('Accept-Language', 'en-US,en;q=0.9');
    proxyReq.setHeader('Cache-Control', 'no-cache');
    proxyReq.setHeader('Pragma', 'no-cache');
    proxyReq.setHeader('Sec-Fetch-Dest', 'document');
    proxyReq.setHeader('Sec-Fetch-Mode', 'navigate');
    proxyReq.setHeader('Sec-Fetch-Site', 'none');
    proxyReq.setHeader('Upgrade-Insecure-Requests', '1');
  },
  onProxyRes: function (proxyRes, req, res) {
    console.log(`Response from pump.fun: ${proxyRes.statusCode} for ${req.url}`);

    // Remove ALL headers that could prevent iframe embedding
    delete proxyRes.headers['x-frame-options'];
    delete proxyRes.headers['X-Frame-Options'];
    delete proxyRes.headers['content-security-policy'];
    delete proxyRes.headers['Content-Security-Policy'];
    delete proxyRes.headers['x-content-type-options'];
    delete proxyRes.headers['X-Content-Type-Options'];
    delete proxyRes.headers['referrer-policy'];
    delete proxyRes.headers['Referrer-Policy'];
    delete proxyRes.headers['strict-transport-security'];
    delete proxyRes.headers['Strict-Transport-Security'];

    // Set permissive CORS headers
    proxyRes.headers['Access-Control-Allow-Origin'] = '*';
    proxyRes.headers['Access-Control-Allow-Methods'] = '*';
    proxyRes.headers['Access-Control-Allow-Headers'] = '*';
    proxyRes.headers['Access-Control-Allow-Credentials'] = 'true';

    // Explicitly allow iframe embedding
    proxyRes.headers['X-Frame-Options'] = 'ALLOWALL';

    // Set cache headers to ensure fresh content
    proxyRes.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate';
    proxyRes.headers['Pragma'] = 'no-cache';
    proxyRes.headers['Expires'] = '0';
  },
  onError: function (err, req, res) {
    console.error('Proxy error:', err.message);
    res.status(500).json({
      error: 'Proxy error occurred',
      message: err.message,
      url: req.url
    });
  }
});

// Use the proxy for /proxy/pump.fun routes
app.use('/proxy/pump.fun', pumpFunProxy);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Proxy server is running' });
});

// Start the server
app.listen(PORT, () => {
  console.log(`Proxy server running on http://localhost:${PORT}`);
  console.log(`Pump.fun proxy available at: http://localhost:${PORT}/proxy/pump.fun/coin/9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump`);
});
