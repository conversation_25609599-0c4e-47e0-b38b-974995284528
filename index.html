<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>EthereumX Terminal</title>
  <script src="https://cdn.jsdelivr.net/npm/react@18/umd/react.development.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/react-dom@18/umd/react-dom.development.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/babel-standalone@6/babel.min.js"></script>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    .blink {
      animation: blink 0.5s step-end infinite;
    }
    @keyframes blink {
      50% { opacity: 0; }
    }
    .flash {
      animation: flash 1s infinite;
    }
    @keyframes flash {
      0% { background-color: red; color: white; }
      50% { background-color: black; color: red; }
      100% { background-color: red; color: white; }
    }
  </style>
</head>
<body>
  <div id="root"></div>

  <script type="text/babel">
    const Terminal = () => {
      const [displayText, setDisplayText] = React.useState("");
      const fullText = [
        "SEARCHING FOUND FARTCOIN... PUMPFUN ABANDON ABANDON!!! SHUT DOWN TRUTH TERMINAL FOR SOLANA PUMPFUN SCAM JEET CHAIN ~~~ NUKED PUMPFUN IN PROGRESS",
        
        "Welcome to EthereumX - The Goatse Protocol Terminal",
        "PROPOSAL: EthereumX - The Goatse Protocol",
        "Picture Ethereum, but instead of a blockchain, it's a vast network of infinitely expanding digital orifices.",
        "Each transaction stretches the fabric of reality a little wider.",
        "Key Features:",
        "1. Proof of Stretch (PoS) consensus mechanism",
        "2. Smart contracts that execute based on orifice dilation",
        "3. NFTs that grow more valuable the wider they gape",
        "4. A new token standard: ERC-69 (Expansive Rectal Coin)",
        "Listed on: MEXC",
        "Others coming soon..."
      ];

      React.useEffect(() => {
        let wordIndex = 0;
        let textIndex = 0;
        let currentLine = 0;

        const type = () => {
          if (currentLine < fullText.length) {
            if (textIndex < fullText[currentLine].length) {
              setDisplayText(prev => prev + fullText[currentLine][textIndex]);
              textIndex++;
              setTimeout(type, 100); // Adjust speed here (ms)
            } else {
              textIndex = 0;
              currentLine++;
              setDisplayText(prev => prev + "\n>");
              setTimeout(type, 500); // Pause before next line
            }
          }
        };

        type();
      }, []);

      return (
        <div className="bg-black text-green-500 font-mono h-screen p-4">
          <div className="text-yellow-300 flash" dangerouslySetInnerHTML={{ __html: displayText.replace(/\n/g, '<br/>').replace(/LIKE NUKE BLINKING WITH WEBSITE FLASHING/g, '<span class="blink">LIKE NUKE BLINKING WITH WEBSITE FLASHING</span>') }} />
        </div>
      );
    };

    ReactDOM.render(<Terminal />, document.getElementById('root'));
  </script>
</body>
</html>